# NxtAcre Farm Management Platform - Project Guidelines

## Project Overview
NxtAcre is a comprehensive farm management platform designed to support modern agricultural operations. The platform consists of:
- A web application built with React, TypeScript, and Vite
- A mobile application built with React Native/Expo
- A backend server built with Express.js

The platform provides features for farm management, including:
- User management and authentication
- Field management with interactive mapping
- Equipment and asset management
- Inventory management
- Task management
- Financial management
- Weather integration
- Reporting and analytics
- Transport management (drivers, deliveries, pickups, scheduling, location tracking)
- Receipt management (upload, approval, tracking)

## Project Structure
```
qbooks/
├── .junie/             # Junie AI assistant guidelines
├── arduino/            # Arduino code for IoT devices
├── mobile/             # Mobile application (React Native/Expo)
├── webapp/             # Web application
│   ├── public/         # Static assets for the web application
│   ├── server/         # Backend Express.js server
│   │   ├── controllers/  # API controllers
│   │   ├── db/           # Database scripts and migrations
│   │   ├── middleware/   # Express middleware
│   │   ├── models/       # Data models
│   │   ├── routes/       # API routes
│   │   ├── scripts/      # Utility scripts
│   │   ├── templates/    # Email and other templates
│   │   └── utils/        # Utility functions
│   ├── src/            # Web application source code
│   │   ├── assets/     # Images, fonts, and other static assets
│   │   ├── components/ # Reusable React components
│   │   ├── context/    # React context providers
│   │   ├── hooks/      # Custom React hooks
│   │   ├── pages/      # Page components for different routes
│   │   │   ├── Transport/  # Transport management pages
│   │   │   └── Receipts/   # Receipt management pages
│   │   ├── services/   # API and service integrations
│   │   ├── types/      # TypeScript type definitions
│   │   ├── utils/      # Utility functions
│   │   ├── App.tsx     # Main application component
│   │   └── main.tsx    # Application entry point
│   ├── .env            # Environment variables
│   ├── .env.example    # Example environment variables
│   ├── features.md     # Feature specification document
│   ├── mobileappfeatures.md # Mobile app feature specification
│   ├── package.json    # Project dependencies and scripts
│   ├── tailwind.config.js # Tailwind CSS configuration
│   ├── tsconfig.json   # TypeScript configuration
│   └── vite.config.ts  # Vite configuration
```

## Development Environment Setup

### Prerequisites
- Node.js (v16 or higher)
- npm (v7 or higher)
- PostgreSQL database

### Installation
1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Copy `.env.example` to `.env` and configure environment variables
4. Start the development server:
   ```
   npm run start
   ```

## Running the Project
The project includes several npm scripts:

- `npm run dev`: Start the Vite development server for the web application
- `npm run server`: Start the Express.js backend server
- `npm run dev:server`: Start the backend server with nodemon for auto-reloading
- `npm run start`: Start both the web application and backend server concurrently
- `npm run build`: Build the web application for production
- `npm run lint`: Run ESLint to check for code quality issues
- `npm run preview`: Preview the production build locally

## Testing
When implementing changes, Junie should:

1. Verify that the changes meet the requirements specified in the issue description
2. Ensure that the code follows the existing patterns and conventions
3. Check for any potential edge cases or error scenarios
4. Verify that the UI components render correctly and are responsive

While there are no automated tests in the project, Junie should manually test the functionality by:
1. Running the application locally
2. Navigating to the relevant pages
3. Verifying that the implemented features work as expected

## Building the Project
Before submitting changes, Junie should build the project to ensure there are no compilation errors:

```
npm run build
```

## Code Style Guidelines
The project uses:
- TypeScript for type safety
- ESLint for code quality
- Tailwind CSS for styling

When making changes, Junie should:
1. Follow the existing code style and patterns
2. Use TypeScript types appropriately
3. Use functional components with hooks for React components
4. Use Tailwind CSS classes for styling
5. Keep components modular and reusable
6. Use context for global state management
7. Follow the existing folder structure

## Integration Guidelines
- Google Maps API is used for mapping features
- Weather API is used for weather data
- Stripe API is used for financial data integration

When working with these integrations, ensure that API keys are properly handled and not exposed in the client-side code.

## Mobile Application
The mobile application is built with React Native using Expo. When making changes that affect both web and mobile, ensure that the changes are compatible with both platforms or implement platform-specific solutions as needed.

## Deployment
The project is not currently set up for automated deployment. Changes should be submitted as pull requests for review.

## Changes
1. Any features added to the backend server should also be implemented in the frontend web app.
2. If a db schema change is needed, make sure to add a new migration file in the `webapp/server/db/migrations` directory.
3. Schema migration files should never be modified, and instead, if changes are needed, they should be placed in a new migration file.
4. Do not create any new tables directly in the database. Instead, create a new migration file and run it.
5. Do not modify any existing tables directly in the database. Instead, create a new migration file and run it.
6. Do not modify any existing columns directly in the database. Instead, create a new migration file and run it.
7. Do not delete any existing tables, columns, or rows directly in the database. Instead, create a new migration file and run it.
8. Do not create migration scripts unless specifically needed for something a schema migration file can't solve.
9. When creating new schema migration files, include the following comment structure at the top of the file:
   ```sql
   -- Migration: Brief description of what this migration does
   -- Depends on: file1.sql, file2.sql (if this migration depends on other migration files)
   ```
10. When creating new migration files, make sure to include 'SET search_path TO site;' at the top of the file after the comments.

   The "Migration:" comment is required and should describe the purpose of the migration.
   The "Depends on:" comment is optional and should list any migration files that this migration depends on, separated by commas. Validate the dependencies exist before requiring them.
   For more details on the dependency system, refer to `webapp/server/db/migrations/README_dependency_system.md`.
