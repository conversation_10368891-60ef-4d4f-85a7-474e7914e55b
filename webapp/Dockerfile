FROM node:20-alpine

# Define ARG variables
ARG VITE_MAIN_DOMAIN
ARG VITE_API_URL
ARG API_URL
ARG SPACES_ENDPOINT
ARG SPACES_REGION
ARG SPACES_NAME
ARG SPACES_KEY
ARG SPACES_SECRET
ARG SENTRY_AUTH_TOKEN
ARG SENTRY_ORG
ARG SENTRY_PROJECT
ARG SENTRY_DSN
ARG VITE_SENTRY_DSN

# Set ENV variables from ARG variables
ENV VITE_MAIN_DOMAIN=$VITE_MAIN_DOMAIN
ENV VITE_API_URL=$VITE_API_URL
ENV API_URL=$API_URL
ENV SPACES_ENDPOINT=$SPACES_ENDPOINT
ENV SPACES_REGION=$SPACES_REGION
ENV SPACES_NAME=$SPACES_NAME
ENV SPACES_KEY=$SPACES_KEY
ENV SPACES_SECRET=$SPACES_SECRET
ENV SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN
ENV SENTRY_ORG=$SENTRY_ORG
ENV SENTRY_PROJECT=$SENTRY_PROJECT
ENV SENTRY_DSN=$SENTRY_DSN
ENV VITE_SENTRY_DSN=$VITE_SENTRY_DSN

WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies including devDependencies for build
RUN npm ci --include=dev

# Copy the rest of the application
COPY . .

#setup matrix
RUN mkdir /data && cp synapse-data/admin_token.env /data/admin_token.env

# Create test data directory and setup test data
RUN mkdir -p test/data && npm run setup-test-data

# Build the frontend
RUN npm run --max_old_space_size=4096 build

# Expose the port the app runs on
EXPOSE 3002

# Command to run the application
CMD ["npm", "run", "start:prod"]
