-- Create the matrix schema for Matrix Synapse
CREATE SCHEMA IF NOT EXISTS matrix;

-- Grant privileges to the postgres user
GRANT ALL PRIVILEGES ON SCHEMA matrix TO postgres;

-- Set the search path for the postgres user
ALTER ROLE postgres SET search_path TO site, matrix, public;

-- Create a function to create the matrix schema in any new database
CREATE OR REPLACE FUNCTION create_matrix_schema()
RETURNS VOID AS $$
BEGIN
    -- Create the matrix schema
    EXECUTE 'CREATE SCHEMA IF NOT EXISTS matrix';
    
    -- Grant privileges to the postgres user
    EXECUTE 'GRANT ALL PRIVILEGES ON SCHEMA matrix TO postgres';
END;
$$ LANGUAGE plpgsql;