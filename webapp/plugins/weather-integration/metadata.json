{"name": "Weather Integration", "description": "Provides weather data for farm fields based on their location", "version": "1.0.0", "author": "NxtAcre", "entry_point": "index.js", "icon": "/plugins/weather-integration/icon.png", "is_global": false, "default_settings": {"api_key": "", "update_frequency": "daily", "temperature_unit": "celsius", "notifications": {"enabled": true, "extreme_weather": true, "daily_forecast": false}}, "settings_schema": {"api_key": {"type": "string", "label": "API Key", "description": "Your weather service API key", "required": true}, "update_frequency": {"type": "select", "label": "Update Frequency", "description": "How often to update weather data", "options": [{"value": "hourly", "label": "Hourly"}, {"value": "daily", "label": "Daily"}, {"value": "weekly", "label": "Weekly"}], "default": "daily"}, "temperature_unit": {"type": "select", "label": "Temperature Unit", "description": "Unit for temperature display", "options": [{"value": "celsius", "label": "<PERSON><PERSON><PERSON>"}, {"value": "fahrenheit", "label": "Fahrenheit"}], "default": "celsius"}, "notifications": {"type": "object", "label": "Notifications", "properties": {"enabled": {"type": "boolean", "label": "Enable Notifications", "default": true}, "extreme_weather": {"type": "boolean", "label": "Extreme Weather Alerts", "default": true}, "daily_forecast": {"type": "boolean", "label": "Daily Forecast", "default": false}}}}}