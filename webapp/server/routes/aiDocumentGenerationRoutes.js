import express from 'express';
import { body } from 'express-validator';
import { generateDocument, saveAsTemplate } from '../controllers/aiDocumentGenerationController.js';
import { authenticateToken } from '../middleware/authMiddleware.js';

const router = express.Router();

/**
 * @route POST /api/ai-document-generation/generate
 * @desc Generate a document using AI based on user prompt
 * @access Private
 */
router.post(
  '/generate',
  authenticateToken,
  [
    body('prompt').notEmpty().withMessage('Prompt is required'),
    body('farmId').notEmpty().withMessage('Farm ID is required'),
    body('documentType').optional()
  ],
  generateDocument
);

/**
 * @route POST /api/ai-document-generation/save-as-template
 * @desc Save an AI-generated document as a template
 * @access Private
 */
router.post(
  '/save-as-template',
  authenticateToken,
  [
    body('documentId').notEmpty().withMessage('Document ID is required'),
    body('farmId').notEmpty().withMessage('Farm ID is required'),
    body('title').optional(),
    body('description').optional()
  ],
  saveAsTemplate
);

export default router;
