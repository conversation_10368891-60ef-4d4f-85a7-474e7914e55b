import express from 'express';
import { authenticate } from '../middleware/authMiddleware.js';
import * as orderController from '../controllers/orderController.js';

const router = express.Router();

// Apply authentication middleware to all order routes
router.use(authenticate);

// Order Routes
router.get('/', orderController.getOrders);
router.get('/:id', orderController.getOrder);
router.post('/', orderController.createOrder);
router.put('/:id', orderController.updateOrder);
router.delete('/:id', orderController.deleteOrder);

// Special order operations
router.patch('/:id/status', orderController.updateOrderStatus);
router.post('/:id/receive', orderController.receiveOrder);

export default router;