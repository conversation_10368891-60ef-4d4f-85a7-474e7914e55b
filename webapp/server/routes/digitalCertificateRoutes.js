import express from 'express';
import { 
  getAllDigitalCertificates, 
  getDigitalCertificateById, 
  createNewDigitalCertificate, 
  updateDigitalCertificate, 
  deleteDigitalCertificate,
  getUserCertificates
} from '../controllers/digitalCertificateController.js';
import { authenticateJWT } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateJWT);

// Get all digital certificates for the current user
router.get('/user', getUserCertificates);

// Farm-specific routes
router.get('/farm/:farmId', getAllDigitalCertificates);
router.post('/farm/:farmId', createNewDigitalCertificate);

// Certificate-specific routes
router.get('/:id', getDigitalCertificateById);
router.put('/:id', updateDigitalCertificate);
router.delete('/:id', deleteDigitalCertificate);

export default router;