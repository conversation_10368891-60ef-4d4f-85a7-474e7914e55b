import express from 'express';
import {
  getEquipmentIsobusData,
  getLatestIsobusData,
  createIsobusData,
  receiveExternalIsobusData,
  getEquipmentIsobusImplements
} from '../controllers/isobusController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Get ISOBUS data for a specific equipment item
router.get('/equipment/:equipmentId', authenticate, getEquipmentIsobusData);

// Get the latest ISOBUS data for an equipment item
router.get('/equipment/:equipmentId/latest', authenticate, getLatestIsobusData);

// Get ISOBUS implements for a specific equipment item
router.get('/equipment/:equipmentId/implements', authenticate, getEquipmentIsobusImplements);

// Create new ISOBUS data record
router.post('/', authenticate, createIsobusData);

// Receive ISOBUS data from external systems
// This endpoint is typically not authenticated with the same mechanism
// as it's used by external systems
router.post('/external', receiveExternalIsobusData);

export default router;
