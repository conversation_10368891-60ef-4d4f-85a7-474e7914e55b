import express from 'express';
import { 
  getSupportedSystems,
  getMigrationSystem,
  getMigrationJobs,
  getMigrationJob,
  getMigrationResults,
  createMigrationJob,
  uploadMigrationFile,
  startMigrationJob,
  cancelMigrationJob,
  exportData,
  getMigrationTemplates,
  upload
} from '../controllers/migrationController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// Get all supported migration systems
router.get('/systems', getSupportedSystems);

// Get migration system by ID
router.get('/systems/:systemId', getMigrationSystem);

// Get migration templates for a specific system
router.get('/systems/:systemId/templates', getMigrationTemplates);

// Get migration jobs for a farm
router.get('/jobs', getMigrationJobs);

// Get migration job by ID
router.get('/jobs/:jobId', getMigrationJob);

// Get migration job results
router.get('/jobs/:jobId/results', getMigrationResults);

// Create a new migration job
router.post('/jobs', createMigrationJob);

// Upload migration data file
router.post('/jobs/:jobId/upload', upload.single('file'), uploadMigrationFile);

// Start migration job processing
router.post('/jobs/:jobId/start', startMigrationJob);

// Cancel migration job
router.post('/jobs/:jobId/cancel', cancelMigrationJob);

// Export data to a file for migration to another system
router.post('/export', exportData);

export default router;