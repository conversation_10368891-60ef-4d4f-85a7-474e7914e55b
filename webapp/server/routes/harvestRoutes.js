import express from 'express';
import { 
  getFarmHarvests, 
  getFieldHarvests, 
  getCropHarvests, 
  getHarvestById, 
  createHarvest, 
  updateHarvest, 
  deleteHarvest,
  getHarvestsByStatus
} from '../controllers/harvestController.js';
import { authenticate, authorize } from '../middleware/authMiddleware.js';

const router = express.Router();

// Get all harvests for a farm
router.get('/farm/:farmId', 
  authenticate, 
  authorize([], 'harvests', 'view'),
  getFarmHarvests
);

// Get harvests for a specific field
router.get('/field/:fieldId', 
  authenticate, 
  authorize([], 'harvests', 'view'),
  getFieldHarvests
);

// Get harvests for a specific crop
router.get('/crop/:cropId', 
  authenticate, 
  authorize([], 'harvests', 'view'),
  getCropHarvests
);

// Get harvests by status for a farm
router.get('/status/:status/farm/:farmId', 
  authenticate, 
  authorize([], 'harvests', 'view'),
  getHarvestsByStatus
);

// Get a single harvest by ID
router.get('/:harvestId', 
  authenticate, 
  authorize([], 'harvests', 'view'),
  getHarvestById
);

// Create a new harvest
router.post('/', 
  authenticate, 
  authorize([], 'harvests', 'create'),
  createHarvest
);

// Update a harvest
router.put('/:harvestId', 
  authenticate,
  authorize([], 'harvests', 'edit'),
  updateHarvest
);

// Delete a harvest
router.delete('/:harvestId', 
  authenticate,
  authorize([], 'harvests', 'delete'),
  deleteHarvest
);

export default router;
