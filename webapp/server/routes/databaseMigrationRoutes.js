import express from 'express';
import { 
  getAllMigrations,
  getMigration,
  applyMigration,
  skipMigration,
  scanForMigrations,
  checkMigrationStatus,
  retrySkippedMigration
} from '../controllers/databaseMigrationController.js';
import { authenticate, isGlobalAdmin, authenticateWithToken } from '../middleware/authMiddleware.js';

const router = express.Router();

// Custom middleware to allow either token-based or JWT-based authentication
const allowTokenOrJwtAuth = (req, res, next) => {
  // Check if the migration token is present in headers
  if (req.headers['x-migration-token']) {
    return authenticateWithToken(req, res, next);
  }

  // Otherwise, use standard authentication
  authenticate(req, res, (err) => {
    if (err) return res.status(401).json({ error: 'Authentication failed' });
    isGlobalAdmin(req, res, next);
  });
};

// Apply the custom authentication middleware to all routes
router.use(allowTokenOrJwtAuth);

// Get all database migrations
router.get('/', getAllMigrations);

// Get a specific database migration by ID
router.get('/:migrationId', getMigration);

// Apply a database migration
router.post('/:migrationId/apply', applyMigration);

// Skip a database migration
router.post('/:migrationId/skip', skipMigration);

// Retry a skipped migration
router.post('/:migrationId/retry', retrySkippedMigration);

// Scan for new migrations in the db directory
router.post('/scan', scanForMigrations);

// Check if all migrations have been applied
router.get('/status/check', checkMigrationStatus);

export default router;
