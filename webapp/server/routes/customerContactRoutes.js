import express from 'express';
import {
  contactFarm,
  getFarmContactInfo
} from '../controllers/customerContactController.js';
import { authenticateCustomer } from '../middleware/customerAuthMiddleware.js';

const router = express.Router();

// All routes require customer authentication
router.use(authenticateCustomer);

// Get farm contact information
router.get('/info', getFarmContactInfo);

// Send a contact message to the farm
router.post('/message', contactFarm);

export default router;