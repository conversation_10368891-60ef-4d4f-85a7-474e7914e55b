import express from 'express';
import {
  getUsers,
  getUserById,
  updateUser,
  deleteUser,
  approveBusinessUser,
  getPendingApprovalUsers,
  updateCurrentUser,
  getCurrentUser
} from '../controllers/userController.js';
import { getUserFarmById } from '../controllers/userFarmController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Public routes (none for users API - all routes should be protected)

// Protected routes (require authentication)
router.get('/me', authenticate, getCurrentUser);
router.patch('/me', authenticate, updateCurrentUser);
router.get('/', authenticate, getUsers);
router.get('/pending-approval', authenticate, getPendingApprovalUsers);
router.get('/:userId', authenticate, getUserById);
router.get('/:userId/details', authenticate, getUserById);
router.put('/:userId', authenticate, updateUser);
router.delete('/:userId', authenticate, deleteUser);
router.post('/:userId/approve', authenticate, approveBusinessUser);

// Get a specific UserFarm by ID
router.get('/farm/:id', authenticate, getUserFarmById);

export default router;
