import express from 'express';
import {
  getYieldPredictions,
  getYieldPredictionById,
  createYieldPrediction,
  generateYieldPrediction,
  deleteYieldPrediction
} from '../controllers/yieldPredictionController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// Get all yield predictions for a farm
router.get('/', getYieldPredictions);

// Get a single yield prediction by ID
router.get('/:predictionId', getYieldPredictionById);

// Create a new yield prediction
router.post('/', createYieldPrediction);

// Generate a yield prediction using AI
router.post('/generate', generateYieldPrediction);

// Delete a yield prediction
router.delete('/:predictionId', deleteYieldPrediction);

export default router;