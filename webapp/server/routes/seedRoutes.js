import express from 'express';
import {
  getFarmSeedProducts,
  getSeedProductById,
  createSeedProduct,
  updateSeedProduct,
  deleteSeedProduct
} from '../controllers/seedProductController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all seed routes
router.use(authenticate);

// Get all seed products for a farm
router.get('/farm/:farmId', getFarmSeedProducts);

// Get a single seed product by ID
router.get('/:seedProductId', getSeedProductById);

// Create a new seed product
router.post('/', createSeedProduct);

// Update a seed product
router.put('/:seedProductId', updateSeedProduct);

// Delete a seed product
router.delete('/:seedProductId', deleteSeedProduct);

export default router;