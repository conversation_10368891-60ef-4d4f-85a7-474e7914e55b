import express from 'express';
import {
  checkSyncStatus,
  syncFarm,
  syncAllFarms
} from '../controllers/stripeDebuggerController.js';
import { authenticate, isGlobalAdmin } from '../middleware/authMiddleware.js';

const router = express.Router();

// All routes require authentication and global admin privileges
router.use(authenticate);
router.use(isGlobalAdmin);

// Get sync status for all farms
router.get('/sync-status', checkSyncStatus);

// Sync a specific farm with Stripe
router.post('/sync/:farmId', syncFarm);

// Sync all farms with Stripe
router.post('/sync-all', syncAllFarms);

export default router;