import express from 'express';
import { processTicketEmailReply } from '../controllers/emailWebhookController.js';
import { processEmailReceipt } from '../controllers/receiptController.js';
import { processSmsReceipt } from '../controllers/smsReceiptController.js';
import { processIncomingEmail } from '../controllers/genericEmailWebhookController.js';

// Create a router for webhook endpoints that don't require authentication
const router = express.Router();

// POST /api/webhooks/email - Generic catchall email endpoint
router.post('/email', processIncomingEmail);

// Legacy endpoints - kept for backward compatibility
// POST /api/webhooks/email/ticket - Process an email reply to a support ticket
router.post('/email/ticket', processTicketEmailReply);

// POST /api/webhooks/receipts/email - Process an email receipt
router.post('/receipts/email', processEmailReceipt);

// POST /api/webhooks/receipts/sms - Process an SMS receipt
router.post('/receipts/sms', processSmsReceipt);

export default router;
