import express from 'express';
import {
  createDriverLocation,
  getDriverLocations,
  getLatestDriverLocation,
  getDriverLocationHistory,
  deleteDriverLocation,
  deleteDriverLocationHistory
} from '../controllers/driverLocationController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// All routes require authentication
router.post('/', authenticate, createDriverLocation);
router.get('/', authenticate, getDriverLocations);
router.get('/driver/:driverId/latest', authenticate, getLatestDriverLocation);
router.get('/driver/:driverId/history', authenticate, getDriverLocationHistory);
router.delete('/:locationId', authenticate, deleteDriverLocation);
router.delete('/driver/:driverId/history', authenticate, deleteDriverLocationHistory);

export default router;