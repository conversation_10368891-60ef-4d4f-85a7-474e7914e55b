import express from 'express';
import { getAllFarms, getFarmById, createFarm, updateFarm, deleteFarm } from '../controllers/farmController.js';
import { getUsers, getUserById, createUser, updateUser, deleteUser } from '../controllers/userController.js';
import { getStats } from '../controllers/dashboardController.js';
import { getAllSuppliers, getSupplier, createSupplier, updateSupplier, deleteSupplier } from '../controllers/supplierController.js';
import { 
  getAllSubscriptionPlans, 
  getSubscriptionPlanById, 
  createSubscriptionPlan, 
  updateSubscriptionPlan, 
  deleteSubscriptionPlan 
} from '../controllers/subscriptionController.js';
import { 
  getAllVets, 
  getGlobalVets, 
  getVetById, 
  createVet, 
  updateVet, 
  deleteVet 
} from '../controllers/vetController.js';
import {
  getSupportTickets,
  getSupportTicketById,
  updateSupportTicket,
  deleteSupportTicket,
  addComment,
  deleteComment
} from '../controllers/supportTicketController.js';
import {
  getAllDocuments,
  getDocumentById,
  uploadDocument,
  downloadDocument,
  updateDocument,
  deleteDocument,
  searchDocuments
} from '../controllers/documentController.js';
import {
  getAllApiProviders,
  getApiProviderById,
  createApiProvider,
  updateApiProvider,
  toggleApiProviderStatus,
  getAllApiEndpoints,
  getApiEndpointById,
  createApiEndpoint,
  updateApiEndpoint,
  toggleApiEndpointStatus,
  getAllApiCache,
  getApiCacheById,
  deleteApiCache,
  cleanupCache,
  getApiAnalytics,
  getRecentApiRequests
} from '../controllers/apiDataController.js';
import matrixAdminController from '../controllers/matrixAdminController.js';
import adminCertificateController from '../controllers/adminCertificateController.js';
import { authenticate, isGlobalAdmin } from '../middleware/authMiddleware.js';
import { setFarmSchema } from '../middleware/farmMiddleware.js';

const router = express.Router();

// Farm routes
router.get('/farms', authenticate, isGlobalAdmin, getAllFarms);
router.get('/farms/:farmId', authenticate, isGlobalAdmin, getFarmById);
router.post('/farms', authenticate, isGlobalAdmin, createFarm);
router.put('/farms/:farmId', authenticate, isGlobalAdmin, updateFarm);
router.delete('/farms/:farmId', authenticate, isGlobalAdmin, deleteFarm);

// User routes
router.get('/users', authenticate, isGlobalAdmin, getUsers);
router.get('/users/:userId', authenticate, isGlobalAdmin, getUserById);
router.post('/users', authenticate, isGlobalAdmin, createUser);
router.put('/users/:userId', authenticate, isGlobalAdmin, updateUser);
router.delete('/users/:userId', authenticate, isGlobalAdmin, deleteUser);

// Dashboard routes
// Note: Order matters - put specific routes before parameterized routes
router.get('/dashboard/stats', authenticate, isGlobalAdmin, setFarmSchema, getStats);

// Supplier routes
router.get('/suppliers', authenticate, isGlobalAdmin, getAllSuppliers);
router.get('/suppliers/:supplierId', authenticate, isGlobalAdmin, getSupplier);
router.post('/suppliers', authenticate, isGlobalAdmin, createSupplier);
router.put('/suppliers/:supplierId', authenticate, isGlobalAdmin, updateSupplier);
router.delete('/suppliers/:supplierId', authenticate, isGlobalAdmin, deleteSupplier);

// Subscription routes
router.get('/subscription-plans', authenticate, isGlobalAdmin, getAllSubscriptionPlans);
router.get('/subscription-plans/:planId', authenticate, isGlobalAdmin, getSubscriptionPlanById);
router.post('/subscription-plans', authenticate, isGlobalAdmin, createSubscriptionPlan);
router.put('/subscription-plans/:planId', authenticate, isGlobalAdmin, updateSubscriptionPlan);
router.delete('/subscription-plans/:planId', authenticate, isGlobalAdmin, deleteSubscriptionPlan);

// Vet routes
router.get('/vets', authenticate, isGlobalAdmin, getAllVets);
router.get('/vets/global', authenticate, isGlobalAdmin, getGlobalVets);
router.get('/vets/:vetId', authenticate, isGlobalAdmin, getVetById);
router.post('/vets', authenticate, isGlobalAdmin, createVet);
router.put('/vets/:vetId', authenticate, isGlobalAdmin, updateVet);
router.delete('/vets/:vetId', authenticate, isGlobalAdmin, deleteVet);

// Support ticket routes
router.get('/support-tickets', authenticate, isGlobalAdmin, getSupportTickets);
router.get('/support-tickets/:ticketId', authenticate, isGlobalAdmin, getSupportTicketById);
router.put('/support-tickets/:ticketId', authenticate, isGlobalAdmin, updateSupportTicket);
router.delete('/support-tickets/:ticketId', authenticate, isGlobalAdmin, deleteSupportTicket);
router.post('/support-tickets/:ticketId/comments', authenticate, isGlobalAdmin, addComment);
router.delete('/support-tickets/comments/:commentId', authenticate, isGlobalAdmin, deleteComment);

// API data routes
router.get('/api-providers', authenticate, isGlobalAdmin, getAllApiProviders);
router.get('/api-providers/:providerId', authenticate, isGlobalAdmin, getApiProviderById);
router.post('/api-providers', authenticate, isGlobalAdmin, createApiProvider);
router.put('/api-providers/:providerId', authenticate, isGlobalAdmin, updateApiProvider);
router.put('/api-providers/:providerId/status', authenticate, isGlobalAdmin, toggleApiProviderStatus);
router.get('/api-endpoints', authenticate, isGlobalAdmin, getAllApiEndpoints);
router.get('/api-endpoints/:endpointId', authenticate, isGlobalAdmin, getApiEndpointById);
router.post('/api-endpoints', authenticate, isGlobalAdmin, createApiEndpoint);
router.put('/api-endpoints/:endpointId', authenticate, isGlobalAdmin, updateApiEndpoint);
router.put('/api-endpoints/:endpointId/status', authenticate, isGlobalAdmin, toggleApiEndpointStatus);
router.get('/api-cache', authenticate, isGlobalAdmin, getAllApiCache);
router.get('/api-cache/:cacheId', authenticate, isGlobalAdmin, getApiCacheById);
router.delete('/api-cache/:cacheId', authenticate, isGlobalAdmin, deleteApiCache);
router.post('/api-cache/cleanup', authenticate, isGlobalAdmin, cleanupCache);
router.get('/api-analytics', authenticate, isGlobalAdmin, getApiAnalytics);
router.get('/api-requests/recent', authenticate, isGlobalAdmin, getRecentApiRequests);

// Matrix chat administration routes
router.get('/matrix/users', authenticate, isGlobalAdmin, matrixAdminController.getUsers);
router.get('/matrix/users/:userId', authenticate, isGlobalAdmin, matrixAdminController.getUserById);
router.post('/matrix/users', authenticate, isGlobalAdmin, matrixAdminController.createUser);
router.put('/matrix/users/:userId', authenticate, isGlobalAdmin, matrixAdminController.updateUser);
router.delete('/matrix/users/:userId', authenticate, isGlobalAdmin, matrixAdminController.deleteUser);
router.post('/matrix/sync-users', authenticate, isGlobalAdmin, matrixAdminController.syncUsers);

router.get('/matrix/rooms', authenticate, isGlobalAdmin, matrixAdminController.getRooms);
router.get('/matrix/rooms/:roomId', authenticate, isGlobalAdmin, matrixAdminController.getRoomById);
router.delete('/matrix/rooms/:roomId', authenticate, isGlobalAdmin, matrixAdminController.deleteRoom);
router.get('/matrix/rooms/:roomId/members', authenticate, isGlobalAdmin, matrixAdminController.getRoomMembers);
router.post('/matrix/rooms/:roomId/purge-history', authenticate, isGlobalAdmin, matrixAdminController.purgeRoomHistory);

router.get('/matrix/stats', authenticate, isGlobalAdmin, matrixAdminController.getServerStats);
router.get('/matrix/version', authenticate, isGlobalAdmin, matrixAdminController.getServerVersion);

router.get('/matrix/users/:userId/media', authenticate, isGlobalAdmin, matrixAdminController.getUserMedia);
router.delete('/matrix/users/:userId/media', authenticate, isGlobalAdmin, matrixAdminController.deleteUserMedia);

// Digital certificate and document verification routes
router.get('/digital-certificates', authenticate, isGlobalAdmin, adminCertificateController.getAllCertificates);
router.post('/digital-certificates', authenticate, isGlobalAdmin, adminCertificateController.createCertificate);
router.post('/digital-certificates/upload', authenticate, isGlobalAdmin, adminCertificateController.uploadCertificate);
router.delete('/digital-certificates/:certificateId', authenticate, isGlobalAdmin, adminCertificateController.deleteCertificate);
router.get('/signable-documents/with-digital-signatures', authenticate, isGlobalAdmin, adminCertificateController.getDocumentsWithDigitalSignatures);
router.get('/signable-documents/:documentId/verify-signature', authenticate, isGlobalAdmin, adminCertificateController.verifyDocumentSignature);
router.get('/certificate-stats', authenticate, isGlobalAdmin, adminCertificateController.getCertificateStats);

export default router;
