import express from 'express';
import { authenticate, isApprovedBusinessOwner } from '../middleware/authMiddleware.js';
import * as supplierController from '../controllers/supplierController.js';

const router = express.Router();

// Apply authentication middleware to all supplier routes
router.use(authenticate);

// Basic Supplier Routes
router.get('/', supplierController.getSuppliers);
router.get('/:id', supplierController.getSupplier);
router.post('/', supplierController.createSupplier);
router.put('/:id', isApprovedBusinessOwner, supplierController.updateSupplier);
router.delete('/:id', isApprovedBusinessOwner, supplierController.deleteSupplier);

// Supplier Product Routes
router.get('/:supplierId/products', supplierController.getSupplierProducts);
router.get('/products/:id', supplierController.getSupplierProduct);
router.post('/products', isApprovedBusinessOwner, supplierController.createSupplierProduct);
router.put('/products/:id', isApprovedBusinessOwner, supplierController.updateSupplierProduct);
router.delete('/products/:id', isApprovedBusinessOwner, supplierController.deleteSupplierProduct);

// Supplier Review Routes
router.get('/:supplierId/reviews', supplierController.getSupplierReviews);
router.get('/reviews/:id', supplierController.getSupplierReview);
router.post('/reviews', supplierController.createSupplierReview);
router.put('/reviews/:id', isApprovedBusinessOwner, supplierController.updateSupplierReview);
router.delete('/reviews/:id', isApprovedBusinessOwner, supplierController.deleteSupplierReview);

// Supplier API Integration Routes
router.get('/:supplierId/api/status', supplierController.getSupplierApiStatus);
router.post('/:supplierId/api/sync-products', isApprovedBusinessOwner, supplierController.syncSupplierProducts);
router.get('/:supplierId/api/orders/:orderId', supplierController.checkOrderStatus);

export default router;
