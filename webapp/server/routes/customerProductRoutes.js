import express from 'express';
import {
  getPublicProducts,
  getPublicProductById,
  requestProduct
} from '../controllers/customerProductController.js';
import { authenticateCustomer } from '../middleware/customerAuthMiddleware.js';

const router = express.Router();

// All routes require customer authentication
router.use(authenticateCustomer);

// Get all public products for the farm
router.get('/', getPublicProducts);

// Get a single public product by ID
router.get('/:productId', getPublicProductById);

// Request a product (inquiry)
router.post('/:productId/request', requestProduct);

export default router;