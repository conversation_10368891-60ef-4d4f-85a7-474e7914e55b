import express from 'express';
import {
  getTimeEntries,
  getTimeEntryById,
  createTimeEntry,
  updateTimeEntry,
  deleteTimeEntry,
  getTimeEntriesByEmployee,
  approveTimeEntry,
  getTimeEntrySummary
} from '../controllers/timeEntryController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Get all time entries with optional filtering
router.get('/', getTimeEntries);

// Get time entry summary
router.get('/summary', getTimeEntrySummary);

// Get time entries by employee ID
router.get('/employee/:employeeId', getTimeEntriesByEmployee);

// Get time entry by ID
router.get('/:timeEntryId', getTimeEntryById);

// Create a new time entry
router.post('/', createTimeEntry);

// Update a time entry
router.put('/:timeEntryId', updateTimeEntry);

// Delete a time entry
router.delete('/:timeEntryId', deleteTimeEntry);

// Approve a time entry
router.put('/:timeEntryId/approve', approveTimeEntry);

export default router;