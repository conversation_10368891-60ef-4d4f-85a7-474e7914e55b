import express from 'express';
import {
  getAllFarms,
  getFarmById,
  createFarm,
  updateFarm,
  deleteFarm,
  addFarmAdmin,
  removeFarmAdmin,
  getFarmUsers,
  checkSubdomainAvailability,
  getFarmsByUserId,
  getFarmBySubdomain,
  getPendingFarmEmployees,
  approveFarmEmployee,
  rejectFarmEmployee,
  uploadFarmLogo,
  inviteUserToFarm
} from '../controllers/farmController.js';
import { authenticate, isAdmin, isGlobalAdmin } from '../middleware/authMiddleware.js';
import { setFarmSchema, ensureFarmSchema } from '../middleware/farmMiddleware.js';

const router = express.Router();

// Get all farms (global admin only)
router.get('/', authenticate, isGlobalAdmin, getAllFarms);

// Public route for getting a farm by ID (for debugging)
router.get('/public/:farmId', (req, res) => {
  console.log(`[Public Farm Route] Accessing farm with ID: ${req.params.farmId}`);
  return getFarmById(req, res);
});

// Get a single farm by ID (global admin or farm admin)
router.get('/:farmId', authenticate, getFarmById);

// Create a new farm (global admin only)
router.post('/', authenticate, isGlobalAdmin, createFarm);

// Create a new farm during registration (no auth required)
router.post('/register', createFarm);

// Update a farm (global admin or farm admin)
router.put('/:farmId', authenticate, updateFarm);

// Delete a farm (global admin only)
router.delete('/:farmId', authenticate, isGlobalAdmin, deleteFarm);

// Add a user to a farm as admin
router.post('/:farmId/admins', authenticate, addFarmAdmin);

// Remove a user from farm admins
router.delete('/:farmId/admins/:userId', authenticate, removeFarmAdmin);

// Get all users for a farm
router.get('/:farmId/users', authenticate, setFarmSchema, ensureFarmSchema, getFarmUsers);

// Check if a subdomain is available
router.get('/subdomain/:subdomain/check', checkSubdomainAvailability);

// Get farm details by subdomain (public endpoint for login page)
router.get('/by-subdomain/:subdomain', getFarmBySubdomain);

// Get all farms for a specific user
router.get('/user/:userId', authenticate, getFarmsByUserId);

// Get all farms for the authenticated user
router.get('/user', authenticate, getFarmsByUserId);

// Get pending farm employees for approval (farm owners and admins only)
router.get('/:farmId/pending-employees', authenticate, isAdmin, getPendingFarmEmployees);

// Approve a farm employee (farm owners and admins only)
router.post('/:farmId/approve-employee/:userId', authenticate, isAdmin, approveFarmEmployee);

// Reject a farm employee (farm owners and admins only)
router.delete('/:farmId/reject-employee/:userId', authenticate, isAdmin, rejectFarmEmployee);

// Upload farm logo
router.post('/:farmId/logo', authenticate, uploadFarmLogo);

// Invite a user to a farm
router.post('/:farmId/invite', authenticate, inviteUserToFarm);

export default router;
