import express from 'express';
import { 
  getTaxSummary, 
  getTaxCategories, 
  getTaxDeductions, 
  createTaxDeduction, 
  updateTaxDeduction, 
  deleteTaxDeduction, 
  getTaxPlanningRecommendations,
  // Tax Document endpoints
  getTaxDocuments,
  getTaxDocument,
  createTaxDocument,
  updateTaxDocument,
  deleteTaxDocument,
  uploadTaxDocumentFile,
  // Employee Tax Info endpoints
  getEmployeeTaxInfo,
  getEmployeeTaxInfoById,
  createEmployeeTaxInfo,
  updateEmployeeTaxInfo,
  deleteEmployeeTaxInfo,
  generateW2,
  // Contractor Tax Info endpoints
  getContractorTaxInfo,
  getContractorTaxInfoById,
  createContractorTaxInfo,
  updateContractorTaxInfo,
  deleteContractorTaxInfo,
  generate1099,
  // Tax Payment endpoints
  getTaxPayments,
  getTaxPayment,
  createTaxPayment,
  updateTaxPayment,
  deleteTaxPayment,
  // Tax Filing endpoints
  getTaxFilings,
  getTaxFiling,
  createTaxFiling,
  updateTaxFiling,
  deleteTaxFiling,
  // Payroll Tax endpoints
  calculatePayrollTaxes,
  // Tax Forecasting endpoints
  forecastTaxLiability
} from '../controllers/taxManagementController.js';
import { authenticate } from '../middleware/index.js';
import { checkFarmAccess } from '../middleware/farmAccessMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// Get tax summary for a farm
router.get('/farms/:farmId/tax-summary', checkFarmAccess, getTaxSummary);

// Get tax categories for a farm
router.get('/farms/:farmId/tax-categories', checkFarmAccess, getTaxCategories);

// Get tax deductions for a farm
router.get('/farms/:farmId/tax-deductions', checkFarmAccess, getTaxDeductions);

// Create a new tax deduction
router.post('/farms/:farmId/tax-deductions', checkFarmAccess, createTaxDeduction);

// Update a tax deduction
router.put('/tax-deductions/:deductionId', checkFarmAccess, updateTaxDeduction);

// Delete a tax deduction
router.delete('/tax-deductions/:deductionId', checkFarmAccess, deleteTaxDeduction);

// Get tax planning recommendations for a farm
router.get('/farms/:farmId/tax-planning-recommendations', checkFarmAccess, getTaxPlanningRecommendations);

// Tax Document routes
router.get('/farms/:farmId/tax-documents', checkFarmAccess, getTaxDocuments);
router.get('/tax-documents/:documentId', checkFarmAccess, getTaxDocument);
router.post('/farms/:farmId/tax-documents', checkFarmAccess, createTaxDocument);
router.put('/tax-documents/:documentId', checkFarmAccess, updateTaxDocument);
router.delete('/tax-documents/:documentId', checkFarmAccess, deleteTaxDocument);
router.post('/tax-documents/:documentId/upload', checkFarmAccess, uploadTaxDocumentFile);

// Employee Tax Info routes
router.get('/farms/:farmId/employee-tax-info', checkFarmAccess, getEmployeeTaxInfo);
router.get('/employee-tax-info/:taxInfoId', checkFarmAccess, getEmployeeTaxInfoById);
router.post('/farms/:farmId/employees/:employeeId/tax-info', checkFarmAccess, createEmployeeTaxInfo);
router.put('/employee-tax-info/:taxInfoId', checkFarmAccess, updateEmployeeTaxInfo);
router.delete('/employee-tax-info/:taxInfoId', checkFarmAccess, deleteEmployeeTaxInfo);
router.post('/employee-tax-info/:taxInfoId/generate-w2', checkFarmAccess, generateW2);

// Contractor Tax Info routes
router.get('/farms/:farmId/contractor-tax-info', checkFarmAccess, getContractorTaxInfo);
router.get('/contractor-tax-info/:taxInfoId', checkFarmAccess, getContractorTaxInfoById);
router.post('/farms/:farmId/contractor-tax-info', checkFarmAccess, createContractorTaxInfo);
router.put('/contractor-tax-info/:taxInfoId', checkFarmAccess, updateContractorTaxInfo);
router.delete('/contractor-tax-info/:taxInfoId', checkFarmAccess, deleteContractorTaxInfo);
router.post('/contractor-tax-info/:taxInfoId/generate-1099', checkFarmAccess, generate1099);

// Tax Payment routes
router.get('/farms/:farmId/tax-payments', checkFarmAccess, getTaxPayments);
router.get('/tax-payments/:paymentId', checkFarmAccess, getTaxPayment);
router.post('/farms/:farmId/tax-payments', checkFarmAccess, createTaxPayment);
router.put('/tax-payments/:paymentId', checkFarmAccess, updateTaxPayment);
router.delete('/tax-payments/:paymentId', checkFarmAccess, deleteTaxPayment);

// Tax Filing routes
router.get('/farms/:farmId/tax-filings', checkFarmAccess, getTaxFilings);
router.get('/tax-filings/:filingId', checkFarmAccess, getTaxFiling);
router.post('/farms/:farmId/tax-filings', checkFarmAccess, createTaxFiling);
router.put('/tax-filings/:filingId', checkFarmAccess, updateTaxFiling);
router.delete('/tax-filings/:filingId', checkFarmAccess, deleteTaxFiling);

// Payroll Tax routes
router.get('/farms/:farmId/payroll-taxes', checkFarmAccess, calculatePayrollTaxes);

// Tax Forecasting routes
router.get('/farms/:farmId/tax-forecast', checkFarmAccess, forecastTaxLiability);

export default router;
