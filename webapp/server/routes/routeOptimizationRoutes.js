import express from 'express';
import { 
  getOptimizedRoute, 
  updateScheduleWithOptimizedRoute, 
  getAlternativeRoutes 
} from '../controllers/routeOptimizationController.js';
import { authenticateToken } from '../middleware/authMiddleware.js';
import { checkFarmAccess } from '../middleware/farmAccessMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get optimized route for a driver's schedule
router.get('/schedules/:scheduleId/optimized-route', checkFarmAccess, getOptimizedRoute);

// Update a driver's schedule with optimized route
router.put('/schedules/:scheduleId/optimized-route', checkFarmAccess, updateScheduleWithOptimizedRoute);

// Get alternative routes for a driver's schedule
router.get('/schedules/:scheduleId/alternative-routes', checkFarmAccess, getAlternativeRoutes);

export default router;