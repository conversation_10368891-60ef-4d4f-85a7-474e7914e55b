import express from 'express';
import {
  getFarmSoilSamples,
  getSoilSampleById,
  createSoilSample,
  updateSoilSample,
  deleteSoilSample,
  getSoilTestResults,
  getSoilTestResultById,
  createSoilTestResult,
  updateSoilTestResult,
  deleteSoilTestResult,
  previewImportSoilTestResults,
  importSoilTestResults,
  getFarmSoilAmendments,
  getSoilAmendmentById,
  createSoilAmendment,
  updateSoilAmendment,
  deleteSoilAmendment,
  getSoilRecommendations,
  getSoilRecommendationById,
  createSoilRecommendation,
  updateSoilRecommendation,
  deleteSoilRecommendation
} from '../controllers/soilController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Soil Sample Routes
// Get all soil samples for a farm
router.get('/samples/farm/:farmId', authenticate, getFarmSoilSamples);

// Get a single soil sample by ID
router.get('/samples/:sampleId', authenticate, getSoilSampleById);

// Create a new soil sample
router.post('/samples', authenticate, createSoilSample);

// Update a soil sample
router.put('/samples/:sampleId', authenticate, updateSoilSample);

// Delete a soil sample
router.delete('/samples/:sampleId', authenticate, deleteSoilSample);

// Soil Test Result Routes
// Get all test results for a soil sample
router.get('/test-results/sample/:sampleId', authenticate, getSoilTestResults);

// Get a single test result by ID
router.get('/test-results/:resultId', authenticate, getSoilTestResultById);

// Create a new test result
router.post('/test-results', authenticate, createSoilTestResult);

// Update a test result
router.put('/test-results/:resultId', authenticate, updateSoilTestResult);

// Delete a test result
router.delete('/test-results/:resultId', authenticate, deleteSoilTestResult);

// Preview soil test result import
router.post('/test-results/preview-import', authenticate, previewImportSoilTestResults);

// Import soil test results from file
router.post('/test-results/import', authenticate, importSoilTestResults);

// Soil Amendment Routes
// Get all soil amendments for a farm
router.get('/amendments/farm/:farmId', authenticate, getFarmSoilAmendments);

// Get a single soil amendment by ID
router.get('/amendments/:amendmentId', authenticate, getSoilAmendmentById);

// Create a new soil amendment
router.post('/amendments', authenticate, createSoilAmendment);

// Update a soil amendment
router.put('/amendments/:amendmentId', authenticate, updateSoilAmendment);

// Delete a soil amendment
router.delete('/amendments/:amendmentId', authenticate, deleteSoilAmendment);

// Soil Recommendation Routes
// Get all recommendations for a soil sample
router.get('/recommendations/sample/:sampleId', authenticate, getSoilRecommendations);

// Get a single recommendation by ID
router.get('/recommendations/:recommendationId', authenticate, getSoilRecommendationById);

// Create a new soil recommendation
router.post('/recommendations', authenticate, createSoilRecommendation);

// Update a soil recommendation
router.put('/recommendations/:recommendationId', authenticate, updateSoilRecommendation);

// Delete a soil recommendation
router.delete('/recommendations/:recommendationId', authenticate, deleteSoilRecommendation);

export default router;
