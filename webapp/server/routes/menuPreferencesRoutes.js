import express from 'express';
import {
  getDefaultMenuStructure,
  getUserMenuPreferences,
  saveUserMenuPreferences,
  resetUserMenuPreferences
} from '../controllers/menuPreferencesController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Public routes (none for menu preferences API - all routes should be protected)

// Protected routes (require authentication)
router.get('/defaults', authenticate, getDefaultMenuStructure);
router.get('/preferences/:userId', authenticate, getUserMenuPreferences);
router.post('/preferences', authenticate, saveUserMenuPreferences);
router.delete('/preferences/:userId', authenticate, resetUserMenuPreferences);

export default router;