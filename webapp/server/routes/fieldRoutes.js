import express from 'express';
import { authenticate } from '../middleware/index.js';
import {
  getFarmFields,
  getFieldById,
  createField,
  updateField,
  deleteField,
  updateFieldCropType,
  getFieldsByCropType,
  getFieldDataGov,
  getFarmFieldsDataGov,
  getFieldRecommendedCrops,
  getFieldConservationPractices,
  getFieldHistoricalYieldData,
  getAllFieldData,
  getFieldHarvestDirectionMaps,
  getHarvestDirectionMapById,
  createHarvestDirectionMap,
  updateHarvestDirectionMap,
  deleteHarvestDirectionMap
} from '../controllers/fieldController.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// Get all fields for a farm
router.get('/farm/:farmId', getFarmFields);

// Get all fields with a specific crop type
router.get('/farm/:farmId/crop-type/:cropType', getFieldsByCropType);

// Get a single field by ID
router.get('/:fieldId', getFieldById);

// Create a new field
router.post('/', createField);

// Update a field
router.put('/:fieldId', updateField);

// Delete a field
router.delete('/:fieldId', deleteField);

// Update crop type for a field
router.patch('/:fieldId/crop-type', updateFieldCropType);

// Data.gov integration routes
// Get field data from data.gov for a specific field
router.get('/:fieldId/data-gov', getFieldDataGov);

// Get field data from data.gov for all fields in a farm
router.get('/farm/:farmId/data-gov', getFarmFieldsDataGov);

// Get recommended crops for a field based on data.gov information
router.get('/:fieldId/recommended-crops', getFieldRecommendedCrops);

// Get conservation practices for a field based on data.gov information
router.get('/:fieldId/conservation-practices', getFieldConservationPractices);

// Get historical yield data for a field based on data.gov information
router.get('/:fieldId/historical-yield', getFieldHistoricalYieldData);

// Get all field data in a single request
router.get('/:fieldId/all-data', getAllFieldData);

// Harvest Direction Maps routes
// Get all harvest direction maps for a field
router.get('/:fieldId/harvest-direction-maps', getFieldHarvestDirectionMaps);

// Get a single harvest direction map by ID
router.get('/harvest-direction-maps/:mapId', getHarvestDirectionMapById);

// Create a new harvest direction map
router.post('/harvest-direction-maps', createHarvestDirectionMap);

// Create a new harvest direction map for a specific field
router.post('/:fieldId/harvest-direction-maps', createHarvestDirectionMap);

// Update a harvest direction map
router.put('/harvest-direction-maps/:mapId', updateHarvestDirectionMap);

// Delete a harvest direction map
router.delete('/harvest-direction-maps/:mapId', deleteHarvestDirectionMap);

export default router;
