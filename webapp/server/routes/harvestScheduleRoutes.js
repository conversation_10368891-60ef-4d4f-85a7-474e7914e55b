import express from 'express';
import {
  getAllHarvestSchedules,
  getFarmHarvestSchedules,
  getFieldHarvestSchedules,
  getHarvestScheduleById,
  getHarvestSchedulesByCrop,
  getHarvestSchedulesByStatus,
  getHarvestSchedulesByDateRange,
  createHarvestSchedule,
  updateHarvestSchedule,
  deleteHarvestSchedule,
  getHarvestRecommendations
} from '../controllers/harvestScheduleController.js';
import { authenticate } from '../middleware/index.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// Get all harvest schedules (across all farms)
router.get('/', getAllHarvestSchedules);

// Get harvest schedules by crop
router.get('/crop/:cropId', getHarvestSchedulesByCrop);

// Get harvest schedules by status
router.get('/status/:status', getHarvestSchedulesByStatus);

// Get harvest schedules by date range
router.get('/date-range', getHarvestSchedulesByDateRange);

// Get all harvest schedules for a farm
router.get('/farm/:farmId', getFarmHarvestSchedules);

// Get all harvest schedules for a field
router.get('/field/:fieldId', getFieldHarvestSchedules);

// Get a single harvest schedule by ID
router.get('/:scheduleId', getHarvestScheduleById);

// Create a new harvest schedule
router.post('/', createHarvestSchedule);

// Update a harvest schedule
router.put('/:scheduleId', updateHarvestSchedule);

// Delete a harvest schedule
router.delete('/:scheduleId', deleteHarvestSchedule);

// Get weather-based recommendations for a harvest schedule
router.get('/:scheduleId/recommendations', getHarvestRecommendations);

export default router;
