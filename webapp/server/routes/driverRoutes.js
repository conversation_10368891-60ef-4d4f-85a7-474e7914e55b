import express from 'express';
import {
  createDriver,
  getDrivers,
  getDriverById,
  updateDriver,
  deleteDriver
} from '../controllers/driverController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// All routes require authentication
router.post('/', authenticate, createDriver);
router.get('/', authenticate, getDrivers);
router.get('/:driverId', authenticate, getDriverById);
router.put('/:driverId', authenticate, updateDriver);
router.delete('/:driverId', authenticate, deleteDriver);

export default router;