import express from 'express';
import {
  register,
  registerBusiness,
  login,
  logout,
  verifyTwoFactor,
  setupTwoFactor,
  confirmTwoFactor,
  disableTwoFactor,
  forgotPassword,
  resetPassword,
  refreshToken,
  updateProfile,
  upgradeToBusinessAccount,
  sendEmailVerification,
  verifyEmail,
  verifyEmailWithCode,
  resendEmailVerification,
  checkAuth,
  sendPhoneVerificationCode,
  verifyPhoneNumber,
  setupSMS2FA,
  sendSMS2FACode,
  verifySMS2FACode,
  setupEmail2FA,
  sendEmail2FACode,
  verifyEmail2FACode,
  getAvailable2FAMethods,
  impersonateUser,
  endImpersonation,
  switchFarm
} from '../controllers/authController.js';
import { authenticate, isGlobalAdmin } from '../middleware/index.js';

const router = express.Router();

// Public routes
router.post('/register', register);
router.post('/register-business', registerBusiness);
router.post('/login', login);
router.post('/logout', logout);
router.post('/verify-2fa', verifyTwoFactor);
router.post('/forgot-password', forgotPassword);
router.post('/reset-password', resetPassword);
router.post('/refresh-token', refreshToken);
router.get('/check-auth', checkAuth);

// Protected routes (require authentication middleware)
router.get('/setup-2fa/:userId', authenticate, setupTwoFactor);
router.post('/confirm-2fa', authenticate, confirmTwoFactor);
router.post('/disable-2fa', authenticate, disableTwoFactor);
router.put('/profile/:userId', authenticate, updateProfile);
router.post('/upgrade-business/:userId', authenticate, upgradeToBusinessAccount);
router.post('/send-email-verification/:userId', authenticate, sendEmailVerification);

// Email verification routes (public)
router.get('/verify-email/:token', verifyEmail);
router.post('/verify-email-code', verifyEmailWithCode);
router.post('/resend-email-verification', resendEmailVerification);

// Phone verification routes
router.post('/send-phone-verification/:userId', authenticate, sendPhoneVerificationCode);
router.post('/verify-phone/:userId', authenticate, verifyPhoneNumber);

// SMS-based 2FA routes
router.post('/setup-sms-2fa/:userId', authenticate, setupSMS2FA);
router.post('/send-sms-2fa-code', sendSMS2FACode);
router.post('/verify-sms-2fa', verifySMS2FACode);

// Email-based 2FA routes
router.post('/setup-email-2fa/:userId', authenticate, setupEmail2FA);
router.post('/send-email-2fa-code', sendEmail2FACode);
router.post('/verify-email-2fa', verifyEmail2FACode);

// Get available 2FA methods
router.get('/available-2fa-methods/:userId', getAvailable2FAMethods);

// User impersonation routes (global admin only)
router.post('/impersonate/:userId', authenticate, isGlobalAdmin, impersonateUser);
router.post('/end-impersonation', authenticate, isGlobalAdmin, endImpersonation);

// Farm switching
router.post('/switch-farm', authenticate, switchFarm);

export default router;
