import express from 'express';
import { authenticate } from '../middleware/index.js';
import * as grantSubsidyController from '../controllers/grantSubsidyController.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// Farm Grants routes
router.get('/farms/:farmId/grants', grantSubsidyController.getGrants);
router.get('/farms/:farmId/grants/:grantId', grantSubsidyController.getGrant);
router.post('/farms/:farmId/grants', grantSubsidyController.createGrant);
router.put('/farms/:farmId/grants/:grantId', grantSubsidyController.updateGrant);
router.delete('/farms/:farmId/grants/:grantId', grantSubsidyController.deleteGrant);

// Farm Subsidies routes
router.get('/farms/:farmId/subsidies', grantSubsidyController.getSubsidies);
router.get('/farms/:farmId/subsidies/:subsidyId', grantSubsidyController.getSubsidy);
router.post('/farms/:farmId/subsidies', grantSubsidyController.createSubsidy);
router.put('/farms/:farmId/subsidies/:subsidyId', grantSubsidyController.updateSubsidy);
router.delete('/farms/:farmId/subsidies/:subsidyId', grantSubsidyController.deleteSubsidy);

export default router;