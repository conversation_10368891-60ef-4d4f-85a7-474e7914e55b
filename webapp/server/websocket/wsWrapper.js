/**
 * This is a wrapper module for the 'ws' package to ensure proper exports
 * for both CommonJS and ESM environments.
 */
import * as ws from 'ws';

// Re-export the WebSocket and WebSocketServer classes
export const WebSocket = ws.WebSocket;
export const WebSocketServer = ws.WebSocketServer;

// Export other useful constants and functions
export const CONNECTING = ws.WebSocket.CONNECTING;
export const OPEN = ws.WebSocket.OPEN;
export const CLOSING = ws.WebSocket.CLOSING;
export const CLOSED = ws.WebSocket.CLOSED;
export const createWebSocketStream = ws.createWebSocketStream;

// Default export for compatibility
export default ws.WebSocket;
