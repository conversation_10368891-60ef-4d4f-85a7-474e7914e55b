import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import dotenv from 'dotenv';

dotenv.config();

const FarmDashboardLayout = defineModel('FarmDashboardLayout', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  layout_config: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: {}
  },
  dashboard_type: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: 'farm',
    comment: 'Type of dashboard layout (farm, business, personal)'
  },
  is_default_for_new_users: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether this layout should be used as default for new farm users'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'farm_dashboard_layouts',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default FarmDashboardLayout;
