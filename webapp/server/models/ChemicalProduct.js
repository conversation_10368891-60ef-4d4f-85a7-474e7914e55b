import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Product from './Product.js';
import Farm from './Farm.js';
import dotenv from 'dotenv';

dotenv.config();

const ChemicalProduct = defineModel('ChemicalProduct', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  product_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Product,
      key: 'id'
    }
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  chemical_type: {
    type: DataTypes.ENUM('herbicide', 'insecticide', 'fungicide', 'fertilizer', 'other'),
    allowNull: false,
    defaultValue: 'other'
  },
  active_ingredients: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  concentration: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  application_rate: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  application_method: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  restricted_use: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  safety_information: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  preharvest_interval: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Days before harvest that application is safe'
  },
  reentry_interval: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Hours before reentry is safe'
  },
  epa_registration: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  manufacturer: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  supplier: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  expiration_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  storage_requirements: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'chemical_products',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default ChemicalProduct;
