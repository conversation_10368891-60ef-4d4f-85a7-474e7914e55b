import db from '../db.js';
import { v4 as uuidv4 } from 'uuid';

/**
 * Model for chat messages
 */
class ChatMessage {
  /**
   * Create a new chat message
   * @param {Object} message - Message data
   * @param {string} message.conversation_id - Conversation ID
   * @param {string} message.sender_id - Sender user ID
   * @param {string} message.message_type - Message type (text, image, file, system, task)
   * @param {string} message.content - Message content
   * @param {string} message.parent_message_id - Parent message ID for replies (optional)
   * @returns {Promise<Object>} Created message
   */
  static async create(message) {
    const id = uuidv4();
    const { 
      conversation_id, 
      sender_id, 
      message_type = 'text', 
      content, 
      parent_message_id = null 
    } = message;

    const query = `
      INSERT INTO chat_messages (
        id, conversation_id, sender_id, message_type, content, parent_message_id
      )
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `;

    const result = await db.query(query, [
      id, conversation_id, sender_id, message_type, content, parent_message_id
    ]);

    // Create read status entries for all participants
    await this.createReadStatusEntries(id, conversation_id, sender_id);

    return result.rows[0];
  }

  /**
   * Create read status entries for all participants in a conversation
   * @param {string} messageId - Message ID
   * @param {string} conversationId - Conversation ID
   * @param {string} senderId - Sender user ID
   * @returns {Promise<void>}
   */
  static async createReadStatusEntries(messageId, conversationId, senderId) {
    // Get all participants in the conversation
    const participantsQuery = `
      SELECT user_id FROM chat_conversation_participants
      WHERE conversation_id = $1
    `;

    const participantsResult = await db.query(participantsQuery, [conversationId]);
    const participants = participantsResult.rows;

    // Create read status entries for each participant
    for (const participant of participants) {
      const userId = participant.user_id;
      const isRead = userId === senderId; // Message is read by sender
      const readAt = isRead ? new Date() : null;

      const id = uuidv4();
      const query = `
        INSERT INTO chat_message_read_status (
          id, message_id, user_id, is_read, read_at
        )
        VALUES ($1, $2, $3, $4, $5)
      `;

      await db.query(query, [id, messageId, userId, isRead, readAt]);
    }
  }

  /**
   * Get a message by ID
   * @param {string} id - Message ID
   * @returns {Promise<Object>} Message
   */
  static async getById(id) {
    const query = `
      SELECT m.*, 
             u.first_name as sender_first_name, 
             u.last_name as sender_last_name,
             u.email as sender_email
      FROM chat_messages m
      LEFT JOIN users u ON m.sender_id = u.id
      WHERE m.id = $1
    `;

    const result = await db.query(query, [id]);
    return result.rows[0];
  }

  /**
   * Get messages for a conversation
   * @param {string} conversationId - Conversation ID
   * @param {Object} options - Query options
   * @param {number} options.limit - Maximum number of messages to return
   * @param {number} options.offset - Number of messages to skip
   * @param {string} options.beforeId - Get messages before this message ID
   * @param {string} options.afterId - Get messages after this message ID
   * @returns {Promise<Array>} Messages
   */
  static async getByConversationId(conversationId, options = {}) {
    const { 
      limit = 50, 
      offset = 0, 
      beforeId = null, 
      afterId = null 
    } = options;

    let whereClause = 'WHERE m.conversation_id = $1';
    const params = [conversationId];

    if (beforeId) {
      whereClause += ` AND m.created_at < (SELECT created_at FROM chat_messages WHERE id = $${params.length + 1})`;
      params.push(beforeId);
    }

    if (afterId) {
      whereClause += ` AND m.created_at > (SELECT created_at FROM chat_messages WHERE id = $${params.length + 1})`;
      params.push(afterId);
    }

    const query = `
      SELECT m.*, 
             u.first_name as sender_first_name, 
             u.last_name as sender_last_name,
             u.email as sender_email
      FROM chat_messages m
      LEFT JOIN users u ON m.sender_id = u.id
      ${whereClause}
      ORDER BY m.created_at DESC
      LIMIT $${params.length + 1} OFFSET $${params.length + 2}
    `;

    const result = await db.query(query, [...params, limit, offset]);
    return result.rows;
  }

  /**
   * Get threaded replies to a message
   * @param {string} parentMessageId - Parent message ID
   * @returns {Promise<Array>} Reply messages
   */
  static async getReplies(parentMessageId) {
    const query = `
      SELECT m.*, 
             u.first_name as sender_first_name, 
             u.last_name as sender_last_name,
             u.email as sender_email
      FROM chat_messages m
      LEFT JOIN users u ON m.sender_id = u.id
      WHERE m.parent_message_id = $1
      ORDER BY m.created_at ASC
    `;

    const result = await db.query(query, [parentMessageId]);
    return result.rows;
  }

  /**
   * Update a message
   * @param {string} id - Message ID
   * @param {Object} updates - Fields to update
   * @returns {Promise<Object>} Updated message
   */
  static async update(id, updates) {
    const allowedFields = ['content', 'is_edited'];
    const fields = Object.keys(updates).filter(field => allowedFields.includes(field));

    if (fields.length === 0) {
      return this.getById(id);
    }

    // Always set is_edited to true when updating content
    if (fields.includes('content') && !fields.includes('is_edited')) {
      updates.is_edited = true;
      fields.push('is_edited');
    }

    const setClause = fields.map((field, index) => `${field} = $${index + 2}`).join(', ');
    const values = fields.map(field => updates[field]);

    const query = `
      UPDATE chat_messages
      SET ${setClause}
      WHERE id = $1
      RETURNING *
    `;

    const result = await db.query(query, [id, ...values]);
    return result.rows[0];
  }

  /**
   * Delete a message
   * @param {string} id - Message ID
   * @returns {Promise<boolean>} Success
   */
  static async delete(id) {
    const query = `
      DELETE FROM chat_messages
      WHERE id = $1
      RETURNING id
    `;

    const result = await db.query(query, [id]);
    return result.rowCount > 0;
  }

  /**
   * Add an attachment to a message
   * @param {Object} attachment - Attachment data
   * @param {string} attachment.message_id - Message ID
   * @param {string} attachment.file_name - File name
   * @param {string} attachment.file_type - File type
   * @param {number} attachment.file_size - File size in bytes
   * @param {string} attachment.file_url - File URL
   * @param {string} attachment.thumbnail_url - Thumbnail URL (optional)
   * @returns {Promise<Object>} Created attachment
   */
  static async addAttachment(attachment) {
    const id = uuidv4();
    const { 
      message_id, 
      file_name, 
      file_type, 
      file_size, 
      file_url, 
      thumbnail_url = null 
    } = attachment;

    const query = `
      INSERT INTO chat_message_attachments (
        id, message_id, file_name, file_type, file_size, file_url, thumbnail_url
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `;

    const result = await db.query(query, [
      id, message_id, file_name, file_type, file_size, file_url, thumbnail_url
    ]);

    return result.rows[0];
  }

  /**
   * Get attachments for a message
   * @param {string} messageId - Message ID
   * @returns {Promise<Array>} Attachments
   */
  static async getAttachments(messageId) {
    const query = `
      SELECT * FROM chat_message_attachments
      WHERE message_id = $1
      ORDER BY created_at ASC
    `;

    const result = await db.query(query, [messageId]);
    return result.rows;
  }

  /**
   * Add a reaction to a message
   * @param {string} messageId - Message ID
   * @param {string} userId - User ID
   * @param {string} reaction - Emoji reaction code
   * @returns {Promise<Object>} Created reaction
   */
  static async addReaction(messageId, userId, reaction) {
    const id = uuidv4();

    // Check if reaction already exists
    const checkQuery = `
      SELECT id FROM chat_message_reactions
      WHERE message_id = $1 AND user_id = $2 AND reaction = $3
    `;

    const checkResult = await db.query(checkQuery, [messageId, userId, reaction]);

    if (checkResult.rowCount > 0) {
      // Reaction already exists, return it
      return checkResult.rows[0];
    }

    const query = `
      INSERT INTO chat_message_reactions (id, message_id, user_id, reaction)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `;

    const result = await db.query(query, [id, messageId, userId, reaction]);
    return result.rows[0];
  }

  /**
   * Remove a reaction from a message
   * @param {string} messageId - Message ID
   * @param {string} userId - User ID
   * @param {string} reaction - Emoji reaction code
   * @returns {Promise<boolean>} Success
   */
  static async removeReaction(messageId, userId, reaction) {
    const query = `
      DELETE FROM chat_message_reactions
      WHERE message_id = $1 AND user_id = $2 AND reaction = $3
      RETURNING id
    `;

    const result = await db.query(query, [messageId, userId, reaction]);
    return result.rowCount > 0;
  }

  /**
   * Get reactions for a message
   * @param {string} messageId - Message ID
   * @returns {Promise<Array>} Reactions
   */
  static async getReactions(messageId) {
    const query = `
      SELECT r.*, u.first_name, u.last_name, u.email
      FROM chat_message_reactions r
      JOIN users u ON r.user_id = u.id
      WHERE r.message_id = $1
      ORDER BY r.created_at ASC
    `;

    const result = await db.query(query, [messageId]);
    return result.rows;
  }

  /**
   * Mark a message as read for a user
   * @param {string} messageId - Message ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Updated read status
   */
  static async markAsRead(messageId, userId) {
    const query = `
      UPDATE chat_message_read_status
      SET is_read = TRUE, read_at = CURRENT_TIMESTAMP
      WHERE message_id = $1 AND user_id = $2
      RETURNING *
    `;

    const result = await db.query(query, [messageId, userId]);
    return result.rows[0];
  }

  /**
   * Get read status for a message
   * @param {string} messageId - Message ID
   * @returns {Promise<Array>} Read status for all users
   */
  static async getReadStatus(messageId) {
    const query = `
      SELECT rs.*, u.first_name, u.last_name, u.email
      FROM chat_message_read_status rs
      JOIN users u ON rs.user_id = u.id
      WHERE rs.message_id = $1
    `;

    const result = await db.query(query, [messageId]);
    return result.rows;
  }

  /**
   * Get unread messages count for a user in a conversation
   * @param {string} conversationId - Conversation ID
   * @param {string} userId - User ID
   * @returns {Promise<number>} Unread count
   */
  static async getUnreadCount(conversationId, userId) {
    const query = `
      SELECT COUNT(*) as unread_count
      FROM chat_messages m
      JOIN chat_message_read_status rs ON m.id = rs.message_id
      WHERE m.conversation_id = $1
      AND rs.user_id = $2
      AND rs.is_read = FALSE
    `;

    const result = await db.query(query, [conversationId, userId]);
    return parseInt(result.rows[0].unread_count, 10);
  }

  /**
   * Get all unread messages for a user
   * @param {string} userId - User ID
   * @returns {Promise<Array>} Unread messages grouped by conversation
   */
  static async getAllUnreadMessages(userId) {
    const query = `
      SELECT 
        c.id as conversation_id,
        c.name as conversation_name,
        c.type as conversation_type,
        COUNT(m.id) as unread_count,
        MAX(m.created_at) as latest_message_time
      FROM chat_conversations c
      JOIN chat_conversation_participants cp ON c.id = cp.conversation_id
      JOIN chat_messages m ON c.id = m.conversation_id
      JOIN chat_message_read_status rs ON m.id = rs.message_id
      WHERE cp.user_id = $1
      AND rs.user_id = $1
      AND rs.is_read = FALSE
      GROUP BY c.id, c.name, c.type
      ORDER BY latest_message_time DESC
    `;

    const result = await db.query(query, [userId]);
    return result.rows;
  }

  /**
   * Create a task from a message
   * @param {string} messageId - Message ID
   * @param {string} taskId - Task ID (from tasks table)
   * @param {string} createdBy - User ID who created the task
   * @returns {Promise<Object>} Created chat task
   */
  static async createTask(messageId, taskId, createdBy) {
    const id = uuidv4();

    const query = `
      INSERT INTO chat_tasks (id, message_id, task_id, created_by)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `;

    const result = await db.query(query, [id, messageId, taskId, createdBy]);
    return result.rows[0];
  }

  /**
   * Get task associated with a message
   * @param {string} messageId - Message ID
   * @returns {Promise<Object>} Chat task
   */
  static async getTask(messageId) {
    const query = `
      SELECT * FROM chat_tasks
      WHERE message_id = $1
    `;

    const result = await db.query(query, [messageId]);
    return result.rows[0];
  }

  /**
   * Search messages by content
   * @param {string} searchTerm - Search term
   * @param {string} userId - User ID (to limit search to accessible conversations)
   * @returns {Promise<Array>} Matching messages
   */
  static async search(searchTerm, userId) {
    const query = `
      SELECT m.*, 
             c.name as conversation_name,
             c.type as conversation_type,
             u.first_name as sender_first_name, 
             u.last_name as sender_last_name
      FROM chat_messages m
      JOIN chat_conversations c ON m.conversation_id = c.id
      JOIN chat_conversation_participants cp ON c.id = cp.conversation_id
      LEFT JOIN users u ON m.sender_id = u.id
      WHERE cp.user_id = $1
      AND m.content ILIKE $2
      ORDER BY m.created_at DESC
      LIMIT 100
    `;

    const result = await db.query(query, [userId, `%${searchTerm}%`]);
    return result.rows;
  }
}

export default ChatMessage;
