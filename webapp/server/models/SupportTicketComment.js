import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import User from './User.js';
import SupportTicket from './SupportTicket.js';
import dotenv from 'dotenv';

dotenv.config();

const SupportTicketComment = defineModel('SupportTicketComment', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  ticket_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: SupportTicket,
      key: 'id'
    }
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  is_internal: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether the comment is internal (only visible to support staff)'
  },
  is_from_email: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether the comment was received via email'
  },
  email_source: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Email address that sent this comment (if from email)'
  },
  email_message_id: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Message ID from the email headers'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'support_ticket_comments',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default SupportTicketComment;
