import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';
import User from './User.js';
import Farm from './Farm.js';
import Document from './Document.js';
import DocumentFolder from './DocumentFolder.js';
import Role from './Role.js';

dotenv.config();

const DocumentPermission = defineModel('DocumentPermission', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  // The entity this permission applies to (document or folder)
  entity_type: {
    type: DataTypes.STRING(20),
    allowNull: false,
    validate: {
      isIn: [['document', 'folder']]
    }
  },
  // ID of the document or folder
  entity_id: {
    type: DataTypes.UUID,
    allowNull: false
  },
  // User who has the permission (can be null if role_id is set)
  user_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: User,
      key: 'id'
    }
  },
  // Role that has the permission (can be null if user_id is set)
  role_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Role,
      key: 'id'
    }
  },
  // Whether this permission was inherited from a parent folder
  is_inherited: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  // Farm the permission belongs to
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  // Permission levels
  can_view: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  can_edit: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  can_delete: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  can_share: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'document_permissions',

  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'document_permissions_entity_idx',
      fields: ['entity_type', 'entity_id']
    },
    {
      name: 'document_permissions_user_idx',
      fields: ['user_id']
    },
    {
      name: 'document_permissions_role_idx',
      fields: ['role_id']
    },
    {
      name: 'document_permissions_farm_idx',
      fields: ['farm_id']
    },
    {
      name: 'document_permissions_inherited_idx',
      fields: ['is_inherited']
    }
  ]
});

// Define associations
// DocumentPermission.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
// User.hasMany(DocumentPermission, { foreignKey: 'user_id', as: 'documentPermissions' });
//
DocumentPermission.belongsTo(Role, { foreignKey: 'role_id', as: 'role' });
Role.hasMany(DocumentPermission, { foreignKey: 'role_id', as: 'documentPermissions' });
//
DocumentPermission.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });
Farm.hasMany(DocumentPermission, { foreignKey: 'farm_id', as: 'documentPermissions' });

export default DocumentPermission;
