import db from '../db.js';
import { v4 as uuidv4 } from 'uuid';

/**
 * Model for chat conversations
 */
class ChatConversation {
  /**
   * Create a new chat conversation
   * @param {Object} conversation - Conversation data
   * @param {string} conversation.name - Conversation name (optional for direct messages)
   * @param {string} conversation.type - Conversation type (direct, group, channel)
   * @param {string} conversation.farm_id - Farm ID (optional for farmer-to-farmer chats)
   * @param {string} conversation.created_by - User ID of creator
   * @param {boolean} conversation.is_pinned - Whether the conversation is pinned
   * @returns {Promise<Object>} Created conversation
   */
  static async create(conversation) {
    const id = uuidv4();
    const { name, type = 'direct', farm_id, created_by, is_pinned = false } = conversation;

    const query = `
      INSERT INTO chat_conversations (id, name, type, farm_id, created_by, is_pinned)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `;

    const result = await db.query(query, [id, name, type, farm_id, created_by, is_pinned]);
    return result.rows[0];
  }

  /**
   * Get a conversation by ID
   * @param {string} id - Conversation ID
   * @returns {Promise<Object>} Conversation
   */
  static async getById(id) {
    const query = `
      SELECT * FROM chat_conversations
      WHERE id = $1
    `;

    const result = await db.query(query, [id]);
    return result.rows[0];
  }

  /**
   * Get all conversations for a user
   * @param {string} userId - User ID
   * @returns {Promise<Array>} Conversations
   */
  static async getByUserId(userId) {
    const query = `
      SELECT c.* 
      FROM chat_conversations c
      JOIN chat_conversation_participants p ON c.id = p.conversation_id
      WHERE p.user_id = $1
      ORDER BY c.updated_at DESC
    `;

    const result = await db.query(query, [userId]);
    return result.rows;
  }

  /**
   * Get all conversations for a farm
   * @param {string} farmId - Farm ID
   * @returns {Promise<Array>} Conversations
   */
  static async getByFarmId(farmId) {
    const query = `
      SELECT * FROM chat_conversations
      WHERE farm_id = $1
      ORDER BY updated_at DESC
    `;

    const result = await db.query(query, [farmId]);
    return result.rows;
  }

  /**
   * Get direct conversation between two users
   * @param {string} userId1 - First user ID
   * @param {string} userId2 - Second user ID
   * @returns {Promise<Object>} Conversation
   */
  static async getDirectConversation(userId1, userId2) {
    const query = `
      SELECT c.* 
      FROM chat_conversations c
      JOIN chat_conversation_participants p1 ON c.id = p1.conversation_id
      JOIN chat_conversation_participants p2 ON c.id = p2.conversation_id
      WHERE c.type = 'direct'
      AND p1.user_id = $1
      AND p2.user_id = $2
      LIMIT 1
    `;

    const result = await db.query(query, [userId1, userId2]);
    return result.rows[0];
  }

  /**
   * Update a conversation
   * @param {string} id - Conversation ID
   * @param {Object} updates - Fields to update
   * @returns {Promise<Object>} Updated conversation
   */
  static async update(id, updates) {
    const allowedFields = ['name', 'is_pinned'];
    const fields = Object.keys(updates).filter(field => allowedFields.includes(field));

    if (fields.length === 0) {
      return this.getById(id);
    }

    const setClause = fields.map((field, index) => `${field} = $${index + 2}`).join(', ');
    const values = fields.map(field => updates[field]);

    const query = `
      UPDATE chat_conversations
      SET ${setClause}
      WHERE id = $1
      RETURNING *
    `;

    const result = await db.query(query, [id, ...values]);
    return result.rows[0];
  }

  /**
   * Delete a conversation
   * @param {string} id - Conversation ID
   * @returns {Promise<boolean>} Success
   */
  static async delete(id) {
    const query = `
      DELETE FROM chat_conversations
      WHERE id = $1
      RETURNING id
    `;

    const result = await db.query(query, [id]);
    return result.rowCount > 0;
  }

  /**
   * Add a participant to a conversation
   * @param {string} conversationId - Conversation ID
   * @param {string} userId - User ID
   * @param {boolean} isAdmin - Whether the user is an admin
   * @returns {Promise<Object>} Participant
   */
  static async addParticipant(conversationId, userId, isAdmin = false) {
    const id = uuidv4();
    const query = `
      INSERT INTO chat_conversation_participants (id, conversation_id, user_id, is_admin)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `;

    const result = await db.query(query, [id, conversationId, userId, isAdmin]);
    return result.rows[0];
  }

  /**
   * Remove a participant from a conversation
   * @param {string} conversationId - Conversation ID
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} Success
   */
  static async removeParticipant(conversationId, userId) {
    const query = `
      DELETE FROM chat_conversation_participants
      WHERE conversation_id = $1 AND user_id = $2
      RETURNING id
    `;

    const result = await db.query(query, [conversationId, userId]);
    return result.rowCount > 0;
  }

  /**
   * Get all participants in a conversation
   * @param {string} conversationId - Conversation ID
   * @returns {Promise<Array>} Participants
   */
  static async getParticipants(conversationId) {
    const query = `
      SELECT p.*, u.first_name, u.last_name, u.email
      FROM chat_conversation_participants p
      JOIN users u ON p.user_id = u.id
      WHERE p.conversation_id = $1
    `;

    const result = await db.query(query, [conversationId]);
    return result.rows;
  }

  /**
   * Check if a user is a participant in a conversation
   * @param {string} conversationId - Conversation ID
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} Is participant
   */
  static async isParticipant(conversationId, userId) {
    const query = `
      SELECT id FROM chat_conversation_participants
      WHERE conversation_id = $1 AND user_id = $2
    `;

    const result = await db.query(query, [conversationId, userId]);
    return result.rowCount > 0;
  }

  /**
   * Update a participant's status
   * @param {string} conversationId - Conversation ID
   * @param {string} userId - User ID
   * @param {Object} updates - Fields to update
   * @returns {Promise<Object>} Updated participant
   */
  static async updateParticipant(conversationId, userId, updates) {
    const allowedFields = ['is_admin', 'is_muted', 'last_read_at'];
    const fields = Object.keys(updates).filter(field => allowedFields.includes(field));

    if (fields.length === 0) {
      return null;
    }

    const setClause = fields.map((field, index) => `${field} = $${index + 3}`).join(', ');
    const values = fields.map(field => updates[field]);

    const query = `
      UPDATE chat_conversation_participants
      SET ${setClause}
      WHERE conversation_id = $1 AND user_id = $2
      RETURNING *
    `;

    const result = await db.query(query, [conversationId, userId, ...values]);
    return result.rows[0];
  }
}

export default ChatConversation;
