// This file is deprecated. Use FarmAdmin.js instead.
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import FarmAdmin from './FarmAdmin.js';
import dotenv from 'dotenv';

dotenv.config();

// Get the schema from environment variables
const schema = process.env.DB_SCHEMA || 'site';
console.log(`Defining TenantAdmin model (deprecated) with schema: ${schema}`);

// Create a minimal TenantAdmin model that just references FarmAdmin
// This is to avoid breaking existing code that imports TenantAdmin
const TenantAdmin = FarmAdmin;

console.log(`TenantAdmin model (deprecated) defined as an alias to FarmAdmin model`);

export default TenantAdmin;
