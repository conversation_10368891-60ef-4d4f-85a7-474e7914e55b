import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';
import SubscriptionPlan from './SubscriptionPlan.js';
import { defineModel } from '../utils/modelUtils.js';

dotenv.config();

const User = defineModel('User', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  password_hash: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  first_name: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  last_name: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  phone_number: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  two_factor_secret: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  two_factor_enabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  two_factor_method: {
    type: DataTypes.ENUM('app', 'sms', 'email'),
    allowNull: true,
    comment: 'Preferred 2FA method: authenticator app, SMS, or email'
  },
  phone_verified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether the user has verified their phone number'
  },
  phone_verification_code: {
    type: DataTypes.STRING(10),
    allowNull: true,
    comment: 'Temporary code for phone verification'
  },
  phone_verification_expires: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Expiration time for phone verification code'
  },
  reset_password_token: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  reset_password_expires: {
    type: DataTypes.DATE,
    allowNull: true
  },
  email_verified: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether the user has verified their email address'
  },
  email_verification_token: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  email_verification_expires: {
    type: DataTypes.DATE,
    allowNull: true
  },
  email_2fa_code: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Verification code for email-based 2FA'
  },
  email_2fa_expires: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Expiration timestamp for email-based 2FA code'
  },
  matrix_token: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Token used for authenticating with the Matrix chat system'
  },
  stripe_customer_id: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Stripe customer ID for this user'
  },
  is_global_admin: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether the user is a global admin (platform owner)'
  },
  user_type: {
    type: DataTypes.ENUM('farmer', 'supplier', 'vet', 'admin', 'accountant'),
    allowNull: false,
    defaultValue: 'farmer',
    comment: 'Type of user account'
  },
  is_business_owner: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether the user owns a business listing (supplier or vet)'
  },
  is_approved: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether the business account has been approved by an admin'
  },
  help_tips_disabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether the user has disabled all help tips'
  },
  subscription_plan_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: SubscriptionPlan,
      key: 'id'
    },
    comment: 'Subscription plan for business owners'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'users',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  hooks: {
    beforeCreate: async (user) => {
      console.log('User beforeCreate hook triggered');
      if (user.password_hash) {
        console.log('Hashing password for new user');
        try {
          user.password_hash = await bcrypt.hash(user.password_hash, 10);
          console.log('Password hashed successfully');
        } catch (error) {
          console.error('Error hashing password:', error);
          throw error;
        }
      } else {
        console.warn('No password_hash provided for new user');
      }
    },
    beforeUpdate: async (user) => {
      console.log('User beforeUpdate hook triggered');
      if (user.changed('password_hash')) {
        console.log('Hashing updated password');
        try {
          user.password_hash = await bcrypt.hash(user.password_hash, 10);
          console.log('Password hashed successfully');
        } catch (error) {
          console.error('Error hashing password:', error);
          throw error;
        }
      }
    }
  }
});

// Model is now defined with dynamic schema via defineModel

// Associations are defined in associations.js

// Instance methods
User.prototype.validatePassword = async function(password) {
  return await bcrypt.compare(password, this.password_hash);
};

// Static methods
User.findByEmail = async function(email) {
  return await this.findOne({ where: { email } });
};

export default User;
