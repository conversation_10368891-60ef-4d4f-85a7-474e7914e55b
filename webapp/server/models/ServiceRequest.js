import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import User from './User.js';
import ServiceProvider from './ServiceProvider.js';
import dotenv from 'dotenv';

dotenv.config();

const ServiceRequest = defineModel('ServiceRequest', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  service_provider_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: ServiceProvider,
      key: 'id'
    }
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    },
    comment: 'User who created the request'
  },
  title: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: 'Brief description of the service needed'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: 'Detailed description of the service needed'
  },
  service_type: {
    type: DataTypes.ENUM('agronomy', 'custom_application', 'harvesting', 'planting', 'soil_testing', 'transportation', 'veterinary', 'maintenance', 'other'),
    allowNull: false,
    defaultValue: 'other'
  },
  status: {
    type: DataTypes.ENUM('draft', 'submitted', 'accepted', 'scheduled', 'in_progress', 'completed', 'cancelled', 'declined'),
    allowNull: false,
    defaultValue: 'draft'
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    allowNull: false,
    defaultValue: 'medium'
  },
  location: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Location where service is needed'
  },
  requested_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Date when service is requested'
  },
  scheduled_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Date when service is scheduled'
  },
  completion_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Date when service was completed'
  },
  estimated_cost: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Estimated cost of the service'
  },
  actual_cost: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Actual cost of the service after completion'
  },
  payment_status: {
    type: DataTypes.ENUM('unpaid', 'partial', 'paid'),
    allowNull: false,
    defaultValue: 'unpaid'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Additional notes about the service request'
  },
  provider_notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Notes from the service provider'
  },
  rating: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 5
    },
    comment: 'Rating given to the service provider (1-5)'
  },
  review: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Review of the service provider'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'service_requests',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: All associations are now defined in associations.js

export default ServiceRequest;
