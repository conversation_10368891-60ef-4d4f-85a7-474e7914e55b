import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import Product from './Product.js';
import dotenv from 'dotenv';

dotenv.config();

const MarketTrend = defineModel('MarketTrend', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Farm,
      key: 'id'
    },
    comment: 'Optional farm association, null for global market trends'
  },
  product_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Product,
      key: 'id'
    }
  },
  product_category: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: 'Category of product or commodity'
  },
  product_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: 'Name of product or commodity'
  },
  trend_date: {
    type: DataTypes.DATE,
    allowNull: false,
    comment: 'Date of the trend data point'
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'Price at the given date'
  },
  currency: {
    type: DataTypes.STRING(3),
    allowNull: false,
    defaultValue: 'USD'
  },
  unit: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: 'Unit of measurement (e.g., kg, lb, bushel)'
  },
  source: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Source of the market data'
  },
  market_location: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Geographic market location'
  },
  volume: {
    type: DataTypes.DECIMAL(12, 2),
    allowNull: true,
    comment: 'Trading volume if available'
  },
  price_change: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Price change from previous period'
  },
  price_change_percentage: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Percentage price change from previous period'
  },
  forecast: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Price forecast data if available'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'market_trends',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default MarketTrend;
