import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';
import User from './User.js';
import Farm from './Farm.js';
import Expense from './Expense.js';

dotenv.config();

const Receipt = defineModel('Receipt', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  receipt_number: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  vendor_name: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'USD',
    allowNull: false
  },
  receipt_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('pending', 'approved', 'rejected'),
    defaultValue: 'pending',
    allowNull: false
  },
  file_path: {
    type: DataTypes.STRING(1024),
    allowNull: true
  },
  file_size: {
    type: DataTypes.BIGINT,
    allowNull: true
  },
  file_type: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  mime_type: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  email_source: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  email_subject: {
    type: DataTypes.STRING(512),
    allowNull: true
  },
  email_received_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  // tenant_id field removed as part of migration from tenant to farm
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  expense_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Expense,
      key: 'id'
    }
  },
  uploaded_by: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: User,
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'receipts',

  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'receipts_receipt_number_idx',
      fields: ['receipt_number']
    },
    {
      name: 'receipts_vendor_name_idx',
      fields: ['vendor_name']
    },
    {
      name: 'receipts_receipt_date_idx',
      fields: ['receipt_date']
    },
    {
      name: 'receipts_status_idx',
      fields: ['status']
    },
    // tenant_id index removed as part of migration from tenant to farm
    {
      name: 'receipts_farm_id_idx',
      fields: ['farm_id']
    },
    {
      name: 'receipts_expense_id_idx',
      fields: ['expense_id']
    }
  ]
});

// Associations are defined in associations.js

export default Receipt;
