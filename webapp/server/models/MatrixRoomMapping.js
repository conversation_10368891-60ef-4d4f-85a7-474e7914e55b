import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';

/**
 * Matrix Room Mapping model
 * Stores mappings between NxtAcre conversations and Matrix rooms
 */
const MatrixRoomMapping = sequelize.define('MatrixRoomMapping', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  conversation_id: {
    type: DataTypes.UUID,
    allowNull: false,
    unique: true,
    references: {
      model: 'chat_conversations',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  matrix_room_id: {
    type: DataTypes.TEXT,
    allowNull: false,
    unique: true
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'matrix_room_mappings',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

/**
 * Create a new mapping
 * @param {Object} data - The mapping data
 * @returns {Promise<Object>} The created mapping
 */
MatrixRoomMapping.createMapping = async (data) => {
  try {
    return await MatrixRoomMapping.create(data);
  } catch (error) {
    console.error('Error creating matrix room mapping:', error);
    throw error;
  }
};

/**
 * Get a mapping by conversation ID
 * @param {string} conversationId - The conversation ID
 * @returns {Promise<Object>} The mapping
 */
MatrixRoomMapping.getByConversationId = async (conversationId) => {
  try {
    return await MatrixRoomMapping.findOne({
      where: { conversation_id: conversationId }
    });
  } catch (error) {
    console.error('Error getting matrix room mapping by conversation ID:', error);
    throw error;
  }
};

/**
 * Get a mapping by Matrix room ID
 * @param {string} matrixRoomId - The Matrix room ID
 * @returns {Promise<Object>} The mapping
 */
MatrixRoomMapping.getByMatrixRoomId = async (matrixRoomId) => {
  try {
    return await MatrixRoomMapping.findOne({
      where: { matrix_room_id: matrixRoomId }
    });
  } catch (error) {
    console.error('Error getting matrix room mapping by Matrix room ID:', error);
    throw error;
  }
};

/**
 * Get all mappings
 * @returns {Promise<Array>} All mappings
 */
MatrixRoomMapping.getAllMappings = async () => {
  try {
    return await MatrixRoomMapping.findAll();
  } catch (error) {
    console.error('Error getting all matrix room mappings:', error);
    throw error;
  }
};

/**
 * Delete a mapping by conversation ID
 * @param {string} conversationId - The conversation ID
 * @returns {Promise<boolean>} Whether the mapping was deleted
 */
MatrixRoomMapping.deleteByConversationId = async (conversationId) => {
  try {
    const result = await MatrixRoomMapping.destroy({
      where: { conversation_id: conversationId }
    });
    return result > 0;
  } catch (error) {
    console.error('Error deleting matrix room mapping by conversation ID:', error);
    throw error;
  }
};

export default MatrixRoomMapping;