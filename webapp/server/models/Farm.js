import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import SubscriptionPlan from './SubscriptionPlan.js';
import dotenv from 'dotenv';
import { defineModel } from '../utils/modelUtils.js';

dotenv.config();

const Farm = defineModel('Farm', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  subscription_plan_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: SubscriptionPlan,
      key: 'id'
    }
  },
  subscription_status: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'active'
  },
  subscription_start_date: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  subscription_end_date: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  billing_email: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  billing_address: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  billing_city: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  billing_state: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  billing_zip_code: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  billing_country: {
    type: DataTypes.STRING(100),
    defaultValue: 'USA'
  },
  payment_method_id: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  stripe_customer_id: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  subdomain: {
    type: DataTypes.STRING(50),
    allowNull: true,
    unique: true,
    validate: {
      is: /^[a-z0-9-]+$/i // Only allow alphanumeric characters and hyphens
    }
  },
  custom_login_text: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  custom_login_logo: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  customer_portal_enabled: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether the customer portal is enabled for this farm'
  },
  customer_pays_stripe_fees: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether customers pay Stripe processing fees (true) or the farm absorbs them (false)'
  },
  customer_portal_stripe_fee_message: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Custom message to display to customers about Stripe processing fees'
  },
  location_data: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'GeoJSON for farm location'
  },
  tax_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    defaultValue: null,
    comment: 'Tax rate percentage for the farm state'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'farms',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default Farm;
