import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import User from './User.js';
import { defineModel } from '../utils/modelUtils.js';

const NotificationPreference = defineModel('NotificationPreference', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    },
    unique: true,
    comment: 'The user these preferences belong to'
  },
  enable_in_app: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether in-app notifications are enabled'
  },
  enable_email: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether email notifications are enabled'
  },
  chat_message_notifications: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether chat message notifications are enabled'
  },
  task_notifications: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether task notifications are enabled'
  },
  document_notifications: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether document notifications are enabled'
  },
  system_notifications: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether system notifications are enabled'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'notification_preferences',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
NotificationPreference.belongsTo(User, { foreignKey: 'user_id' });
User.hasOne(NotificationPreference, { foreignKey: 'user_id' });

// Static methods
NotificationPreference.findByUserId = async function(userId) {
  return await this.findOne({ where: { user_id: userId } });
};

NotificationPreference.createOrUpdate = async function(userId, preferences) {
  const [notificationPreference, created] = await this.findOrCreate({
    where: { user_id: userId },
    defaults: { ...preferences, user_id: userId }
  });

  if (!created) {
    await notificationPreference.update(preferences);
  }

  return notificationPreference;
};

export default NotificationPreference;