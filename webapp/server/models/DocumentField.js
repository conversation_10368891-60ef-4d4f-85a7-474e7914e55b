import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import SignableDocument from './SignableDocument.js';
import DocumentSigner from './DocumentSigner.js';

const DocumentField = defineModel('DocumentField', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  document_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: SignableDocument,
      key: 'id'
    }
  },
  signer_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: DocumentSigner,
      key: 'id'
    }
  },
  field_type: {
    type: DataTypes.ENUM('signature', 'initial', 'date', 'text', 'checkbox', 'radio', 'dropdown', 'attachment'),
    allowNull: false
  },
  field_name: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  field_value: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  is_required: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  page_number: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  x_position: {
    type: DataTypes.FLOAT,
    allowNull: true
  },
  y_position: {
    type: DataTypes.FLOAT,
    allowNull: true
  },
  width: {
    type: DataTypes.FLOAT,
    allowNull: true
  },
  height: {
    type: DataTypes.FLOAT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'document_fields',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'document_fields_document_id_idx',
      fields: ['document_id']
    },
    {
      name: 'document_fields_signer_id_idx',
      fields: ['signer_id']
    },
    {
      name: 'document_fields_field_type_idx',
      fields: ['field_type']
    }
  ]
});

// Define associations
DocumentField.belongsTo(SignableDocument, { foreignKey: 'document_id', as: 'fieldDocument' });
DocumentField.belongsTo(DocumentSigner, { foreignKey: 'signer_id', as: 'signer' });

export default DocumentField;
