import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import MigrationJob from './MigrationJob.js';

const MigrationResult = defineModel('MigrationResult', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  job_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: MigrationJob,
      key: 'id'
    }
  },
  total_records: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  imported_records: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  failed_records: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  warnings: {
    type: DataTypes.ARRAY(DataTypes.TEXT),
    defaultValue: []
  },
  errors: {
    type: DataTypes.ARRAY(DataTypes.TEXT),
    defaultValue: []
  },
  entity_counts: {
    type: DataTypes.JSONB,
    defaultValue: {}
  }
}, {
  tableName: 'migration_results',
  timestamps: true,
  underscored: true
});

// Associations are defined in associations.js

export default MigrationResult;
