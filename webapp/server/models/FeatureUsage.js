import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import dotenv from 'dotenv';

dotenv.config();

const FeatureUsage = defineModel('FeatureUsage', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    },
    comment: 'Replaced tenant_id as part of migration from tenant to farm'
  },
  feature_name: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  usage_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  usage_limit: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  reset_date: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'feature_usage',

  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
FeatureUsage.belongsTo(Farm, { foreignKey: 'farm_id' });
Farm.hasMany(FeatureUsage, { foreignKey: 'farm_id' });

export default FeatureUsage;
