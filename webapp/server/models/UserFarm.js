import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import User from './User.js';
import Farm from './Farm.js';
import Role from './Role.js';
import dotenv from 'dotenv';

dotenv.config();

const UserFarm = defineModel('UserFarm', {
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  role: {
    type: DataTypes.ENUM('farm_owner', 'farm_admin', 'farm_manager', 'farm_employee', 'accountant'),
    allowNull: true,
    comment: 'Legacy role field (deprecated, use role_id instead)'
  },
  role_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Role,
      key: 'id'
    },
    comment: 'Reference to the role'
  },
  // Permissions for this role (customizable by farm owner)
  permissions: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: null,
    comment: 'Custom permissions for this role, overrides default role permissions'
  },
  is_billing_contact: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this user is responsible for billing for this farm'
  },
  is_approved: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this user has been approved by a farm owner or admin'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'user_farms',

  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

// Static methods
UserFarm.areUsersInSameFarm = async function(userId1, userId2) {
  try {
    const query = `
      SELECT COUNT(*) as count
      FROM user_farms uf1
      JOIN user_farms uf2 ON uf1.farm_id = uf2.farm_id
      WHERE uf1.user_id = :userId1
      AND uf2.user_id = :userId2
      AND uf1.is_approved = TRUE
      AND uf2.is_approved = TRUE
    `;

    const [result] = await sequelize.query(query, {
      replacements: { userId1, userId2 },
      type: sequelize.QueryTypes.SELECT
    });

    return result.count > 0;
  } catch (error) {
    console.error('Error checking if users are in the same farm:', error);
    return false;
  }
};

export default UserFarm;
