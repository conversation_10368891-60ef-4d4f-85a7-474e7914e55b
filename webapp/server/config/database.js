import { Sequelize, Op } from 'sequelize';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { getSchema } from '../utils/schemaUtils.js';

dotenv.config();

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Database configuration
const sequelize = new Sequelize(
  process.env.DB_NAME || 'farmbooks',
  process.env.DB_USER || 'postgres',
  process.env.DB_PASSWORD || 'postgres',
  {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    dialect: 'postgres',
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    schema: getSchema(),
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000
    },
    dialectOptions: {
      ssl: process.env.DB_HOST && process.env.DB_HOST.includes('digitalocean') ? {
        require: true,
        rejectUnauthorized: false
      } : process.env.NODE_ENV === 'production' ? {
        require: true,
        rejectUnauthorized: false
      } : false
    }
  }
);

// Create AI assistant tables
const createAIAssistantTables = async (schema) => {
  console.log('Creating AI Assistant tables...');
  const aiAssistantTablesPath = path.join(__dirname, '..', 'db', 'ai_assistant_tables.sql');

  // Check if the file exists
  if (fs.existsSync(aiAssistantTablesPath)) {
    const aiAssistantTablesSQL = fs.readFileSync(aiAssistantTablesPath, 'utf8');

    // Split the SQL into individual statements
    const aiAssistantStatements = aiAssistantTablesSQL
      .split(';')
      .filter(statement => statement.trim() !== '')
      .map(statement => statement.trim() + ';');

    console.log(`Found ${aiAssistantStatements.length} SQL statements to execute in ai_assistant_tables.sql.`);

    // Set the search_path to use the specified schema before executing AI assistant tables
    await sequelize.query(`SET search_path TO ${schema};`);
    console.log(`Set search_path to schema: ${schema} for AI assistant tables`);

    // Execute each statement
    for (let i = 0; i < aiAssistantStatements.length; i++) {
      const statement = aiAssistantStatements[i];
      try {
        await sequelize.query(statement);
        console.log(`Executed statement ${i + 1}/${aiAssistantStatements.length} from ai_assistant_tables.sql`);
      } catch (error) {
        console.error(`Error executing statement ${i + 1}/${aiAssistantStatements.length} from ai_assistant_tables.sql:`, error.message);
        console.error('Statement:', statement);
        // Don't throw error for AI assistant tables statements, just log it
      }
    }

    console.log('AI Assistant tables created successfully!');
  } else {
    console.warn('AI Assistant tables SQL file not found at:', aiAssistantTablesPath);
  }
};

// Test the connection
const testConnection = async () => {
  try {
    console.log('Attempting to connect to database...');
    const schema = getSchema();
    console.log('Database config:', {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      database: process.env.DB_NAME || 'farmbooks',
      user: process.env.DB_USER || 'postgres',
      schema: schema,
      // Not logging password for security reasons
    });

    await sequelize.authenticate();
    console.log('Database connection has been established successfully.');

    // Check if schema exists and create it if it doesn't
    try {
      console.log(`Checking if schema '${schema}' exists...`);
      const schemaCheck = await sequelize.query(
        `SELECT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = '${schema}');`,
        { type: sequelize.QueryTypes.SELECT }
      );

      const schemaExists = schemaCheck[0].exists;

      if (!schemaExists) {
        console.log(`Schema '${schema}' does not exist, creating it...`);
        await sequelize.query(`CREATE SCHEMA IF NOT EXISTS ${schema};`);
        console.log(`Schema '${schema}' created successfully.`);
      } else {
        console.log(`Schema '${schema}' already exists.`);
      }

      // Set search_path to use the specified schema
      await sequelize.query(`SET search_path TO ${schema};`);
      console.log(`Set search_path to schema: ${schema}`);
    } catch (schemaError) {
      console.error('Error checking or creating schema:', schemaError.message);
    }

    // Check if tables exist
    try {
      console.log('Checking if users table exists...');
      await sequelize.query(`SELECT 1 FROM ${schema}.users LIMIT 1`);
      console.log('Users table exists and is accessible.');
    } catch (tableError) {
      console.error('Error accessing users table:', tableError.message);
      console.log('This may indicate that the database schema has not been properly initialized.');
    }
  } catch (error) {
    console.error('Unable to connect to the database:', error);
    console.error('Connection error details:', {
      message: error.message,
      name: error.name,
      code: error.original?.code,
      errno: error.original?.errno
    });
  }
};

// Initialize database schema
const initializeSchema = async () => {
  try {
    console.log('Checking if database schema needs to be initialized...');
    const schema = getSchema();

    // Check if schema exists and create it if it doesn't
    try {
      console.log(`Checking if schema '${schema}' exists...`);
      const schemaCheck = await sequelize.query(
        `SELECT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = '${schema}');`,
        { type: sequelize.QueryTypes.SELECT }
      );

      const schemaExists = schemaCheck[0].exists;

      if (!schemaExists) {
        console.log(`Schema '${schema}' does not exist, creating it...`);
        await sequelize.query(`CREATE SCHEMA IF NOT EXISTS ${schema};`);
        console.log(`Schema '${schema}' created successfully.`);
      } else {
        console.log(`Schema '${schema}' already exists.`);
      }

      // Set search_path to use the specified schema
      await sequelize.query(`SET search_path TO ${schema};`);
      console.log(`Set search_path to schema: ${schema}`);
    } catch (schemaError) {
      console.error('Error checking or creating schema:', schemaError.message);
    }

    // Check if users table exists
    let usersTableExists = false;
    try {
      await sequelize.query(`SELECT 1 FROM ${schema}.users LIMIT 1`);
      console.log('Users table already exists, schema initialization not needed.');
      usersTableExists = true;
    } catch (error) {
      console.log('Users table does not exist, initializing database schema...');
    }

    // Check if AI assistant tables exist
    let aiAssistantTablesExist = false;
    try {
      await sequelize.query(`SELECT 1 FROM ${schema}.ai_assistant_queries LIMIT 1`);
      console.log('AI assistant tables already exist.');
      aiAssistantTablesExist = true;
    } catch (error) {
      console.log('AI assistant tables do not exist, they will be created.');
    }

    // If users table exists and AI assistant tables exist, we don't need to apply any schema migrations automatically
    // All migrations should be applied through the global admin migrations page
    if (usersTableExists && aiAssistantTablesExist) {
      console.log('Users table and AI assistant tables exist. Schema migrations should be applied through the global admin migrations page.');
      return;
    }

    // If users table exists but AI assistant tables don't, we only need to create the AI assistant tables
    if (usersTableExists && !aiAssistantTablesExist) {
      console.log('Users table exists but AI assistant tables do not. Creating AI assistant tables...');
      // Skip to creating AI assistant tables
      try {
        await createAIAssistantTables(schema);
      } catch (error) {
        console.error('Error creating AI assistant tables:', error);
      }
      return;
    }

    // Read schema.sql file
    const schemaPath = path.join(__dirname, '..', 'db', 'schema.sql');
    console.log('Reading schema file from:', schemaPath);

    const schemaSQL = fs.readFileSync(schemaPath, 'utf8');
    console.log('Schema file read successfully.');

    // Split the schema into individual statements
    const statements = schemaSQL
      .split(';')
      .filter(statement => statement.trim() !== '')
      .map(statement => statement.trim() + ';');

    console.log(`Found ${statements.length} SQL statements to execute.`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      try {
        // Set the search_path to use the specified schema
        if (i === 0) {
          const schema = getSchema();
          await sequelize.query(`SET search_path TO ${schema};`);
          console.log(`Set search_path to schema: ${schema}`);
        }
        await sequelize.query(statement);
        console.log(`Executed statement ${i + 1}/${statements.length}`);
      } catch (error) {
        console.error(`Error executing statement ${i + 1}/${statements.length}:`, error.message);
        console.error('Statement:', statement);
        throw error;
      }
    }

    console.log('Database schema initialized successfully.');

    // Create AI assistant tables
    try {
      await createAIAssistantTables(schema);
    } catch (aiTablesError) {
      console.error('Error creating AI Assistant tables:', aiTablesError);
      // Continue even if AI assistant tables creation fails
    }
  } catch (error) {
    console.error('Error initializing database schema:', error);
    throw error;
  }
};

// Synchronize models with database
const syncModels = async () => {
  try {
    console.log('Synchronizing models with database...');
    const schema = getSchema();

    // Set search_path to use the specified schema
    await sequelize.query(`SET search_path TO ${schema};`);
    console.log(`Set search_path to schema: ${schema}`);

    // Synchronize models with the database
    await sequelize.sync({
      alter: false, // Use alter: true to update tables if models change
      schema: schema // Specify the schema to use
    });

    console.log(`Models synchronized successfully with schema: ${schema}`);

    // Initialize feature permissions
    try {
      console.log('Initializing feature permissions...');
      const initializeFeaturePermissions = (await import('../scripts/initializeFeaturePermissions.js')).default;
      await initializeFeaturePermissions();
      console.log('Feature permissions initialized successfully');
    } catch (permissionError) {
      console.error('Error initializing feature permissions:', permissionError);
      // Continue even if permission initialization fails
    }
  } catch (error) {
    console.error('Error synchronizing models:', error);
    throw error;
  }
};

export { sequelize, Op, testConnection, initializeSchema, syncModels, createAIAssistantTables };
