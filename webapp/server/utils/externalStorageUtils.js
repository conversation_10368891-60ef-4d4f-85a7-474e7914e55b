import { google } from 'googleapis';
import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { promisify } from 'util';
import { saveFile, generateStoragePath, updateStorageUsage } from './fileUtils.js';
import Document from '../models/Document.js';
import ExternalStorageConnection from '../models/ExternalStorageConnection.js';

// Get directory name for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Convert fs functions to promise-based
const writeFile = promisify(fs.writeFile);
const readFile = promisify(fs.readFile);

// Base upload directory
const uploadsDir = path.join(__dirname, '..', '..', 'uploads');

/**
 * Get Google Drive client for a user
 * @param {string} userId - The user ID
 * @param {string} tenantId - The tenant ID
 * @returns {Promise<{drive: any, success: boolean, error: string|null}>}
 */
export const getGoogleDriveClient = async (userId, tenantId) => {
  try {
    // Find Google Drive connection for the user
    const connection = await ExternalStorageConnection.findOne({
      where: {
        user_id: userId,
        tenant_id: tenantId,
        provider: 'google_drive',
        status: 'active'
      }
    });

    if (!connection) {
      return {
        drive: null,
        success: false,
        error: 'Google Drive connection not found or inactive'
      };
    }

    // Check if token is expired and needs refresh
    if (connection.token_expires_at && new Date(connection.token_expires_at) <= new Date()) {
      // Token is expired, need to refresh
      const oauth2Client = new google.auth.OAuth2(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET,
        process.env.GOOGLE_REDIRECT_URI
      );

      // Set credentials for refresh
      oauth2Client.setCredentials({
        refresh_token: connection.refresh_token
      });

      try {
        // Refresh token
        const { tokens } = await oauth2Client.refreshToken(connection.refresh_token);

        // Calculate new expiration date
        const expiryDate = tokens.expiry_date || (Date.now() + (tokens.expires_in * 1000));

        // Update connection with new tokens
        await connection.update({
          access_token: tokens.access_token,
          token_expires_at: new Date(expiryDate),
          updated_at: new Date()
        });

        // Set up drive client with new token
        oauth2Client.setCredentials({
          access_token: tokens.access_token,
          refresh_token: connection.refresh_token,
          expiry_date: expiryDate
        });

        const drive = google.drive({ version: 'v3', auth: oauth2Client });

        return {
          drive,
          success: true,
          error: null
        };
      } catch (refreshError) {
        console.error('Error refreshing Google Drive token:', refreshError);

        // Update connection status to error
        await connection.update({
          status: 'error',
          updated_at: new Date()
        });

        return {
          drive: null,
          success: false,
          error: 'Failed to refresh Google Drive token'
        };
      }
    }

    // Token is still valid
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_REDIRECT_URI
    );

    oauth2Client.setCredentials({
      access_token: connection.access_token,
      refresh_token: connection.refresh_token,
      expiry_date: connection.token_expires_at ? connection.token_expires_at.getTime() : null
    });

    const drive = google.drive({ version: 'v3', auth: oauth2Client });

    return {
      drive,
      success: true,
      error: null
    };
  } catch (error) {
    console.error('Error getting Google Drive client:', error);
    return {
      drive: null,
      success: false,
      error: error.message
    };
  }
};

/**
 * Get Dropbox client for a user
 * @param {string} userId - The user ID
 * @param {string} tenantId - The tenant ID
 * @returns {Promise<{accessToken: string|null, success: boolean, error: string|null}>}
 */
export const getDropboxClient = async (userId, tenantId) => {
  try {
    // Find Dropbox connection for the user
    const connection = await ExternalStorageConnection.findOne({
      where: {
        user_id: userId,
        tenant_id: tenantId,
        provider: 'dropbox',
        status: 'active'
      }
    });

    if (!connection) {
      return {
        accessToken: null,
        success: false,
        error: 'Dropbox connection not found or inactive'
      };
    }

    // Check if token is expired and needs refresh
    if (connection.token_expires_at && new Date(connection.token_expires_at) <= new Date()) {
      try {
        // Token is expired, need to refresh
        const response = await axios.post('https://api.dropboxapi.com/oauth2/token', 
          new URLSearchParams({
            grant_type: 'refresh_token',
            refresh_token: connection.refresh_token,
            client_id: process.env.DROPBOX_APP_KEY,
            client_secret: process.env.DROPBOX_APP_SECRET
          }), {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded'
            }
          }
        );

        // Calculate new expiration date
        const expiresAt = new Date();
        expiresAt.setSeconds(expiresAt.getSeconds() + response.data.expires_in);

        // Update connection with new tokens
        await connection.update({
          access_token: response.data.access_token,
          token_expires_at: expiresAt,
          updated_at: new Date()
        });

        return {
          accessToken: response.data.access_token,
          success: true,
          error: null
        };
      } catch (refreshError) {
        console.error('Error refreshing Dropbox token:', refreshError);

        // Update connection status to error
        await connection.update({
          status: 'error',
          updated_at: new Date()
        });

        return {
          accessToken: null,
          success: false,
          error: 'Failed to refresh Dropbox token'
        };
      }
    }

    // Token is still valid
    return {
      accessToken: connection.access_token,
      success: true,
      error: null
    };
  } catch (error) {
    console.error('Error getting Dropbox client:', error);
    return {
      accessToken: null,
      success: false,
      error: error.message
    };
  }
};

/**
 * List files from Google Drive
 * @param {string} userId - The user ID
 * @param {string} tenantId - The tenant ID
 * @param {string} folderId - The Google Drive folder ID (optional)
 * @param {number} pageSize - Number of files to return
 * @param {string} pageToken - Token for pagination
 * @returns {Promise<{files: Array, nextPageToken: string|null, success: boolean, error: string|null}>}
 */
export const listGoogleDriveFiles = async (userId, tenantId, folderId = 'root', pageSize = 100, pageToken = null) => {
  try {
    const { drive, success, error } = await getGoogleDriveClient(userId, tenantId);

    if (!success) {
      return {
        files: [],
        nextPageToken: null,
        success: false,
        error
      };
    }

    // Build query
    let query = "trashed = false";
    if (folderId !== 'root') {
      query += ` and '${folderId}' in parents`;
    } else {
      query += " and 'root' in parents";
    }

    // List files
    const response = await drive.files.list({
      q: query,
      pageSize: pageSize,
      pageToken: pageToken,
      fields: 'nextPageToken, files(id, name, mimeType, size, modifiedTime, webViewLink, iconLink, thumbnailLink)'
    });

    return {
      files: response.data.files,
      nextPageToken: response.data.nextPageToken || null,
      success: true,
      error: null
    };
  } catch (error) {
    console.error('Error listing Google Drive files:', error);
    return {
      files: [],
      nextPageToken: null,
      success: false,
      error: error.message
    };
  }
};

/**
 * List files from Dropbox
 * @param {string} userId - The user ID
 * @param {string} tenantId - The tenant ID
 * @param {string} path - The Dropbox folder path (optional)
 * @param {number} limit - Number of files to return
 * @param {string} cursor - Cursor for pagination
 * @returns {Promise<{files: Array, cursor: string|null, hasMore: boolean, success: boolean, error: string|null}>}
 */
export const listDropboxFiles = async (userId, tenantId, path = '', limit = 100, cursor = null) => {
  try {
    const { accessToken, success, error } = await getDropboxClient(userId, tenantId);

    if (!success) {
      return {
        files: [],
        cursor: null,
        hasMore: false,
        success: false,
        error
      };
    }

    // List files
    let response;
    if (cursor) {
      response = await axios.post('https://api.dropboxapi.com/2/files/list_folder/continue', 
        { cursor },
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
    } else {
      response = await axios.post('https://api.dropboxapi.com/2/files/list_folder', 
        {
          path: path || '',
          recursive: false,
          include_media_info: true,
          include_deleted: false,
          include_has_explicit_shared_members: true,
          limit
        },
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // Transform response to a more usable format
    const files = response.data.entries.map(entry => ({
      id: entry.id,
      name: entry.name,
      path: entry.path_display,
      mimeType: entry['.tag'] === 'folder' ? 'folder' : entry.media_info?.metadata?.mime_type || 'application/octet-stream',
      size: entry.size || 0,
      modifiedTime: entry.server_modified,
      isFolder: entry['.tag'] === 'folder'
    }));

    return {
      files,
      cursor: response.data.cursor,
      hasMore: response.data.has_more,
      success: true,
      error: null
    };
  } catch (error) {
    console.error('Error listing Dropbox files:', error);
    return {
      files: [],
      cursor: null,
      hasMore: false,
      success: false,
      error: error.message
    };
  }
};

/**
 * Get file metadata from Google Drive
 * @param {string} userId - The user ID
 * @param {string} tenantId - The tenant ID
 * @param {string} fileId - The Google Drive file ID
 * @returns {Promise<{file: Object|null, success: boolean, error: string|null}>}
 */
export const getGoogleDriveFileMetadata = async (userId, tenantId, fileId) => {
  try {
    const { drive, success, error } = await getGoogleDriveClient(userId, tenantId);

    if (!success) {
      return {
        file: null,
        success: false,
        error
      };
    }

    // Get file metadata
    const response = await drive.files.get({
      fileId,
      fields: 'id, name, mimeType, size, modifiedTime, webViewLink, iconLink, thumbnailLink'
    });

    return {
      file: response.data,
      success: true,
      error: null
    };
  } catch (error) {
    console.error('Error getting Google Drive file metadata:', error);
    return {
      file: null,
      success: false,
      error: error.message
    };
  }
};

/**
 * Get file metadata from Dropbox
 * @param {string} userId - The user ID
 * @param {string} tenantId - The tenant ID
 * @param {string} path - The Dropbox file path
 * @returns {Promise<{file: Object|null, success: boolean, error: string|null}>}
 */
export const getDropboxFileMetadata = async (userId, tenantId, path) => {
  try {
    const { accessToken, success, error } = await getDropboxClient(userId, tenantId);

    if (!success) {
      return {
        file: null,
        success: false,
        error
      };
    }

    // Get file metadata
    const response = await axios.post('https://api.dropboxapi.com/2/files/get_metadata', 
      {
        path,
        include_media_info: true,
        include_deleted: false,
        include_has_explicit_shared_members: true
      },
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    // Transform response to a more usable format
    const file = {
      id: response.data.id,
      name: response.data.name,
      path: response.data.path_display,
      mimeType: response.data['.tag'] === 'folder' ? 'folder' : response.data.media_info?.metadata?.mime_type || 'application/octet-stream',
      size: response.data.size || 0,
      modifiedTime: response.data.server_modified,
      isFolder: response.data['.tag'] === 'folder'
    };

    return {
      file,
      success: true,
      error: null
    };
  } catch (error) {
    console.error('Error getting Dropbox file metadata:', error);
    return {
      file: null,
      success: false,
      error: error.message
    };
  }
};

/**
 * Download file from Google Drive
 * @param {string} userId - The user ID
 * @param {string} tenantId - The tenant ID
 * @param {string} fileId - The Google Drive file ID
 * @returns {Promise<{buffer: Buffer|null, mimeType: string|null, success: boolean, error: string|null}>}
 */
export const downloadGoogleDriveFile = async (userId, tenantId, fileId) => {
  try {
    const { drive, success, error } = await getGoogleDriveClient(userId, tenantId);

    if (!success) {
      return {
        buffer: null,
        mimeType: null,
        success: false,
        error
      };
    }

    // Get file metadata to get the MIME type
    const metaResponse = await drive.files.get({
      fileId,
      fields: 'mimeType, name'
    });

    // Download file
    const response = await drive.files.get({
      fileId,
      alt: 'media'
    }, {
      responseType: 'arraybuffer'
    });

    return {
      buffer: Buffer.from(response.data),
      mimeType: metaResponse.data.mimeType,
      fileName: metaResponse.data.name,
      success: true,
      error: null
    };
  } catch (error) {
    console.error('Error downloading Google Drive file:', error);
    return {
      buffer: null,
      mimeType: null,
      fileName: null,
      success: false,
      error: error.message
    };
  }
};

/**
 * Download file from Dropbox
 * @param {string} userId - The user ID
 * @param {string} tenantId - The tenant ID
 * @param {string} path - The Dropbox file path
 * @returns {Promise<{buffer: Buffer|null, mimeType: string|null, success: boolean, error: string|null}>}
 */
export const downloadDropboxFile = async (userId, tenantId, path) => {
  try {
    const { accessToken, success, error } = await getDropboxClient(userId, tenantId);

    if (!success) {
      return {
        buffer: null,
        mimeType: null,
        fileName: null,
        success: false,
        error
      };
    }

    // Get file metadata to get the MIME type
    const metaResponse = await axios.post('https://api.dropboxapi.com/2/files/get_metadata', 
      { path },
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    // Download file
    const response = await axios.post('https://content.dropboxapi.com/2/files/download', 
      null,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Dropbox-API-Arg': JSON.stringify({ path })
        },
        responseType: 'arraybuffer'
      }
    );

    return {
      buffer: Buffer.from(response.data),
      mimeType: metaResponse.data.media_info?.metadata?.mime_type || 'application/octet-stream',
      fileName: metaResponse.data.name,
      success: true,
      error: null
    };
  } catch (error) {
    console.error('Error downloading Dropbox file:', error);
    return {
      buffer: null,
      mimeType: null,
      fileName: null,
      success: false,
      error: error.message
    };
  }
};

/**
 * Link an external file to the document system
 * @param {string} userId - The user ID
 * @param {string} tenantId - The tenant ID
 * @param {string} source - The source ('google_drive' or 'dropbox')
 * @param {string} externalId - The external file ID
 * @param {string} externalPath - The external file path (for Dropbox)
 * @param {Object} metadata - The file metadata
 * @param {string} folderId - The document folder ID (optional)
 * @returns {Promise<{document: Object|null, success: boolean, error: string|null}>}
 */
export const linkExternalFile = async (userId, tenantId, source, externalId, externalPath, metadata, folderId = null) => {
  try {
    // Create document record
    const document = await Document.create({
      name: metadata.name,
      description: metadata.description || '',
      file_path: source === 'google_drive' ? externalId : externalPath,
      file_size: metadata.size || 0,
      file_type: path.extname(metadata.name).substring(1) || 'unknown',
      mime_type: metadata.mimeType || 'application/octet-stream',
      is_external: true,
      external_source: source,
      external_id: externalId,
      folder_id: folderId,
      tenant_id: tenantId,
      uploaded_by: userId,
      created_at: new Date(),
      updated_at: new Date()
    });

    // Update storage usage (count only, no bytes for external files)
    await updateStorageUsage(tenantId, 0, true, 1);

    return {
      document,
      success: true,
      error: null
    };
  } catch (error) {
    console.error('Error linking external file:', error);
    return {
      document: null,
      success: false,
      error: error.message
    };
  }
};

/**
 * Import an external file to the local system
 * @param {string} userId - The user ID
 * @param {string} tenantId - The tenant ID
 * @param {string} documentId - The document ID
 * @returns {Promise<{document: Object|null, success: boolean, error: string|null}>}
 */
export const importExternalFile = async (userId, tenantId, documentId) => {
  try {
    // Get document
    const document = await Document.findOne({
      where: {
        id: documentId,
        tenant_id: tenantId,
        is_external: true
      }
    });

    if (!document) {
      return {
        document: null,
        success: false,
        error: 'Document not found or not external'
      };
    }

    // Download file from external source
    let fileData;
    if (document.external_source === 'google_drive') {
      fileData = await downloadGoogleDriveFile(userId, tenantId, document.external_id);
    } else if (document.external_source === 'dropbox') {
      fileData = await downloadDropboxFile(userId, tenantId, document.file_path);
    } else {
      return {
        document: null,
        success: false,
        error: 'Unsupported external source'
      };
    }

    if (!fileData.success) {
      return {
        document: null,
        success: false,
        error: fileData.error
      };
    }

    // Generate storage path
    const storagePath = generateStoragePath(tenantId, userId, document.name);

    // Save file
    const fullPath = await saveFile(fileData.buffer, storagePath);

    // Update document record
    const oldSize = document.file_size;
    await document.update({
      file_path: storagePath,
      file_size: fileData.buffer.length,
      is_external: false,
      external_source: null,
      external_id: null,
      updated_at: new Date()
    });

    // Update storage usage
    await updateStorageUsage(
      tenantId, 
      fileData.buffer.length, // bytes added
      false, // not external anymore
      0 // no change in document count
    );

    return {
      document,
      success: true,
      error: null
    };
  } catch (error) {
    console.error('Error importing external file:', error);
    return {
      document: null,
      success: false,
      error: error.message
    };
  }
};

/**
 * Bulk import external files to the local system
 * @param {string} userId - The user ID
 * @param {string} tenantId - The tenant ID
 * @param {Array<string>} documentIds - Array of document IDs to import
 * @returns {Promise<{results: Array, success: boolean, error: string|null}>}
 */
export const bulkImportExternalFiles = async (userId, tenantId, documentIds) => {
  const results = [];
  let overallSuccess = true;

  for (const documentId of documentIds) {
    const result = await importExternalFile(userId, tenantId, documentId);
    results.push({
      documentId,
      success: result.success,
      error: result.error
    });

    if (!result.success) {
      overallSuccess = false;
    }
  }

  return {
    results,
    success: overallSuccess,
    error: overallSuccess ? null : 'Some files failed to import'
  };
};

export default {
  getGoogleDriveClient,
  getDropboxClient,
  listGoogleDriveFiles,
  listDropboxFiles,
  getGoogleDriveFileMetadata,
  getDropboxFileMetadata,
  downloadGoogleDriveFile,
  downloadDropboxFile,
  linkExternalFile,
  importExternalFile,
  bulkImportExternalFiles
};
