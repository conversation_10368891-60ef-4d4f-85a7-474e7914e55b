// AI Testing Functionality Test Script

const axios = require('axios');
const assert = require('assert');

// Configuration
const API_URL = process.env.API_URL || 'http://localhost:3000';
const TOKEN = process.env.TOKEN; // Set this to a valid global admin token

// We need a configuration ID for testing
let configurationId;
let modelId;

// Helper function to make authenticated requests
const makeRequest = async (method, endpoint, data = null) => {
  try {
    const config = {
      headers: {
        Authorization: `Bearer ${TOKEN}`
      }
    };

    let response;
    if (method === 'GET') {
      response = await axios.get(`${API_URL}${endpoint}`, config);
    } else if (method === 'POST') {
      response = await axios.post(`${API_URL}${endpoint}`, data, config);
    } else if (method === 'PUT') {
      response = await axios.put(`${API_URL}${endpoint}`, data, config);
    } else if (method === 'DELETE') {
      response = await axios.delete(`${API_URL}${endpoint}`, config);
    }

    return response.data;
  } catch (error) {
    console.error(`Error making ${method} request to ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
};

// Setup: Get a configuration for testing
const setupConfiguration = async () => {
  console.log('Setting up a configuration for testing');
  
  // Try to get existing configurations
  const configurations = await makeRequest('GET', '/api/ai-configuration/configurations');
  
  if (configurations.length > 0) {
    // Find a configuration with an API key
    const configWithKey = configurations.find(config => config.api_key || config.api_key_encrypted);
    
    if (configWithKey) {
      configurationId = configWithKey.id;
      console.log(`Using existing configuration with ID: ${configurationId}`);
      
      // Get the default model ID if available
      if (configWithKey.default_model_id) {
        modelId = configWithKey.default_model_id;
        console.log(`Using default model with ID: ${modelId}`);
      } else {
        // Try to get a model for this provider
        const models = await makeRequest('GET', `/api/ai-configuration/providers/${configWithKey.provider_id}/models`);
        if (models.length > 0) {
          modelId = models[0].id;
          console.log(`Using model with ID: ${modelId}`);
        }
      }
      
      return;
    }
  }
  
  console.log('No suitable configuration found for testing. Please create a configuration with an API key.');
  throw new Error('No suitable configuration found for testing');
};

// Test functions
const testConfigurationWithDefaultModel = async () => {
  console.log('Testing POST /api/ai-configuration/test with default model');
  
  const testData = {
    configuration_id: configurationId,
    prompt: 'Hello, how are you today?'
  };
  
  const result = await makeRequest('POST', '/api/ai-configuration/test', testData);
  
  assert(result.response, 'Response should have a response field');
  assert(result.model, 'Response should have a model field');
  assert(result.provider, 'Response should have a provider field');
  
  console.log(`Got response: "${result.response.substring(0, 50)}..."`);
  console.log(`From model: ${result.model}`);
  console.log(`From provider: ${result.provider}`);
  
  return result;
};

const testConfigurationWithSpecificModel = async () => {
  // Skip if we don't have a model ID
  if (!modelId) {
    console.log('Skipping test with specific model as no model ID is available');
    return null;
  }
  
  console.log('Testing POST /api/ai-configuration/test with specific model');
  
  const testData = {
    configuration_id: configurationId,
    model_id: modelId,
    prompt: 'What is the capital of France?'
  };
  
  const result = await makeRequest('POST', '/api/ai-configuration/test', testData);
  
  assert(result.response, 'Response should have a response field');
  assert(result.model, 'Response should have a model field');
  assert(result.provider, 'Response should have a provider field');
  
  console.log(`Got response: "${result.response.substring(0, 50)}..."`);
  console.log(`From model: ${result.model}`);
  console.log(`From provider: ${result.provider}`);
  
  return result;
};

const testConfigurationWithLongPrompt = async () => {
  console.log('Testing POST /api/ai-configuration/test with a longer prompt');
  
  const longPrompt = `
    Please analyze the following crop rotation scenario:
    Field: North Field, 50 acres
    Previous crops: 
    - 2020: Corn
    - 2021: Soybeans
    - 2022: Wheat
    Soil type: Loamy
    Climate: Temperate with moderate rainfall
    
    What would be the best crop to plant next year and why?
  `;
  
  const testData = {
    configuration_id: configurationId,
    prompt: longPrompt
  };
  
  const result = await makeRequest('POST', '/api/ai-configuration/test', testData);
  
  assert(result.response, 'Response should have a response field');
  assert(result.model, 'Response should have a model field');
  assert(result.provider, 'Response should have a provider field');
  
  console.log(`Got response: "${result.response.substring(0, 50)}..."`);
  console.log(`From model: ${result.model}`);
  console.log(`From provider: ${result.provider}`);
  
  return result;
};

// Run all tests
const runTests = async () => {
  try {
    console.log('Starting AI Testing functionality tests...');
    
    // Setup: Get a configuration
    await setupConfiguration();
    
    // Test with default model
    await testConfigurationWithDefaultModel();
    
    // Test with specific model
    await testConfigurationWithSpecificModel();
    
    // Test with longer prompt
    await testConfigurationWithLongPrompt();
    
    console.log('All tests passed!');
  } catch (error) {
    console.error('Test failed:', error);
    process.exit(1);
  }
};

// Run the tests if this file is executed directly
if (require.main === module) {
  if (!TOKEN) {
    console.error('Please set the TOKEN environment variable to a valid global admin token');
    process.exit(1);
  }
  
  runTests();
}

module.exports = {
  runTests,
  testConfigurationWithDefaultModel,
  testConfigurationWithSpecificModel,
  testConfigurationWithLongPrompt,
  setupConfiguration
};