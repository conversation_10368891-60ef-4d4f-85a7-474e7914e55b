import NotificationPreference from '../models/NotificationPreference.js';

/**
 * Controller for handling notification preferences
 */
const notificationController = {
  /**
   * Get notification preferences for the current user
   */
  getPreferences: async (req, res) => {
    try {
      const userId = req.user.id;

      // Find or create notification preferences for the user
      let preferences = await NotificationPreference.findByUserId(userId);

      // If preferences don't exist, create with default values
      if (!preferences) {
        preferences = await NotificationPreference.create({
          user_id: userId,
          enable_in_app: true,
          enable_email: true,
          chat_message_notifications: true,
          task_notifications: true,
          document_notifications: true,
          system_notifications: true
        });
      }

      // Return preferences in the format expected by the frontend
      return res.status(200).json({
        enableInApp: preferences.enable_in_app,
        enableEmail: preferences.enable_email,
        chatMessageNotifications: preferences.chat_message_notifications,
        taskNotifications: preferences.task_notifications,
        documentNotifications: preferences.document_notifications,
        systemNotifications: preferences.system_notifications
      });
    } catch (error) {
      console.error('Error getting notification preferences:', error);
      return res.status(500).json({ error: 'Failed to get notification preferences' });
    }
  },

  /**
   * Update notification preferences for the current user
   */
  updatePreferences: async (req, res) => {
    try {
      const userId = req.user.id;
      const {
        enableInApp,
        enableEmail,
        chatMessageNotifications,
        taskNotifications,
        documentNotifications,
        systemNotifications
      } = req.body;

      // Convert frontend camelCase to database snake_case
      const preferences = {
        enable_in_app: enableInApp,
        enable_email: enableEmail,
        chat_message_notifications: chatMessageNotifications,
        task_notifications: taskNotifications,
        document_notifications: documentNotifications,
        system_notifications: systemNotifications
      };

      // Create or update notification preferences
      const updatedPreferences = await NotificationPreference.createOrUpdate(userId, preferences);

      // Return updated preferences in the format expected by the frontend
      return res.status(200).json({
        enableInApp: updatedPreferences.enable_in_app,
        enableEmail: updatedPreferences.enable_email,
        chatMessageNotifications: updatedPreferences.chat_message_notifications,
        taskNotifications: updatedPreferences.task_notifications,
        documentNotifications: updatedPreferences.document_notifications,
        systemNotifications: updatedPreferences.system_notifications
      });
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      return res.status(500).json({ error: 'Failed to update notification preferences' });
    }
  }
};

export default notificationController;