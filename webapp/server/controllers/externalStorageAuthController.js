import { google } from 'googleapis';
import axios from 'axios';
import { sequelize } from '../config/database.js';
import ExternalStorageConnection from '../models/ExternalStorageConnection.js';
import dotenv from 'dotenv';

dotenv.config();

// Google Drive OAuth configuration
const googleOAuth2Client = new google.auth.OAuth2(
  process.env.GOOGLE_CLIENT_ID,
  process.env.GOOGLE_CLIENT_SECRET,
  process.env.GOOGLE_REDIRECT_URI
);

// Generate authorization URL for Google Drive OAuth
export const getGoogleDriveAuthUrl = async (req, res) => {
  try {
    const { userId, tenantId, farmId } = req.query;
    const effectiveTenantId = tenantId || farmId;

    if (!userId || !effectiveTenantId) {
      return res.status(400).json({ error: 'User ID and Tenant ID are required' });
    }

    // Generate authorization URL
    const authUrl = googleOAuth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: [
        'https://www.googleapis.com/auth/drive.readonly',
        'https://www.googleapis.com/auth/userinfo.email',
        'https://www.googleapis.com/auth/userinfo.profile'
      ],
      prompt: 'consent', // Force to get refresh token
      state: JSON.stringify({ userId, tenantId: effectiveTenantId })
    });

    return res.json({ authUrl });
  } catch (error) {
    console.error('Error generating Google Drive authorization URL:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Handle OAuth callback from Google Drive
export const handleGoogleDriveCallback = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { code, state } = req.query;

    if (!code || !state) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    // Parse state to get user ID and tenant ID
    const { userId, tenantId } = JSON.parse(state);

    // Exchange authorization code for tokens
    const { tokens } = await googleOAuth2Client.getToken(code);
    const { access_token, refresh_token, expiry_date } = tokens;

    // Get user info from Google
    const userInfoResponse = await axios.get('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        Authorization: `Bearer ${access_token}`
      }
    });

    const { id: providerUserId, email: providerEmail, name: providerDisplayName } = userInfoResponse.data;

    // Check if connection already exists
    let connection = await ExternalStorageConnection.findOne({
      where: {
        user_id: userId,
        tenant_id: tenantId,
        provider: 'google_drive'
      },
      transaction
    });

    if (connection) {
      // Update existing connection
      await connection.update({
        access_token,
        refresh_token: refresh_token || connection.refresh_token, // Keep old refresh token if new one is not provided
        token_expires_at: expiry_date ? new Date(expiry_date) : null,
        provider_user_id: providerUserId,
        provider_email: providerEmail,
        provider_display_name: providerDisplayName,
        status: 'active',
        updated_at: new Date()
      }, { transaction });
    } else {
      // Create new connection
      connection = await ExternalStorageConnection.create({
        user_id: userId,
        tenant_id: tenantId,
        provider: 'google_drive',
        access_token,
        refresh_token,
        token_expires_at: expiry_date ? new Date(expiry_date) : null,
        provider_user_id: providerUserId,
        provider_email: providerEmail,
        provider_display_name: providerDisplayName,
        status: 'active',
        created_at: new Date(),
        updated_at: new Date()
      }, { transaction });
    }

    await transaction.commit();

    // Redirect to success page
    return res.redirect(`/documents/external?provider=google_drive&success=true`);
  } catch (error) {
    await transaction.rollback();
    console.error('Error handling Google Drive callback:', error);
    return res.redirect(`/documents/external?provider=google_drive&success=false&error=${encodeURIComponent(error.message)}`);
  }
};

// Generate authorization URL for Dropbox OAuth
export const getDropboxAuthUrl = async (req, res) => {
  try {
    const { userId, tenantId, farmId } = req.query;
    const effectiveTenantId = tenantId || farmId;

    if (!userId || !effectiveTenantId) {
      return res.status(400).json({ error: 'User ID and Tenant ID are required' });
    }

    // Generate authorization URL
    const authUrl = `https://www.dropbox.com/oauth2/authorize?client_id=${
      process.env.DROPBOX_APP_KEY
    }&response_type=code&redirect_uri=${
      encodeURIComponent(process.env.DROPBOX_REDIRECT_URI)
    }&state=${
      encodeURIComponent(JSON.stringify({ userId, tenantId: effectiveTenantId }))
    }`;

    return res.json({ authUrl });
  } catch (error) {
    console.error('Error generating Dropbox authorization URL:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Handle OAuth callback from Dropbox
export const handleDropboxCallback = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { code, state } = req.query;

    if (!code || !state) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    // Parse state to get user ID and tenant ID
    const { userId, tenantId } = JSON.parse(state);

    // Exchange authorization code for tokens
    const tokenResponse = await axios.post('https://api.dropboxapi.com/oauth2/token', 
      new URLSearchParams({
        code,
        grant_type: 'authorization_code',
        redirect_uri: process.env.DROPBOX_REDIRECT_URI,
        client_id: process.env.DROPBOX_APP_KEY,
        client_secret: process.env.DROPBOX_APP_SECRET
      }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );

    const { access_token, refresh_token, expires_in } = tokenResponse.data;

    // Calculate token expiration date
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + expires_in);

    // Get user info from Dropbox
    const userInfoResponse = await axios.post('https://api.dropboxapi.com/2/users/get_current_account', 
      null, {
        headers: {
          'Authorization': `Bearer ${access_token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    const { account_id: providerUserId, email: providerEmail, name: { display_name: providerDisplayName } } = userInfoResponse.data;

    // Check if connection already exists
    let connection = await ExternalStorageConnection.findOne({
      where: {
        user_id: userId,
        tenant_id: tenantId,
        provider: 'dropbox'
      },
      transaction
    });

    if (connection) {
      // Update existing connection
      await connection.update({
        access_token,
        refresh_token: refresh_token || connection.refresh_token, // Keep old refresh token if new one is not provided
        token_expires_at: expiresAt,
        provider_user_id: providerUserId,
        provider_email: providerEmail,
        provider_display_name: providerDisplayName,
        status: 'active',
        updated_at: new Date()
      }, { transaction });
    } else {
      // Create new connection
      connection = await ExternalStorageConnection.create({
        user_id: userId,
        tenant_id: tenantId,
        provider: 'dropbox',
        access_token,
        refresh_token,
        token_expires_at: expiresAt,
        provider_user_id: providerUserId,
        provider_email: providerEmail,
        provider_display_name: providerDisplayName,
        status: 'active',
        created_at: new Date(),
        updated_at: new Date()
      }, { transaction });
    }

    await transaction.commit();

    // Redirect to success page
    return res.redirect(`/documents/external?provider=dropbox&success=true`);
  } catch (error) {
    await transaction.rollback();
    console.error('Error handling Dropbox callback:', error);
    return res.redirect(`/documents/external?provider=dropbox&success=false&error=${encodeURIComponent(error.message)}`);
  }
};

// Get user's external storage connections
export const getUserConnections = async (req, res) => {
  try {
    const { userId, tenantId, farmId } = req.params;
    const effectiveTenantId = tenantId || farmId;

    if (!userId || !effectiveTenantId) {
      return res.status(400).json({ error: 'User ID and Tenant ID are required' });
    }

    // Get connections
    const connections = await ExternalStorageConnection.findAll({
      where: {
        user_id: userId,
        tenant_id: effectiveTenantId,
        status: 'active'
      },
      attributes: [
        'id', 'provider', 'provider_email', 'provider_display_name', 
        'status', 'last_sync_at', 'created_at', 'updated_at'
      ]
    });

    return res.json(connections);
  } catch (error) {
    console.error('Error getting user connections:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Disconnect external storage
export const disconnectExternalStorage = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { connectionId } = req.params;

    // Find connection
    const connection = await ExternalStorageConnection.findByPk(connectionId);

    if (!connection) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Connection not found' });
    }

    // Delete connection
    await connection.destroy({ transaction });

    await transaction.commit();

    return res.json({
      success: true,
      message: `${connection.provider === 'google_drive' ? 'Google Drive' : 'Dropbox'} disconnected successfully`
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error disconnecting external storage:', error);
    return res.status(500).json({ error: error.message });
  }
};
