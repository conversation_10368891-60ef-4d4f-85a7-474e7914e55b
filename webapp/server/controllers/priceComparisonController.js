import PriceComparison from '../models/PriceComparison.js';
import Farm from '../models/Farm.js';
import Product from '../models/Product.js';
import Supplier from '../models/Supplier.js';
import { sequelize } from '../config/database.js';

// Get all price comparisons (for global admin)
export const getAllPriceComparisons = async (req, res) => {
  try {
    const priceComparisons = await PriceComparison.findAll({
      include: [
        {
          model: Farm,
          as: 'priceComparisonFarm',
          attributes: ['id', 'name']
        },
        {
          model: Product,
          as: 'priceComparisonProduct',
          attributes: ['id', 'name', 'type']
        },
        {
          model: Supplier,
          as: 'bestPriceSupplier',
          attributes: ['id', 'name']
        }
      ],
      order: [['comparison_date', 'DESC']]
    });

    res.status(200).json(priceComparisons);
  } catch (error) {
    console.error('Error fetching all price comparisons:', error);
    res.status(500).json({ message: 'Failed to fetch price comparisons', error: error.message });
  }
};

// Get all price comparisons for a farm
export const getPriceComparisons = async (req, res) => {
  try {
    const { farmId } = req.query;

    // If farmId is not provided, fall back to getting all price comparisons
    if (!farmId) {
      return getAllPriceComparisons(req, res);
    }

    const priceComparisons = await PriceComparison.findAll({
      where: {
        farm_id: farmId
      },
      include: [
        {
          model: Product,
          as: 'priceComparisonProduct',
          attributes: ['id', 'name', 'type']
        },
        {
          model: Supplier,
          as: 'bestPriceSupplier',
          attributes: ['id', 'name']
        }
      ],
      order: [['comparison_date', 'DESC']]
    });

    res.status(200).json(priceComparisons);
  } catch (error) {
    console.error('Error fetching price comparisons:', error);
    res.status(500).json({ message: 'Failed to fetch price comparisons', error: error.message });
  }
};

// Get a single price comparison by ID
export const getPriceComparison = async (req, res) => {
  try {
    const { id } = req.params;

    const priceComparison = await PriceComparison.findByPk(id, {
      include: [
        {
          model: Farm,
          as: 'priceComparisonFarm',
          attributes: ['id', 'name']
        },
        {
          model: Product,
          as: 'priceComparisonProduct',
          attributes: ['id', 'name', 'description', 'type', 'price', 'unit']
        },
        {
          model: Supplier,
          as: 'bestPriceSupplier',
          attributes: ['id', 'name', 'contact_name', 'email', 'phone']
        }
      ]
    });

    if (!priceComparison) {
      return res.status(404).json({ message: 'Price comparison not found' });
    }

    res.status(200).json(priceComparison);
  } catch (error) {
    console.error('Error fetching price comparison:', error);
    res.status(500).json({ message: 'Failed to fetch price comparison', error: error.message });
  }
};

// Create a new price comparison
export const createPriceComparison = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const {
      farm_id,
      product_id,
      product_name,
      product_category,
      unit,
      comparison_date,
      price_data,
      best_price,
      best_price_supplier_id,
      price_range,
      average_price,
      notes,
      is_active
    } = req.body;

    if (!farm_id || !product_name || !price_data) {
      await transaction.rollback();
      return res.status(400).json({ message: 'Farm ID, product name, and price data are required' });
    }

    // Calculate best price, price range, and average price if not provided
    let calculatedBestPrice = best_price;
    let calculatedBestPriceSupplier = best_price_supplier_id;
    let calculatedPriceRange = price_range;
    let calculatedAveragePrice = average_price;

    if (price_data && Array.isArray(price_data) && price_data.length > 0) {
      // If price_data is provided but best_price is not, calculate it
      if (calculatedBestPrice === undefined) {
        const prices = price_data.map(item => parseFloat(item.price));
        calculatedBestPrice = Math.min(...prices);
        
        // Find the supplier with the best price
        const bestPriceItem = price_data.find(item => parseFloat(item.price) === calculatedBestPrice);
        if (bestPriceItem && bestPriceItem.supplier_id) {
          calculatedBestPriceSupplier = bestPriceItem.supplier_id;
        }
      }

      // If price_range is not provided, calculate it
      if (calculatedPriceRange === undefined) {
        const prices = price_data.map(item => parseFloat(item.price));
        calculatedPriceRange = Math.max(...prices) - Math.min(...prices);
      }

      // If average_price is not provided, calculate it
      if (calculatedAveragePrice === undefined) {
        const prices = price_data.map(item => parseFloat(item.price));
        calculatedAveragePrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;
      }
    }

    const newPriceComparison = await PriceComparison.create({
      farm_id,
      product_id,
      product_name,
      product_category,
      unit,
      comparison_date: comparison_date || new Date(),
      price_data,
      best_price: calculatedBestPrice,
      best_price_supplier_id: calculatedBestPriceSupplier,
      price_range: calculatedPriceRange,
      average_price: calculatedAveragePrice,
      notes,
      is_active: is_active !== undefined ? is_active : true
    }, { transaction });

    await transaction.commit();

    // Return the created price comparison with related data
    const result = await PriceComparison.findByPk(newPriceComparison.id, {
      include: [
        {
          model: Farm,
          as: 'priceComparisonFarm',
          attributes: ['id', 'name']
        },
        {
          model: Product,
          as: 'priceComparisonProduct',
          attributes: ['id', 'name', 'description', 'type', 'price', 'unit']
        },
        {
          model: Supplier,
          as: 'bestPriceSupplier',
          attributes: ['id', 'name', 'contact_name', 'email', 'phone']
        }
      ]
    });

    res.status(201).json(result);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating price comparison:', error);
    res.status(500).json({ message: 'Failed to create price comparison', error: error.message });
  }
};

// Update a price comparison
export const updatePriceComparison = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const {
      product_id,
      product_name,
      product_category,
      unit,
      comparison_date,
      price_data,
      best_price,
      best_price_supplier_id,
      price_range,
      average_price,
      notes,
      is_active
    } = req.body;

    const priceComparison = await PriceComparison.findByPk(id, { transaction });

    if (!priceComparison) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Price comparison not found' });
    }

    // Calculate best price, price range, and average price if price_data is updated
    let calculatedBestPrice = best_price;
    let calculatedBestPriceSupplier = best_price_supplier_id;
    let calculatedPriceRange = price_range;
    let calculatedAveragePrice = average_price;

    if (price_data && Array.isArray(price_data) && price_data.length > 0) {
      // If price_data is provided but best_price is not, calculate it
      if (calculatedBestPrice === undefined) {
        const prices = price_data.map(item => parseFloat(item.price));
        calculatedBestPrice = Math.min(...prices);
        
        // Find the supplier with the best price
        const bestPriceItem = price_data.find(item => parseFloat(item.price) === calculatedBestPrice);
        if (bestPriceItem && bestPriceItem.supplier_id) {
          calculatedBestPriceSupplier = bestPriceItem.supplier_id;
        }
      }

      // If price_range is not provided, calculate it
      if (calculatedPriceRange === undefined) {
        const prices = price_data.map(item => parseFloat(item.price));
        calculatedPriceRange = Math.max(...prices) - Math.min(...prices);
      }

      // If average_price is not provided, calculate it
      if (calculatedAveragePrice === undefined) {
        const prices = price_data.map(item => parseFloat(item.price));
        calculatedAveragePrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;
      }
    }

    // Update price comparison fields
    if (product_id !== undefined) priceComparison.product_id = product_id;
    if (product_name !== undefined) priceComparison.product_name = product_name;
    if (product_category !== undefined) priceComparison.product_category = product_category;
    if (unit !== undefined) priceComparison.unit = unit;
    if (comparison_date !== undefined) priceComparison.comparison_date = comparison_date;
    if (price_data !== undefined) priceComparison.price_data = price_data;
    if (calculatedBestPrice !== undefined) priceComparison.best_price = calculatedBestPrice;
    if (calculatedBestPriceSupplier !== undefined) priceComparison.best_price_supplier_id = calculatedBestPriceSupplier;
    if (calculatedPriceRange !== undefined) priceComparison.price_range = calculatedPriceRange;
    if (calculatedAveragePrice !== undefined) priceComparison.average_price = calculatedAveragePrice;
    if (notes !== undefined) priceComparison.notes = notes;
    if (is_active !== undefined) priceComparison.is_active = is_active;

    await priceComparison.save({ transaction });
    await transaction.commit();

    // Return the updated price comparison with related data
    const result = await PriceComparison.findByPk(id, {
      include: [
        {
          model: Farm,
          as: 'priceComparisonFarm',
          attributes: ['id', 'name']
        },
        {
          model: Product,
          as: 'priceComparisonProduct',
          attributes: ['id', 'name', 'description', 'type', 'price', 'unit']
        },
        {
          model: Supplier,
          as: 'bestPriceSupplier',
          attributes: ['id', 'name', 'contact_name', 'email', 'phone']
        }
      ]
    });

    res.status(200).json(result);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating price comparison:', error);
    res.status(500).json({ message: 'Failed to update price comparison', error: error.message });
  }
};

// Delete a price comparison
export const deletePriceComparison = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    const priceComparison = await PriceComparison.findByPk(id, { transaction });

    if (!priceComparison) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Price comparison not found' });
    }

    await priceComparison.destroy({ transaction });
    await transaction.commit();

    res.status(200).json({ message: 'Price comparison deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting price comparison:', error);
    res.status(500).json({ message: 'Failed to delete price comparison', error: error.message });
  }
};