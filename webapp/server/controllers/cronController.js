/**
 * Controller for handling cron job endpoints
 * These endpoints are used for scheduled tasks that need to run at regular intervals
 */

import Farm from '../models/Farm.js';
import Field from '../models/Field.js';
import Weather from '../models/Weather.js';
import Grant from '../models/Grant.js';
import ScriptExecution from '../models/ScriptExecution.js';
import { processAlertRules } from './alertController.js';
import { sequelize } from '../config/database.js';
import axios from 'axios';

// National Weather Service API base URL
const NWS_API_BASE = 'https://api.weather.gov';

// API configuration for grants
const GRANTS_GOV_API_URL = process.env.GRANTS_GOV_API_URL || 'https://www.grants.gov/grantsws/rest';
const GRANTS_GOV_API_KEY = process.env.GRANTS_GOV_API_KEY;
const FARMERS_GOV_API_URL = process.env.FARMERS_GOV_API_URL || 'https://www.farmers.gov/api';
const FARMERS_GOV_API_KEY = process.env.FARMERS_GOV_API_KEY;
const USDA_ARMS_API_URL = process.env.USDA_ARMS_API_URL || 'https://api.ers.usda.gov/arms';
const USDA_ARMS_API_KEY = process.env.USDA_ARMS_API_KEY;

/**
 * Middleware to verify the cron job secret key
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const verifyCronSecret = (req, res, next) => {
  const providedSecret = req.headers['x-cron-secret'] || req.query.secret;
  const expectedSecret = process.env.CRON_SECRET_KEY;

  if (!providedSecret || providedSecret !== expectedSecret) {
    return res.status(401).json({ error: 'Unauthorized: Invalid or missing cron secret key' });
  }

  next();
};

/**
 * Process all alert rules for all farms
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const processAllAlerts = async (req, res) => {
  try {
    // Get all farms
    const farms = await Farm.findAll();
    const results = [];

    // Process alert rules for each farm
    for (const farm of farms) {
      console.log(`Processing alert rules for farm: ${farm.id}`);

      try {
        // Create a mock request and response object for the alert processor
        const mockReq = {
          body: { farmId: farm.id }
        };

        let alertResults = null;

        // Create a mock response that captures the results
        const mockRes = {
          status: (code) => ({
            json: (data) => {
              alertResults = data;
            }
          })
        };

        // Call the processAlertRules function
        await processAlertRules(mockReq, mockRes);

        results.push({
          farmId: farm.id,
          farmName: farm.name,
          success: true,
          alertResults
        });
      } catch (error) {
        console.error(`Error processing alert rules for farm ${farm.id}:`, error);
        results.push({
          farmId: farm.id,
          farmName: farm.name,
          success: false,
          error: error.message
        });
      }
    }

    return res.status(200).json({
      message: `Processed alert rules for ${farms.length} farms`,
      results
    });
  } catch (error) {
    console.error('Error in processAllAlerts:', error);
    return res.status(500).json({ error: 'Failed to process alerts', details: error.message });
  }
};

/**
 * Run system maintenance tasks
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const runSystemMaintenance = async (req, res) => {
  try {
    // Placeholder for system maintenance tasks
    // This could include database cleanup, log rotation, etc.

    console.log('Running system maintenance tasks');

    // Example maintenance task: log the current time
    const now = new Date();
    console.log(`System maintenance run at: ${now.toISOString()}`);

    return res.status(200).json({
      message: 'System maintenance completed successfully',
      timestamp: now.toISOString()
    });
  } catch (error) {
    console.error('Error in runSystemMaintenance:', error);
    return res.status(500).json({ error: 'Failed to run system maintenance', details: error.message });
  }
};

/**
 * Health check endpoint for monitoring
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const healthCheck = async (req, res) => {
  try {
    // Return basic health information
    return res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV
    });
  } catch (error) {
    console.error('Error in healthCheck:', error);
    return res.status(500).json({ error: 'Health check failed', details: error.message });
  }
};

/**
 * Fetch and store weather data for all farms
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const fetchAllFarmsWeather = async (req, res) => {
  try {
    // Log script execution start
    const scriptExecution = await ScriptExecution.create({
      name: 'Fetch All Farms Weather',
      description: 'Fetch and store weather data for all farms',
      file_path: 'controllers/cronController.js',
      status: 'running'
    });

    console.log('Starting weather data fetch for all farms');

    // Get all farms
    const farms = await Farm.findAll();
    const results = [];
    let successCount = 0;
    let errorCount = 0;

    // Process each farm
    for (const farm of farms) {
      console.log(`Fetching weather data for farm: ${farm.id} (${farm.name})`);

      try {
        // Get farm location data
        const farmLocation = farm.location_data;
        if (!farmLocation || !farmLocation.latitude || !farmLocation.longitude) {
          console.warn(`Farm ${farm.id} (${farm.name}) has missing or invalid location data`);
          results.push({
            farmId: farm.id,
            farmName: farm.name,
            success: false,
            error: 'Missing or invalid location data'
          });
          errorCount++;
          continue;
        }

        // Fetch weather data
        const weatherData = await fetchWeatherData(farmLocation.latitude, farmLocation.longitude);

        // Store weather data
        await storeWeatherData(weatherData, farm.id, null, farmLocation.latitude, farmLocation.longitude);

        // Get fields for this farm
        const fields = await Field.findAll({ where: { farm_id: farm.id } });

        // Process each field
        for (const field of fields) {
          // Get field location data (center point)
          const fieldLocation = field.location_data;
          if (!fieldLocation || !fieldLocation.center || !fieldLocation.center.latitude || !fieldLocation.center.longitude) {
            console.warn(`Field ${field.id} (${field.name}) has missing or invalid location data`);
            continue;
          }

          // Fetch and store weather data for field
          const fieldWeatherData = await fetchWeatherData(fieldLocation.center.latitude, fieldLocation.center.longitude);
          await storeWeatherData(fieldWeatherData, farm.id, field.id, fieldLocation.center.latitude, fieldLocation.center.longitude);
        }

        results.push({
          farmId: farm.id,
          farmName: farm.name,
          success: true,
          fieldsProcessed: fields.length
        });
        successCount++;
      } catch (error) {
        console.error(`Error fetching weather data for farm ${farm.id}:`, error);
        results.push({
          farmId: farm.id,
          farmName: farm.name,
          success: false,
          error: error.message
        });
        errorCount++;
      }
    }

    // Update script execution record
    await scriptExecution.update({
      executed: true,
      executed_at: new Date(),
      status: 'completed',
      output: JSON.stringify({
        totalFarms: farms.length,
        successCount,
        errorCount,
        results
      })
    });

    return res.status(200).json({
      message: `Processed weather data for ${farms.length} farms (${successCount} successful, ${errorCount} failed)`,
      results
    });
  } catch (error) {
    console.error('Error in fetchAllFarmsWeather:', error);

    // Update script execution record with error
    try {
      await ScriptExecution.update(
        {
          executed: true,
          executed_at: new Date(),
          status: 'failed',
          error_message: error.message
        },
        {
          where: {
            name: 'Fetch All Farms Weather',
            status: 'running'
          }
        }
      );
    } catch (logError) {
      console.error('Error updating script execution record:', logError);
    }

    return res.status(500).json({ error: 'Failed to fetch weather data', details: error.message });
  }
};

/**
 * Fetch and store grants data from various sources
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const fetchAndStoreGrants = async (req, res) => {
  try {
    // Log script execution start
    const scriptExecution = await ScriptExecution.create({
      name: 'Fetch and Store Grants',
      description: 'Fetch and store grants data from various sources',
      file_path: 'controllers/cronController.js',
      status: 'running'
    });

    console.log('Starting grants data fetch from various sources');

    const results = {
      grantsGov: { success: false, count: 0, error: null },
      farmersGov: { success: false, count: 0, error: null },
      usda: { success: false, count: 0, error: null }
    };

    // Fetch and store grants from grants.gov
    try {
      const grantsGovData = await fetchGrantsGovGrants();
      await storeGrantsData(grantsGovData, 'grants.gov');
      results.grantsGov.success = true;
      results.grantsGov.count = grantsGovData.length;
    } catch (error) {
      console.error('Error fetching grants.gov grants:', error);
      results.grantsGov.error = error.message;
    }

    // Fetch and store grants from farmers.gov
    try {
      const farmersGovData = await fetchFarmersGovGrants();
      await storeGrantsData(farmersGovData, 'farmers.gov');
      results.farmersGov.success = true;
      results.farmersGov.count = farmersGovData.length;
    } catch (error) {
      console.error('Error fetching farmers.gov grants:', error);
      results.farmersGov.error = error.message;
    }

    // Fetch and store grants from USDA
    try {
      const usdaData = await fetchUSDAGrants();
      await storeGrantsData(usdaData, 'usda');
      results.usda.success = true;
      results.usda.count = usdaData.length;
    } catch (error) {
      console.error('Error fetching USDA grants:', error);
      results.usda.error = error.message;
    }

    // Calculate total counts
    const totalCount = results.grantsGov.count + results.farmersGov.count + results.usda.count;
    const successCount = (results.grantsGov.success ? 1 : 0) + 
                         (results.farmersGov.success ? 1 : 0) + 
                         (results.usda.success ? 1 : 0);

    // Update script execution record
    await scriptExecution.update({
      executed: true,
      executed_at: new Date(),
      status: 'completed',
      output: JSON.stringify({
        totalSources: 3,
        successfulSources: successCount,
        totalGrantsStored: totalCount,
        results
      })
    });

    return res.status(200).json({
      message: `Processed grants data from 3 sources (${successCount} successful)`,
      totalGrantsStored: totalCount,
      results
    });
  } catch (error) {
    console.error('Error in fetchAndStoreGrants:', error);

    // Update script execution record with error
    try {
      await ScriptExecution.update(
        {
          executed: true,
          executed_at: new Date(),
          status: 'failed',
          error_message: error.message
        },
        {
          where: {
            name: 'Fetch and Store Grants',
            status: 'running'
          }
        }
      );
    } catch (logError) {
      console.error('Error updating script execution record:', logError);
    }

    return res.status(500).json({ error: 'Failed to fetch grants data', details: error.message });
  }
};

// Helper Functions for Weather Data

/**
 * Map NWS forecast icons to our icon codes (similar to OpenWeatherMap)
 * @param {string} iconUrl - The icon URL from NWS API
 * @returns {string} - The mapped icon code
 */
const mapNWSIconToCode = (iconUrl) => {
  if (!iconUrl) return '01d'; // default to clear day

  // Extract the icon name from the URL
  const iconName = iconUrl.split('/').pop().replace(/\?.*$/, '');

  // Check if it's day or night
  const isDay = !iconName.includes('night');
  const suffix = isDay ? 'd' : 'n';

  // Map NWS icons to codes similar to OpenWeatherMap
  if (iconName.includes('skc') || iconName.includes('few')) return `01${suffix}`; // clear
  if (iconName.includes('sct')) return `02${suffix}`; // partly cloudy
  if (iconName.includes('bkn')) return `03${suffix}`; // mostly cloudy
  if (iconName.includes('ovc')) return `04${suffix}`; // overcast
  if (iconName.includes('rain') || iconName.includes('showers')) return `09${suffix}`; // rain
  if (iconName.includes('tsra')) return `11${suffix}`; // thunderstorm
  if (iconName.includes('snow')) return `13${suffix}`; // snow
  if (iconName.includes('fog') || iconName.includes('haze')) return `50${suffix}`; // fog

  return `01${suffix}`; // default to clear
};

/**
 * Map NWS forecast descriptions to conditions (similar to OpenWeatherMap)
 * @param {string} description - The description from NWS API
 * @returns {string} - The mapped condition
 */
const mapNWSDescriptionToCondition = (description) => {
  if (!description) return 'Clear';

  const desc = description.toLowerCase();

  if (desc.includes('sunny') || desc.includes('clear')) return 'Clear';
  if (desc.includes('cloud')) return 'Clouds';
  if (desc.includes('rain') || desc.includes('shower')) return 'Rain';
  if (desc.includes('thunderstorm') || desc.includes('tstorm')) return 'Thunderstorm';
  if (desc.includes('snow') || desc.includes('flurries')) return 'Snow';
  if (desc.includes('fog') || desc.includes('mist') || desc.includes('haze')) return 'Mist';

  return 'Clear'; // default
};

/**
 * Fetch weather data from NWS API
 * @param {number} latitude - The latitude
 * @param {number} longitude - The longitude
 * @returns {Object} - The weather data
 */
const fetchWeatherData = async (latitude, longitude) => {
  try {
    // Step 1: Get the grid point for the location
    const pointsUrl = `${NWS_API_BASE}/points/${latitude},${longitude}`;
    let pointsResponse;
    try {
      pointsResponse = await axios.get(pointsUrl, {
        headers: {
          'User-Agent': '(NxtAcre Farm Management, <EMAIL>)',
          'Accept': 'application/geo+json'
        }
      });
    } catch (error) {
      console.error('Error fetching grid points:', error);
      throw new Error(`Failed to fetch grid points: ${error.message}`);
    }

    const { gridId, gridX, gridY } = pointsResponse.data.properties;

    // Step 2: Get the forecast for the grid point
    const forecastUrl = `${NWS_API_BASE}/gridpoints/${gridId}/${gridX},${gridY}/forecast`;
    let forecastResponse;
    try {
      forecastResponse = await axios.get(forecastUrl, {
        headers: {
          'User-Agent': '(NxtAcre Farm Management, <EMAIL>)',
          'Accept': 'application/geo+json'
        }
      });
    } catch (error) {
      console.error('Error fetching forecast:', error);
      throw new Error(`Failed to fetch forecast: ${error.message}`);
    }

    // Step 3: Get the hourly forecast for current conditions
    const hourlyForecastUrl = `${NWS_API_BASE}/gridpoints/${gridId}/${gridX},${gridY}/forecast/hourly`;
    let hourlyForecastResponse;
    try {
      hourlyForecastResponse = await axios.get(hourlyForecastUrl, {
        headers: {
          'User-Agent': '(NxtAcre Farm Management, <EMAIL>)',
          'Accept': 'application/geo+json'
        }
      });
    } catch (error) {
      console.error('Error fetching hourly forecast:', error);
      throw new Error(`Failed to fetch hourly forecast: ${error.message}`);
    }

    // Process current weather data (from the first hourly forecast period)
    const currentPeriod = hourlyForecastResponse.data.properties.periods[0];

    const current = {
      temp: Math.round(currentPeriod.temperature),
      feels_like: Math.round(currentPeriod.temperature), // NWS doesn't provide feels_like
      condition: mapNWSDescriptionToCondition(currentPeriod.shortForecast),
      icon: mapNWSIconToCode(currentPeriod.icon),
      wind_speed: currentPeriod.windSpeed,
      wind_direction: currentPeriod.windDirection,
      precipitation_chance: currentPeriod.probabilityOfPrecipitation?.value || 0,
      humidity: currentPeriod.relativeHumidity?.value || 0,
      timestamp: currentPeriod.startTime
    };

    // Process hourly forecast data
    const hourlyPeriods = hourlyForecastResponse.data.properties.periods;
    const hourlyForecast = hourlyPeriods.slice(0, 24).map(period => {
      return {
        time: period.startTime,
        temp: Math.round(period.temperature),
        condition: mapNWSDescriptionToCondition(period.shortForecast),
        icon: mapNWSIconToCode(period.icon),
        wind_speed: period.windSpeed,
        wind_direction: period.windDirection,
        precipitation_chance: period.probabilityOfPrecipitation?.value || 0,
        humidity: period.relativeHumidity?.value || 0
      };
    });

    // Process forecast data
    const dailyPeriods = forecastResponse.data.properties.periods;
    const dailyForecasts = {};

    // NWS API returns separate periods for day and night
    // We need to combine them to get high/low temps for each day
    dailyPeriods.forEach((period) => {
      const date = new Date(period.startTime);
      const day = date.toLocaleDateString('en-US', { weekday: 'short' });
      const isDaytime = period.isDaytime;

      if (!dailyForecasts[day]) {
        dailyForecasts[day] = { 
          high: isDaytime ? period.temperature : -Infinity,
          low: !isDaytime ? period.temperature : Infinity,
          condition: isDaytime ? mapNWSDescriptionToCondition(period.shortForecast) : '',
          icon: isDaytime ? mapNWSIconToCode(period.icon) : '',
          date: date,
          wind_speed: period.windSpeed,
          wind_direction: period.windDirection,
          precipitation_chance: period.probabilityOfPrecipitation?.value || 0,
          humidity: period.relativeHumidity?.value || 0
        };
      } else {
        if (isDaytime) {
          dailyForecasts[day].high = period.temperature;
          dailyForecasts[day].condition = mapNWSDescriptionToCondition(period.shortForecast);
          dailyForecasts[day].icon = mapNWSIconToCode(period.icon);
        } else {
          dailyForecasts[day].low = period.temperature;
        }
      }
    });

    // Convert to array and limit to 7 days
    const forecast = Object.entries(dailyForecasts)
      .sort((a, b) => a[1].date - b[1].date) // Sort by date
      .map(([day, data]) => {
        return {
          day: day === new Date().toLocaleDateString('en-US', { weekday: 'short' }) ? 'Today' : day,
          date: data.date.toISOString().split('T')[0],
          high: Math.round(data.high),
          low: Math.round(data.low),
          condition: data.condition,
          icon: data.icon,
          wind_speed: data.wind_speed,
          wind_direction: data.wind_direction,
          precipitation_chance: data.precipitation_chance,
          humidity: data.humidity
        };
      })
      .slice(0, 7);

    return { current, forecast, hourly: hourlyForecast };
  } catch (error) {
    console.error('Error fetching weather data:', error);
    throw new Error(`Failed to fetch weather data: ${error.message}`);
  }
};

/**
 * Store weather data in database
 * @param {Object} weatherData - The weather data to store
 * @param {string} farmId - The farm ID
 * @param {string} fieldId - The field ID (optional)
 * @param {number} latitude - The latitude
 * @param {number} longitude - The longitude
 */
const storeWeatherData = async (weatherData, farmId, fieldId, latitude, longitude) => {
  const transaction = await sequelize.transaction();

  try {
    // Store current weather
    await Weather.create({
      farm_id: farmId,
      field_id: fieldId,
      latitude,
      longitude,
      timestamp: new Date(weatherData.current.timestamp),
      temperature: weatherData.current.temp,
      feels_like: weatherData.current.feels_like,
      humidity: weatherData.current.humidity,
      wind_speed: parseFloat(weatherData.current.wind_speed),
      wind_direction: weatherData.current.wind_direction,
      precipitation: 0, // NWS doesn't provide current precipitation
      precipitation_chance: weatherData.current.precipitation_chance,
      condition: weatherData.current.condition,
      icon: weatherData.current.icon,
      forecast_type: 'current'
    }, { transaction });

    // Store hourly forecast
    for (let i = 0; i < weatherData.hourly.length; i++) {
      const hour = weatherData.hourly[i];
      await Weather.create({
        farm_id: farmId,
        field_id: fieldId,
        latitude,
        longitude,
        timestamp: new Date(hour.time),
        temperature: hour.temp,
        feels_like: hour.temp, // NWS doesn't provide feels_like
        humidity: hour.humidity,
        wind_speed: parseFloat(hour.wind_speed),
        wind_direction: hour.wind_direction,
        precipitation: 0, // NWS doesn't provide precipitation amount
        precipitation_chance: hour.precipitation_chance,
        condition: hour.condition,
        icon: hour.icon,
        forecast_type: 'hourly',
        forecast_hour: i
      }, { transaction });
    }

    // Store daily forecast
    for (let i = 0; i < weatherData.forecast.length; i++) {
      const day = weatherData.forecast[i];
      await Weather.create({
        farm_id: farmId,
        field_id: fieldId,
        latitude,
        longitude,
        timestamp: new Date(day.date),
        temperature: day.high, // Store high temperature
        feels_like: day.high, // NWS doesn't provide feels_like
        humidity: day.humidity,
        wind_speed: parseFloat(day.wind_speed),
        wind_direction: day.wind_direction,
        precipitation: 0, // NWS doesn't provide precipitation amount
        precipitation_chance: day.precipitation_chance,
        condition: day.condition,
        icon: day.icon,
        forecast_type: 'daily',
        forecast_day: i
      }, { transaction });
    }

    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    console.error('Error storing weather data:', error);
    throw new Error(`Failed to store weather data: ${error.message}`);
  }
};

// Helper Functions for Grants Data

/**
 * Fetch grants from grants.gov API
 * @returns {Array} - The grants data
 */
const fetchGrantsGovGrants = async () => {
  try {
    // Configure API client
    const grantsGovClient = axios.create({
      baseURL: GRANTS_GOV_API_URL,
      headers: {
        'Authorization': `Bearer ${GRANTS_GOV_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    // Categories to fetch
    const categories = ['agriculture', 'farm', 'farming', 'rural', 'crop', 'livestock'];
    let allGrants = [];

    // Fetch grants for each category
    for (const category of categories) {
      try {
        const response = await grantsGovClient.get('/opportunities', {
          params: {
            keyword: category,
            limit: 100
          }
        });

        // Transform the API response to match our expected format
        const grants = response.data.opportunities.map(opportunity => ({
          id: opportunity.id,
          title: opportunity.title,
          description: opportunity.description || 'No description available',
          agency: opportunity.agency,
          opportunityNumber: opportunity.opportunityNumber,
          category: category,
          eligibility: opportunity.eligibility || 'See grant details for eligibility information',
          fundingAmount: opportunity.awardCeiling ? `$${opportunity.awardCeiling}` : 'See grant details for funding information',
          closeDate: opportunity.closeDate,
          url: `https://www.grants.gov/web/grants/view-opportunity.html?oppId=${opportunity.id}`,
          createdAt: opportunity.postDate || new Date().toISOString()
        }));

        allGrants = [...allGrants, ...grants];
      } catch (error) {
        console.warn(`Failed to fetch ${category} grants from grants.gov API:`, error);

        // Fallback to mock data if the API call fails
        const mockGrants = Array.from({ length: 5 }, (_, i) => ({
          id: `grant-${category}-${i + 1}`,
          title: `${category.charAt(0).toUpperCase() + category.slice(1)} Grant ${i + 1}`,
          description: `This is a mock grant for ${category} projects. It provides funding for various agricultural initiatives.`,
          agency: 'Department of Agriculture',
          opportunityNumber: `USDA-GRANTS-2023-${category}-${i + 1}`,
          category: category,
          eligibility: 'Small to medium-sized farms, agricultural cooperatives',
          fundingAmount: `$${(Math.random() * 500000 + 50000).toFixed(2)}`,
          closeDate: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
          url: 'https://www.grants.gov/web/grants/search-grants.html',
          createdAt: new Date().toISOString()
        }));

        allGrants = [...allGrants, ...mockGrants];
      }
    }

    // Remove duplicates based on id
    const uniqueGrants = Array.from(new Map(allGrants.map(grant => [grant.id, grant])).values());

    return uniqueGrants;
  } catch (error) {
    console.error('Error fetching grants.gov grants:', error);
    throw new Error(`Failed to fetch grants.gov grants: ${error.message}`);
  }
};

/**
 * Fetch grants from farmers.gov API
 * @returns {Array} - The grants data
 */
const fetchFarmersGovGrants = async () => {
  try {
    // Configure API client
    const farmersGovClient = axios.create({
      baseURL: FARMERS_GOV_API_URL,
      headers: {
        'Authorization': `Bearer ${FARMERS_GOV_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    try {
      const response = await farmersGovClient.get('/programs', {
        params: {
          type: 'grant',
          limit: 100
        }
      });

      // Transform the API response to match our expected format
      return response.data.programs.map(program => ({
        id: program.id,
        title: program.title,
        description: program.description || 'No description available',
        agency: program.agency || 'USDA',
        opportunityNumber: program.programCode || `FARMERS-${program.id}`,
        category: program.category || 'agriculture',
        eligibility: program.eligibility || 'See program details for eligibility information',
        fundingAmount: program.fundingAmount || 'See program details for funding information',
        closeDate: program.applicationDeadline || null,
        url: program.url || `https://www.farmers.gov/programs/${program.id}`,
        createdAt: program.publishDate || new Date().toISOString()
      }));
    } catch (error) {
      console.warn('Failed to fetch from farmers.gov API, using mock data instead:', error);

      // Fallback to mock data if the API call fails
      return Array.from({ length: 10 }, (_, i) => ({
        id: `farmers-grant-${i + 1}`,
        title: `Farmers.gov Agricultural Grant ${i + 1}`,
        description: 'This is a mock grant from farmers.gov. It provides funding for various agricultural initiatives.',
        agency: 'USDA',
        opportunityNumber: `FARMERS-2023-${i + 1}`,
        category: 'agriculture',
        eligibility: 'Small to medium-sized farms, agricultural cooperatives',
        fundingAmount: `$${(Math.random() * 500000 + 50000).toFixed(2)}`,
        closeDate: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        url: 'https://www.farmers.gov/programs',
        createdAt: new Date().toISOString()
      }));
    }
  } catch (error) {
    console.error('Error fetching farmers.gov grants:', error);
    throw new Error(`Failed to fetch farmers.gov grants: ${error.message}`);
  }
};

/**
 * Fetch grants from USDA API
 * @returns {Array} - The grants data
 */
const fetchUSDAGrants = async () => {
  try {
    // Configure API client
    const usdaArmsClient = axios.create({
      baseURL: USDA_ARMS_API_URL,
      headers: {
        'Authorization': `Bearer ${USDA_ARMS_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    try {
      const response = await usdaArmsClient.get('/programs', {
        params: {
          type: 'grant',
          limit: 100
        }
      });

      // Transform the API response to match our expected format
      return response.data.programs.map(program => ({
        id: program.id,
        title: program.title,
        description: program.description || 'No description available',
        agency: program.agency || 'USDA',
        opportunityNumber: program.programCode || `USDA-${program.id}`,
        category: program.category || 'agriculture',
        eligibility: program.eligibility || 'See program details for eligibility information',
        fundingAmount: program.fundingAmount || 'See program details for funding information',
        closeDate: program.applicationDeadline || null,
        url: program.url || `https://www.usda.gov/programs/${program.id}`,
        createdAt: program.publishDate || new Date().toISOString()
      }));
    } catch (error) {
      console.warn('Failed to fetch from USDA API, using mock data instead:', error);

      // Fallback to mock data if the API call fails
      return Array.from({ length: 10 }, (_, i) => ({
        id: `usda-grant-${i + 1}`,
        title: `USDA Agricultural Grant ${i + 1}`,
        description: 'This is a mock grant from USDA. It provides funding for various agricultural initiatives.',
        agency: 'USDA',
        opportunityNumber: `USDA-2023-${i + 1}`,
        category: 'agriculture',
        eligibility: 'Small to medium-sized farms, agricultural cooperatives',
        fundingAmount: `$${(Math.random() * 500000 + 50000).toFixed(2)}`,
        closeDate: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        url: 'https://www.usda.gov/programs',
        createdAt: new Date().toISOString()
      }));
    }
  } catch (error) {
    console.error('Error fetching USDA grants:', error);
    throw new Error(`Failed to fetch USDA grants: ${error.message}`);
  }
};

/**
 * Store grants data in database
 * @param {Array} grantsData - The grants data to store
 * @param {string} source - The source of the grants data
 */
const storeGrantsData = async (grantsData, source) => {
  const transaction = await sequelize.transaction();

  try {
    for (const grantData of grantsData) {
      // Check if grant already exists
      const existingGrant = await Grant.findOne({
        where: {
          external_id: grantData.id,
          source: source
        }
      });

      if (existingGrant) {
        // Update existing grant
        await existingGrant.update({
          title: grantData.title,
          description: grantData.description,
          agency: grantData.agency,
          opportunity_number: grantData.opportunityNumber,
          category: grantData.category,
          eligibility: grantData.eligibility,
          funding_amount: grantData.fundingAmount,
          close_date: grantData.closeDate ? new Date(grantData.closeDate) : null,
          url: grantData.url
        }, { transaction });
      } else {
        // Create new grant
        await Grant.create({
          external_id: grantData.id,
          title: grantData.title,
          description: grantData.description,
          agency: grantData.agency,
          opportunity_number: grantData.opportunityNumber,
          category: grantData.category,
          eligibility: grantData.eligibility,
          funding_amount: grantData.fundingAmount,
          close_date: grantData.closeDate ? new Date(grantData.closeDate) : null,
          url: grantData.url,
          source: source
        }, { transaction });
      }
    }

    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    console.error('Error storing grants data:', error);
    throw new Error(`Failed to store grants data: ${error.message}`);
  }
};
