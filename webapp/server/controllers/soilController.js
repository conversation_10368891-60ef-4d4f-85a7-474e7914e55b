import SoilSample from '../models/SoilSample.js';
import SoilTestResult from '../models/SoilTestResult.js';
import SoilAmendment from '../models/SoilAmendment.js';
import SoilRecommendation from '../models/SoilRecommendation.js';
import Farm from '../models/Farm.js';
import { sequelize } from '../config/database.js';

// Soil Sample Controllers

// Get all soil samples for a farm
export const getFarmSoilSamples = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all soil samples for the farm
    const soilSamples = await SoilSample.findAll({
      where: { farm_id: farmId },
      order: [['sample_date', 'DESC']]
    });

    return res.status(200).json({ soilSamples });
  } catch (error) {
    console.error('Error getting farm soil samples:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single soil sample by ID
export const getSoilSampleById = async (req, res) => {
  try {
    const { sampleId } = req.params;

    const soilSample = await SoilSample.findByPk(sampleId);

    if (!soilSample) {
      return res.status(404).json({ error: 'Soil sample not found' });
    }

    return res.status(200).json({ soilSample });
  } catch (error) {
    console.error('Error getting soil sample:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new soil sample
export const createSoilSample = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      farmId, 
      fieldId, 
      sampleDate, 
      location, 
      depth, 
      labName, 
      labReference, 
      status, 
      notes 
    } = req.body;

    // Validate required fields
    if (!farmId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    if (!sampleDate) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Sample date is required' });
    }

    if (!status) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Status is required' });
    }

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Create soil sample
    const soilSample = await SoilSample.create({
      farm_id: farmId,
      field_id: fieldId,
      sample_date: sampleDate,
      location,
      depth,
      lab_name: labName,
      lab_reference: labReference,
      status,
      notes
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({ 
      message: 'Soil sample created successfully',
      soilSample 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating soil sample:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a soil sample
export const updateSoilSample = async (req, res) => {
  try {
    const { sampleId } = req.params;
    const { 
      fieldId, 
      sampleDate, 
      location, 
      depth, 
      labName, 
      labReference, 
      status, 
      notes 
    } = req.body;

    // Find soil sample to ensure it exists
    const soilSample = await SoilSample.findByPk(sampleId);
    if (!soilSample) {
      return res.status(404).json({ error: 'Soil sample not found' });
    }

    // Update soil sample
    await soilSample.update({
      field_id: fieldId !== undefined ? fieldId : soilSample.field_id,
      sample_date: sampleDate || soilSample.sample_date,
      location: location !== undefined ? location : soilSample.location,
      depth: depth !== undefined ? depth : soilSample.depth,
      lab_name: labName !== undefined ? labName : soilSample.lab_name,
      lab_reference: labReference !== undefined ? labReference : soilSample.lab_reference,
      status: status || soilSample.status,
      notes: notes !== undefined ? notes : soilSample.notes
    });

    return res.status(200).json({ 
      message: 'Soil sample updated successfully',
      soilSample 
    });
  } catch (error) {
    console.error('Error updating soil sample:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a soil sample
export const deleteSoilSample = async (req, res) => {
  try {
    const { sampleId } = req.params;

    // Find soil sample to ensure it exists
    const soilSample = await SoilSample.findByPk(sampleId);
    if (!soilSample) {
      return res.status(404).json({ error: 'Soil sample not found' });
    }

    // Delete soil sample
    await soilSample.destroy();

    return res.status(200).json({ 
      message: 'Soil sample deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting soil sample:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Soil Test Result Controllers

// Get all test results for a soil sample
export const getSoilTestResults = async (req, res) => {
  try {
    const { sampleId } = req.params;

    // Find soil sample to ensure it exists
    const soilSample = await SoilSample.findByPk(sampleId);
    if (!soilSample) {
      return res.status(404).json({ error: 'Soil sample not found' });
    }

    // Get all test results for the soil sample
    const soilTestResults = await SoilTestResult.findAll({
      where: { soil_sample_id: sampleId },
      order: [['created_at', 'DESC']]
    });

    return res.status(200).json({ soilTestResults });
  } catch (error) {
    console.error('Error getting soil test results:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single test result by ID
export const getSoilTestResultById = async (req, res) => {
  try {
    const { resultId } = req.params;

    const soilTestResult = await SoilTestResult.findByPk(resultId);

    if (!soilTestResult) {
      return res.status(404).json({ error: 'Soil test result not found' });
    }

    return res.status(200).json({ soilTestResult });
  } catch (error) {
    console.error('Error getting soil test result:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new test result
export const createSoilTestResult = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      soilSampleId, 
      ph, 
      organicMatter, 
      nitrogen, 
      phosphorus, 
      potassium, 
      calcium, 
      magnesium, 
      sulfur, 
      zinc, 
      manganese, 
      copper, 
      iron, 
      boron, 
      cec, 
      baseSaturation, 
      otherResults 
    } = req.body;

    // Validate required fields
    if (!soilSampleId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Soil sample ID is required' });
    }

    // Find soil sample to ensure it exists
    const soilSample = await SoilSample.findByPk(soilSampleId);
    if (!soilSample) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Soil sample not found' });
    }

    // Create soil test result
    const soilTestResult = await SoilTestResult.create({
      soil_sample_id: soilSampleId,
      ph,
      organic_matter: organicMatter,
      nitrogen,
      phosphorus,
      potassium,
      calcium,
      magnesium,
      sulfur,
      zinc,
      manganese,
      copper,
      iron,
      boron,
      cec,
      base_saturation: baseSaturation,
      other_results: otherResults
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({ 
      message: 'Soil test result created successfully',
      soilTestResult 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating soil test result:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a test result
export const updateSoilTestResult = async (req, res) => {
  try {
    const { resultId } = req.params;
    const { 
      ph, 
      organicMatter, 
      nitrogen, 
      phosphorus, 
      potassium, 
      calcium, 
      magnesium, 
      sulfur, 
      zinc, 
      manganese, 
      copper, 
      iron, 
      boron, 
      cec, 
      baseSaturation, 
      otherResults 
    } = req.body;

    // Find soil test result to ensure it exists
    const soilTestResult = await SoilTestResult.findByPk(resultId);
    if (!soilTestResult) {
      return res.status(404).json({ error: 'Soil test result not found' });
    }

    // Update soil test result
    await soilTestResult.update({
      ph: ph !== undefined ? ph : soilTestResult.ph,
      organic_matter: organicMatter !== undefined ? organicMatter : soilTestResult.organic_matter,
      nitrogen: nitrogen !== undefined ? nitrogen : soilTestResult.nitrogen,
      phosphorus: phosphorus !== undefined ? phosphorus : soilTestResult.phosphorus,
      potassium: potassium !== undefined ? potassium : soilTestResult.potassium,
      calcium: calcium !== undefined ? calcium : soilTestResult.calcium,
      magnesium: magnesium !== undefined ? magnesium : soilTestResult.magnesium,
      sulfur: sulfur !== undefined ? sulfur : soilTestResult.sulfur,
      zinc: zinc !== undefined ? zinc : soilTestResult.zinc,
      manganese: manganese !== undefined ? manganese : soilTestResult.manganese,
      copper: copper !== undefined ? copper : soilTestResult.copper,
      iron: iron !== undefined ? iron : soilTestResult.iron,
      boron: boron !== undefined ? boron : soilTestResult.boron,
      cec: cec !== undefined ? cec : soilTestResult.cec,
      base_saturation: baseSaturation !== undefined ? baseSaturation : soilTestResult.base_saturation,
      other_results: otherResults !== undefined ? otherResults : soilTestResult.other_results
    });

    return res.status(200).json({ 
      message: 'Soil test result updated successfully',
      soilTestResult 
    });
  } catch (error) {
    console.error('Error updating soil test result:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a test result
export const deleteSoilTestResult = async (req, res) => {
  try {
    const { resultId } = req.params;

    // Find soil test result to ensure it exists
    const soilTestResult = await SoilTestResult.findByPk(resultId);
    if (!soilTestResult) {
      return res.status(404).json({ error: 'Soil test result not found' });
    }

    // Delete soil test result
    await soilTestResult.destroy();

    return res.status(200).json({ 
      message: 'Soil test result deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting soil test result:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Preview soil test result import
export const previewImportSoilTestResults = async (req, res) => {
  try {
    if (!req.files || !req.files.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const file = req.files.file;
    const format = req.body.format || 'csv';

    // Process file based on format
    let preview = {};

    if (format === 'csv') {
      // Simple CSV parsing for preview (first line only)
      const csvData = file.data.toString('utf8');
      const lines = csvData.split('\n');

      if (lines.length < 2) {
        return res.status(400).json({ error: 'CSV file must have at least a header row and one data row' });
      }

      const headers = lines[0].split(',').map(header => header.trim());
      const values = lines[1].split(',').map(value => value.trim());

      // Map CSV headers to soil test result fields
      const fieldMap = {
        'ph': 'ph',
        'organic matter': 'organic_matter',
        'nitrogen': 'nitrogen',
        'phosphorus': 'phosphorus',
        'potassium': 'potassium',
        'calcium': 'calcium',
        'magnesium': 'magnesium',
        'sulfur': 'sulfur',
        'zinc': 'zinc',
        'manganese': 'manganese',
        'copper': 'copper',
        'iron': 'iron',
        'boron': 'boron',
        'cec': 'cec',
        'base saturation': 'base_saturation'
      };

      // Create preview object with mapped fields
      headers.forEach((header, index) => {
        const lowerHeader = header.toLowerCase();
        const fieldName = fieldMap[lowerHeader] || lowerHeader;
        preview[fieldName] = values[index] || null;
      });
    } else if (format === 'excel') {
      // For Excel files, we'd use a library like xlsx
      // This is a simplified version for the example
      return res.status(400).json({ 
        error: 'Excel import preview not implemented yet. Please use CSV format.' 
      });
    } else if (format === 'json') {
      // Parse JSON file
      try {
        const jsonData = JSON.parse(file.data.toString('utf8'));
        preview = jsonData;
      } catch (err) {
        return res.status(400).json({ error: 'Invalid JSON file' });
      }
    } else {
      return res.status(400).json({ error: 'Unsupported file format' });
    }

    return res.status(200).json({ preview });
  } catch (error) {
    console.error('Error previewing soil test result import:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Import soil test results from file
export const importSoilTestResults = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    if (!req.files || !req.files.file) {
      await transaction.rollback();
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const file = req.files.file;
    const format = req.body.format || 'csv';
    const sampleId = req.body.sampleId;

    if (!sampleId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Soil sample ID is required' });
    }

    // Find soil sample to ensure it exists
    const soilSample = await SoilSample.findByPk(sampleId);
    if (!soilSample) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Soil sample not found' });
    }

    // Process file based on format
    let soilTestData = [];

    if (format === 'csv') {
      // Parse CSV file
      const csvData = file.data.toString('utf8');
      const lines = csvData.split('\n');

      if (lines.length < 2) {
        await transaction.rollback();
        return res.status(400).json({ error: 'CSV file must have at least a header row and one data row' });
      }

      const headers = lines[0].split(',').map(header => header.trim());

      // Map CSV headers to soil test result fields
      const fieldMap = {
        'ph': 'ph',
        'organic matter': 'organic_matter',
        'nitrogen': 'nitrogen',
        'phosphorus': 'phosphorus',
        'potassium': 'potassium',
        'calcium': 'calcium',
        'magnesium': 'magnesium',
        'sulfur': 'sulfur',
        'zinc': 'zinc',
        'manganese': 'manganese',
        'copper': 'copper',
        'iron': 'iron',
        'boron': 'boron',
        'cec': 'cec',
        'base saturation': 'base_saturation'
      };

      // Process each data row
      for (let i = 1; i < lines.length; i++) {
        if (!lines[i].trim()) continue; // Skip empty lines

        const values = lines[i].split(',').map(value => value.trim());
        const testResult = { soil_sample_id: sampleId };

        headers.forEach((header, index) => {
          const lowerHeader = header.toLowerCase();
          const fieldName = fieldMap[lowerHeader];

          if (fieldName && values[index]) {
            testResult[fieldName] = parseFloat(values[index]) || null;
          }
        });

        soilTestData.push(testResult);
      }
    } else if (format === 'excel') {
      // For Excel files, we'd use a library like xlsx
      // This is a simplified version for the example
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Excel import not implemented yet. Please use CSV format.' 
      });
    } else if (format === 'json') {
      // Parse JSON file
      try {
        const jsonData = JSON.parse(file.data.toString('utf8'));

        if (Array.isArray(jsonData)) {
          // If JSON is an array, process each item
          soilTestData = jsonData.map(item => ({
            soil_sample_id: sampleId,
            ph: item.ph || null,
            organic_matter: item.organic_matter || null,
            nitrogen: item.nitrogen || null,
            phosphorus: item.phosphorus || null,
            potassium: item.potassium || null,
            calcium: item.calcium || null,
            magnesium: item.magnesium || null,
            sulfur: item.sulfur || null,
            zinc: item.zinc || null,
            manganese: item.manganese || null,
            copper: item.copper || null,
            iron: item.iron || null,
            boron: item.boron || null,
            cec: item.cec || null,
            base_saturation: item.base_saturation || null
          }));
        } else {
          // If JSON is a single object
          soilTestData = [{
            soil_sample_id: sampleId,
            ph: jsonData.ph || null,
            organic_matter: jsonData.organic_matter || null,
            nitrogen: jsonData.nitrogen || null,
            phosphorus: jsonData.phosphorus || null,
            potassium: jsonData.potassium || null,
            calcium: jsonData.calcium || null,
            magnesium: jsonData.magnesium || null,
            sulfur: jsonData.sulfur || null,
            zinc: jsonData.zinc || null,
            manganese: jsonData.manganese || null,
            copper: jsonData.copper || null,
            iron: jsonData.iron || null,
            boron: jsonData.boron || null,
            cec: jsonData.cec || null,
            base_saturation: jsonData.base_saturation || null
          }];
        }
      } catch (err) {
        await transaction.rollback();
        return res.status(400).json({ error: 'Invalid JSON file' });
      }
    } else {
      await transaction.rollback();
      return res.status(400).json({ error: 'Unsupported file format' });
    }

    // Create soil test results
    const createdResults = [];
    for (const testData of soilTestData) {
      const result = await SoilTestResult.create(testData, { transaction });
      createdResults.push(result);
    }

    await transaction.commit();

    return res.status(201).json({ 
      message: `Successfully imported ${createdResults.length} soil test results`,
      results: createdResults
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error importing soil test results:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Soil Amendment Controllers

// Get all soil amendments for a farm
export const getFarmSoilAmendments = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all soil amendments for the farm
    const soilAmendments = await SoilAmendment.findAll({
      where: { farm_id: farmId },
      order: [['amendment_date', 'DESC']]
    });

    return res.status(200).json({ soilAmendments });
  } catch (error) {
    console.error('Error getting farm soil amendments:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single soil amendment by ID
export const getSoilAmendmentById = async (req, res) => {
  try {
    const { amendmentId } = req.params;

    const soilAmendment = await SoilAmendment.findByPk(amendmentId);

    if (!soilAmendment) {
      return res.status(404).json({ error: 'Soil amendment not found' });
    }

    return res.status(200).json({ soilAmendment });
  } catch (error) {
    console.error('Error getting soil amendment:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new soil amendment
export const createSoilAmendment = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      farmId, 
      fieldId, 
      amendmentDate, 
      amendmentType, 
      quantity, 
      unit, 
      costPerUnit, 
      totalCost, 
      notes 
    } = req.body;

    // Validate required fields
    if (!farmId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    if (!amendmentDate) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Amendment date is required' });
    }

    if (!amendmentType) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Amendment type is required' });
    }

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Create soil amendment
    const soilAmendment = await SoilAmendment.create({
      farm_id: farmId,
      field_id: fieldId,
      amendment_date: amendmentDate,
      amendment_type: amendmentType,
      quantity,
      unit,
      cost_per_unit: costPerUnit,
      total_cost: totalCost,
      notes
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({ 
      message: 'Soil amendment created successfully',
      soilAmendment 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating soil amendment:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a soil amendment
export const updateSoilAmendment = async (req, res) => {
  try {
    const { amendmentId } = req.params;
    const { 
      fieldId, 
      amendmentDate, 
      amendmentType, 
      quantity, 
      unit, 
      costPerUnit, 
      totalCost, 
      notes 
    } = req.body;

    // Find soil amendment to ensure it exists
    const soilAmendment = await SoilAmendment.findByPk(amendmentId);
    if (!soilAmendment) {
      return res.status(404).json({ error: 'Soil amendment not found' });
    }

    // Update soil amendment
    await soilAmendment.update({
      field_id: fieldId !== undefined ? fieldId : soilAmendment.field_id,
      amendment_date: amendmentDate || soilAmendment.amendment_date,
      amendment_type: amendmentType || soilAmendment.amendment_type,
      quantity: quantity !== undefined ? quantity : soilAmendment.quantity,
      unit: unit !== undefined ? unit : soilAmendment.unit,
      cost_per_unit: costPerUnit !== undefined ? costPerUnit : soilAmendment.cost_per_unit,
      total_cost: totalCost !== undefined ? totalCost : soilAmendment.total_cost,
      notes: notes !== undefined ? notes : soilAmendment.notes
    });

    return res.status(200).json({ 
      message: 'Soil amendment updated successfully',
      soilAmendment 
    });
  } catch (error) {
    console.error('Error updating soil amendment:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a soil amendment
export const deleteSoilAmendment = async (req, res) => {
  try {
    const { amendmentId } = req.params;

    // Find soil amendment to ensure it exists
    const soilAmendment = await SoilAmendment.findByPk(amendmentId);
    if (!soilAmendment) {
      return res.status(404).json({ error: 'Soil amendment not found' });
    }

    // Delete soil amendment
    await soilAmendment.destroy();

    return res.status(200).json({ 
      message: 'Soil amendment deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting soil amendment:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Soil Recommendation Controllers

// Get all soil recommendations for a soil sample
export const getSoilRecommendations = async (req, res) => {
  try {
    const { sampleId } = req.params;

    // Find soil sample to ensure it exists
    const soilSample = await SoilSample.findByPk(sampleId);
    if (!soilSample) {
      return res.status(404).json({ error: 'Soil sample not found' });
    }

    // Get all recommendations for the soil sample
    const soilRecommendations = await SoilRecommendation.findAll({
      where: { soil_sample_id: sampleId },
      order: [['created_at', 'DESC']]
    });

    return res.status(200).json({ soilRecommendations });
  } catch (error) {
    console.error('Error getting soil recommendations:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single soil recommendation by ID
export const getSoilRecommendationById = async (req, res) => {
  try {
    const { recommendationId } = req.params;
    const soilRecommendation = await SoilRecommendation.findByPk(recommendationId);

    if (!soilRecommendation) {
      return res.status(404).json({ error: 'Soil recommendation not found' });
    }

    return res.status(200).json({ soilRecommendation });
  } catch (error) {
    console.error('Error getting soil recommendation:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new soil recommendation
export const createSoilRecommendation = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      soilSampleId, 
      fieldId, 
      nutrientType, 
      recommendedAmount, 
      unit, 
      applicationMethod, 
      applicationTiming, 
      notes 
    } = req.body;

    // Validate required fields
    if (!soilSampleId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Soil sample ID is required' });
    }

    if (!nutrientType) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Nutrient type is required' });
    }

    if (!recommendedAmount) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Recommended amount is required' });
    }

    if (!unit) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Unit is required' });
    }

    // Find soil sample to ensure it exists
    const soilSample = await SoilSample.findByPk(soilSampleId);
    if (!soilSample) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Soil sample not found' });
    }

    // Create soil recommendation
    const soilRecommendation = await SoilRecommendation.create({
      soil_sample_id: soilSampleId,
      field_id: fieldId,
      nutrient_type: nutrientType,
      recommended_amount: recommendedAmount,
      unit,
      application_method: applicationMethod,
      application_timing: applicationTiming,
      notes
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({ 
      message: 'Soil recommendation created successfully',
      soilRecommendation 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating soil recommendation:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a soil recommendation
export const updateSoilRecommendation = async (req, res) => {
  try {
    const { recommendationId } = req.params;
    const { 
      fieldId, 
      nutrientType, 
      recommendedAmount, 
      unit, 
      applicationMethod, 
      applicationTiming, 
      notes 
    } = req.body;

    // Find soil recommendation to ensure it exists
    const soilRecommendation = await SoilRecommendation.findByPk(recommendationId);
    if (!soilRecommendation) {
      return res.status(404).json({ error: 'Soil recommendation not found' });
    }

    // Update soil recommendation
    await soilRecommendation.update({
      field_id: fieldId !== undefined ? fieldId : soilRecommendation.field_id,
      nutrient_type: nutrientType || soilRecommendation.nutrient_type,
      recommended_amount: recommendedAmount !== undefined ? recommendedAmount : soilRecommendation.recommended_amount,
      unit: unit || soilRecommendation.unit,
      application_method: applicationMethod !== undefined ? applicationMethod : soilRecommendation.application_method,
      application_timing: applicationTiming !== undefined ? applicationTiming : soilRecommendation.application_timing,
      notes: notes !== undefined ? notes : soilRecommendation.notes
    });

    return res.status(200).json({ 
      message: 'Soil recommendation updated successfully',
      soilRecommendation 
    });
  } catch (error) {
    console.error('Error updating soil recommendation:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a soil recommendation
export const deleteSoilRecommendation = async (req, res) => {
  try {
    const { recommendationId } = req.params;

    // Find soil recommendation to ensure it exists
    const soilRecommendation = await SoilRecommendation.findByPk(recommendationId);
    if (!soilRecommendation) {
      return res.status(404).json({ error: 'Soil recommendation not found' });
    }

    // Delete soil recommendation
    await soilRecommendation.destroy();

    return res.status(200).json({ 
      message: 'Soil recommendation deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting soil recommendation:', error);
    return res.status(500).json({ error: error.message });
  }
};
