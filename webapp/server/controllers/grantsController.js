import axios from 'axios';
import { Farm, User } from '../models/index.js';
import dotenv from 'dotenv';

dotenv.config();

// Helper functions for generating mock data
const generateMockGrantsGovGrants = (category, limit) => {
  console.log('Generating mock Grants.gov grants...');
  return Array.from({ length: limit }, (_, i) => ({
    id: `grant-${i + 1}`,
    title: `Agricultural Grant ${i + 1}`,
    description: `This is a mock grant for ${category} projects. It provides funding for various agricultural initiatives.`,
    agency: 'Department of Agriculture',
    opportunityNumber: `USDA-GRANTS-2023-${i + 1}`,
    category: category,
    eligibility: 'Small to medium-sized farms, agricultural cooperatives',
    fundingAmount: `$${(Math.random() * 500000 + 50000).toFixed(2)}`,
    closeDate: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    url: 'https://www.grants.gov/web/grants/search-grants.html',
    createdAt: new Date().toISOString()
  }));
};

const generateMockUSDAGrants = (limit) => {
  console.log('Generating mock USDA grants...');
  return Array.from({ length: limit }, (_, i) => ({
    id: `usda-${i + 1}`,
    title: `USDA Agricultural Program ${i + 1}`,
    description: `This is a mock USDA program that provides assistance for agricultural operations.`,
    category: ['conservation', 'research', 'rural development', 'small business'][Math.floor(Math.random() * 4)],
    eligibility: 'Farmers, ranchers, and agricultural businesses',
    details: {
      program_type: ['Grant', 'Loan', 'Cost Share', 'Technical Assistance'][Math.floor(Math.random() * 4)],
      application_deadline: new Date(Date.now() + Math.random() * 60 * 24 * 60 * 60 * 1000).toLocaleDateString(),
      funding_range: `$${(Math.random() * 100000 + 10000).toFixed(2)} - $${(Math.random() * 500000 + 100000).toFixed(2)}`,
      contact_email: '<EMAIL>'
    },
    url: 'https://www.ers.usda.gov/developer/data-apis/arms-data-api/'
  }));
};

const generateMockFarmersGovGrants = (limit) => {
  console.log('Generating mock Farmers.gov grants...');
  return Array.from({ length: limit }, (_, i) => ({
    id: `farmers-${i + 1}`,
    title: `Farmers.gov Grant Program ${i + 1}`,
    description: `This is a mock farmers.gov grant program that provides assistance for agricultural operations.`,
    agency: 'USDA',
    category: ['conservation', 'organic', 'beginning farmers', 'disaster assistance'][Math.floor(Math.random() * 4)],
    eligibility: 'Farmers, ranchers, and agricultural businesses',
    fundingAmount: `$${(Math.random() * 200000 + 25000).toFixed(2)}`,
    closeDate: new Date(Date.now() + Math.random() * 45 * 24 * 60 * 60 * 1000).toISOString(),
    url: 'https://www.farmers.gov/fund',
    createdAt: new Date().toISOString()
  }));
};

const generateMockFSAGrants = (limit) => {
  console.log('Generating mock FSA grants...');
  return Array.from({ length: limit }, (_, i) => ({
    id: `fsa-${i + 1}`,
    title: `FSA Grant Program ${i + 1}`,
    description: `This is a mock FSA grant program that provides assistance for agricultural operations.`,
    agency: 'Farm Service Agency',
    category: ['conservation', 'disaster assistance', 'farm loans', 'price support'][Math.floor(Math.random() * 4)],
    eligibility: 'Farmers, ranchers, and agricultural businesses',
    details: {
      program_type: ['Grant', 'Loan', 'Cost Share', 'Technical Assistance'][Math.floor(Math.random() * 4)],
      application_deadline: new Date(Date.now() + Math.random() * 60 * 24 * 60 * 60 * 1000).toLocaleDateString(),
      funding_range: `$${(Math.random() * 100000 + 10000).toFixed(2)} - $${(Math.random() * 500000 + 100000).toFixed(2)}`,
      contact_email: '<EMAIL>'
    },
    url: 'https://www.fsa.usda.gov/programs-and-services/index',
    createdAt: new Date().toISOString()
  }));
};

const generateMockRuralDevelopmentGrants = (limit) => {
  console.log('Generating mock Rural Development grants...');
  return Array.from({ length: limit }, (_, i) => ({
    id: `rd-${i + 1}`,
    title: `Rural Development Grant Program ${i + 1}`,
    description: `This is a mock Rural Development grant program that provides assistance for rural communities and agricultural operations.`,
    agency: 'USDA Rural Development',
    category: ['rural development', 'housing', 'business', 'community facilities', 'utilities'][Math.floor(Math.random() * 5)],
    eligibility: 'Rural communities, farmers, ranchers, and agricultural businesses',
    fundingAmount: `$${(Math.random() * 300000 + 50000).toFixed(2)}`,
    closeDate: new Date(Date.now() + Math.random() * 60 * 24 * 60 * 60 * 1000).toISOString(),
    url: 'https://www.rd.usda.gov/programs-services',
    createdAt: new Date().toISOString(),
    programType: ['Grant', 'Loan', 'Loan Guarantee', 'Technical Assistance'][Math.floor(Math.random() * 4)],
    ruralAreaEligibility: Math.random() > 0.2 // 80% chance of being true
  }));
};

const generateMockNRCSGrants = (limit) => {
  console.log('Generating mock NRCS grants...');
  return Array.from({ length: limit }, (_, i) => ({
    id: `nrcs-${i + 1}`,
    title: `NRCS Conservation Program ${i + 1}`,
    description: `This is a mock NRCS program that provides assistance for conservation efforts on agricultural lands.`,
    agency: 'Natural Resources Conservation Service',
    category: 'conservation',
    eligibility: 'Farmers, ranchers, and agricultural landowners',
    details: {
      program_type: ['Grant', 'Cost Share', 'Technical Assistance', 'Easement'][Math.floor(Math.random() * 4)],
      application_deadline: new Date(Date.now() + Math.random() * 60 * 24 * 60 * 60 * 1000).toLocaleDateString(),
      funding_range: `$${(Math.random() * 100000 + 10000).toFixed(2)} - $${(Math.random() * 500000 + 100000).toFixed(2)}`,
      contact_email: '<EMAIL>'
    },
    url: 'https://www.nrcs.usda.gov/programs-initiatives',
    createdAt: new Date().toISOString(),
    conservationFocus: [
      ['soil health', 'erosion control', 'nutrient management'][Math.floor(Math.random() * 3)],
      ['water quality', 'water conservation', 'irrigation efficiency'][Math.floor(Math.random() * 3)],
      ['wildlife habitat', 'pollinator habitat', 'wetland restoration'][Math.floor(Math.random() * 3)]
    ].slice(0, Math.floor(Math.random() * 3) + 1) // Random 1-3 focus areas
  }));
};

const generateMockNIFAGrants = (limit) => {
  console.log('Generating mock NIFA grants...');
  return Array.from({ length: limit }, (_, i) => ({
    id: `nifa-${i + 1}`,
    title: `NIFA Research Grant ${i + 1}`,
    description: `This is a mock NIFA grant that provides funding for agricultural research, education, and extension activities.`,
    agency: 'National Institute of Food and Agriculture',
    category: 'research',
    eligibility: 'Universities, colleges, research institutions, and individuals',
    fundingAmount: `$${(Math.random() * 500000 + 100000).toFixed(2)}`,
    closeDate: new Date(Date.now() + Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString(),
    url: 'https://www.nifa.usda.gov/grants',
    createdAt: new Date().toISOString(),
    researchFocus: [
      ['agricultural research', 'crop improvement', 'livestock health'][Math.floor(Math.random() * 3)],
      ['education', 'agricultural literacy', 'workforce development'][Math.floor(Math.random() * 3)],
      ['extension', 'technology transfer', 'community outreach'][Math.floor(Math.random() * 3)]
    ].slice(0, Math.floor(Math.random() * 3) + 1) // Random 1-3 focus areas
  }));
};

const generateMockRMAPrograms = (limit) => {
  console.log('Generating mock RMA programs...');
  return Array.from({ length: limit }, (_, i) => ({
    id: `rma-${i + 1}`,
    title: `RMA Risk Management Program ${i + 1}`,
    description: `This is a mock RMA program that provides risk management tools for agricultural producers.`,
    agency: 'Risk Management Agency',
    category: 'risk management',
    eligibility: 'Agricultural producers',
    details: {
      program_type: ['Insurance', 'Education', 'Outreach', 'Research'][Math.floor(Math.random() * 4)],
      application_deadline: new Date(Date.now() + Math.random() * 60 * 24 * 60 * 60 * 1000).toLocaleDateString(),
      coverage_range: `${(Math.random() * 50 + 50).toFixed(0)}% - ${(Math.random() * 15 + 85).toFixed(0)}%`,
      contact_email: '<EMAIL>'
    },
    url: 'https://www.rma.usda.gov/en/Topics',
    createdAt: new Date().toISOString(),
    riskManagementType: ['Crop Insurance', 'Revenue Protection', 'Yield Protection', 'Whole-Farm Revenue Protection'][Math.floor(Math.random() * 4)],
    cropsCovered: [
      ['corn', 'soybeans', 'wheat', 'cotton', 'rice', 'sorghum', 'barley', 'oats', 'rye', 'potatoes', 'tomatoes', 'apples', 'grapes', 'oranges', 'almonds', 'walnuts', 'pecans']
      .sort(() => 0.5 - Math.random()).slice(0, Math.floor(Math.random() * 5) + 1) // Random 1-5 crops
    ]
  }));
};

const generateMockAMSGrants = (limit) => {
  console.log('Generating mock AMS grants...');
  return Array.from({ length: limit }, (_, i) => ({
    id: `ams-${i + 1}`,
    title: `AMS Marketing Grant ${i + 1}`,
    description: `This is a mock AMS grant that provides funding for agricultural marketing initiatives.`,
    agency: 'Agricultural Marketing Service',
    category: 'marketing',
    eligibility: 'Farmers, ranchers, producer groups, and agricultural businesses',
    fundingAmount: `$${(Math.random() * 200000 + 50000).toFixed(2)}`,
    closeDate: new Date(Date.now() + Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString(),
    url: 'https://www.ams.usda.gov/services/grants',
    createdAt: new Date().toISOString(),
    marketingFocus: [
      ['local food', 'regional food systems', 'farm to school'][Math.floor(Math.random() * 3)],
      ['farmers markets', 'food hubs', 'community supported agriculture'][Math.floor(Math.random() * 3)],
      ['value-added', 'organic', 'specialty crops'][Math.floor(Math.random() * 3)]
    ].slice(0, Math.floor(Math.random() * 3) + 1) // Random 1-3 focus areas
  }));
};

const generateMockDataGovGrants = (category, limit) => {
  console.log('Generating mock Data.gov grants...');
  return Array.from({ length: limit }, (_, i) => ({
    id: `datagov-${i + 1}`,
    title: `Data.gov Agricultural Grant ${i + 1}`,
    description: `This is a mock data.gov grant for agricultural projects. It provides funding for various agricultural initiatives including sustainable farming, technology adoption, and rural development.`,
    agency: ['Department of Agriculture', 'Department of Commerce', 'Department of Energy', 'Environmental Protection Agency'][Math.floor(Math.random() * 4)],
    opportunityNumber: `DATAGOV-AG-2023-${i + 1}`,
    category: category,
    eligibility: 'Farmers, ranchers, agricultural businesses, research institutions, and non-profit organizations',
    fundingAmount: `$${(Math.random() * 500000 + 50000).toFixed(2)}`,
    closeDate: new Date(Date.now() + Math.random() * 60 * 24 * 60 * 60 * 1000).toISOString(),
    url: 'https://www.data.gov',
    createdAt: new Date().toISOString(),
    source: 'data.gov'
  }));
};

// API configuration
const GRANTS_GOV_API_URL = process.env.GRANTS_GOV_API_URL || 'https://www.grants.gov/grantsws/rest';
const GRANTS_GOV_API_KEY = process.env.GRANTS_GOV_API_KEY;
const FARMERS_GOV_API_URL = process.env.FARMERS_GOV_API_URL || 'https://www.farmers.gov/api';
const FARMERS_GOV_API_KEY = process.env.FARMERS_GOV_API_KEY;
const USDA_ARMS_API_URL = process.env.USDA_ARMS_API_URL || 'https://api.ers.usda.gov/arms';
const USDA_ARMS_API_KEY = process.env.USDA_ARMS_API_KEY;
const FARM_SERVICE_AGENCY_API_URL = process.env.FARM_SERVICE_AGENCY_API_URL || 'https://api.fsa.usda.gov';
const FARM_SERVICE_AGENCY_API_KEY = process.env.FARM_SERVICE_AGENCY_API_KEY;
const RURAL_DEVELOPMENT_API_URL = process.env.RURAL_DEVELOPMENT_API_URL || 'https://api.rd.usda.gov/v1';
const RURAL_DEVELOPMENT_API_KEY = process.env.RURAL_DEVELOPMENT_API_KEY;
const NRCS_API_URL = process.env.NRCS_API_URL || 'https://api.nrcs.usda.gov/v1';
const NRCS_API_KEY = process.env.NRCS_API_KEY;
const NIFA_API_URL = process.env.NIFA_API_URL || 'https://api.nifa.usda.gov/v1';
const NIFA_API_KEY = process.env.NIFA_API_KEY;
const RMA_API_URL = process.env.RMA_API_URL || 'https://api.rma.usda.gov/v1';
const RMA_API_KEY = process.env.RMA_API_KEY;
const AMS_API_URL = process.env.AMS_API_URL || 'https://api.ams.usda.gov/v1';
const AMS_API_KEY = process.env.AMS_API_KEY;
const DATA_GOV_API_URL = process.env.DATA_GOV_API_URL || 'https://api.data.gov';
const DATA_GOV_API_KEY = process.env.DATA_GOV_API_KEY;

// Configure API clients
const grantsGovClient = axios.create({
  baseURL: GRANTS_GOV_API_URL,
  headers: {
    'Authorization': `Bearer ${GRANTS_GOV_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

const dataGovClient = axios.create({
  baseURL: DATA_GOV_API_URL,
  params: {
    'api_key': DATA_GOV_API_KEY
  },
  headers: {
    'Content-Type': 'application/json'
  }
});

const farmersGovClient = axios.create({
  baseURL: FARMERS_GOV_API_URL,
  headers: {
    'Authorization': `Bearer ${FARMERS_GOV_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

const usdaArmsClient = axios.create({
  baseURL: USDA_ARMS_API_URL,
  headers: {
    'Authorization': `Bearer ${USDA_ARMS_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

const farmServiceAgencyClient = axios.create({
  baseURL: FARM_SERVICE_AGENCY_API_URL,
  headers: {
    'Authorization': `Bearer ${FARM_SERVICE_AGENCY_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

const ruralDevelopmentClient = axios.create({
  baseURL: RURAL_DEVELOPMENT_API_URL,
  headers: {
    'Authorization': `Bearer ${RURAL_DEVELOPMENT_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

const nrcsClient = axios.create({
  baseURL: NRCS_API_URL,
  headers: {
    'Authorization': `Bearer ${NRCS_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

const nifaClient = axios.create({
  baseURL: NIFA_API_URL,
  headers: {
    'Authorization': `Bearer ${NIFA_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

const rmaClient = axios.create({
  baseURL: RMA_API_URL,
  headers: {
    'Authorization': `Bearer ${RMA_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

const amsClient = axios.create({
  baseURL: AMS_API_URL,
  headers: {
    'Authorization': `Bearer ${AMS_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

/**
 * Fetch grants from grants.gov API
 */
export const fetchGrantsGovGrants = async (req, res) => {
  try {
    const { category = 'agriculture', limit = 20 } = req.query;

    let grants = [];

    // Check if API key is configured
    if (!GRANTS_GOV_API_KEY || GRANTS_GOV_API_KEY === 'your_grants_gov_api_key') {
      console.warn('Grants.gov API key not configured. Using mock data as fallback.');
      // Use mock data as fallback
      grants = generateMockGrantsGovGrants(category, limit);
    } else {
      try {
        // Try to fetch from the real API
        console.log('Fetching grants from Grants.gov API...');
        const response = await grantsGovClient.get('/opportunities', {
          params: {
            keyword: category,
            limit: limit
          }
        });

        // Transform the API response to match our expected format
        grants = response.data.opportunities.map(opportunity => ({
          id: opportunity.id,
          title: opportunity.title,
          description: opportunity.description || 'No description available',
          agency: opportunity.agency,
          opportunityNumber: opportunity.opportunityNumber,
          category: category,
          eligibility: opportunity.eligibility || 'See grant details for eligibility information',
          fundingAmount: opportunity.awardCeiling ? `$${opportunity.awardCeiling}` : 'See grant details for funding information',
          closeDate: opportunity.closeDate,
          url: `https://www.grants.gov/web/grants/view-opportunity.html?oppId=${opportunity.id}`,
          createdAt: opportunity.postDate || new Date().toISOString()
        }));
        console.log(`Successfully fetched ${grants.length} grants from Grants.gov API`);
      } catch (apiError) {
        console.warn('Failed to fetch from grants.gov API, using mock data as fallback:', apiError);
        // Use mock data as fallback
        grants = generateMockGrantsGovGrants(category, limit);
      }
    }

    res.json(grants.slice(0, limit));
  } catch (error) {
    console.error('Error fetching grants.gov grants:', error);
    res.status(500).json({ error: 'Failed to fetch grants from grants.gov' });
  }
};

/**
 * Fetch USDA grants from ARMS Data API
 */
export const fetchUSDAGrants = async (req, res) => {
  try {
    const { limit = 20 } = req.query;

    let grants = [];

    // Check if API key is configured
    if (!USDA_ARMS_API_KEY || USDA_ARMS_API_KEY === 'your_usda_arms_api_key') {
      console.warn('USDA ARMS API key not configured. Using mock data as fallback.');
      // Use mock data as fallback
      grants = generateMockUSDAGrants(limit);
    } else {
      try {
        // Try to fetch from the real API
        console.log('Fetching grants from USDA ARMS API...');
        const response = await usdaArmsClient.get('/programs', {
          params: {
            limit: limit
          }
        });

        // Transform the API response to match our expected format
        grants = response.data.programs.map(program => ({
          id: program.id,
          title: program.title,
          description: program.description || 'No description available',
          category: program.category || 'agriculture',
          eligibility: program.eligibility || 'See program details for eligibility information',
          details: {
            program_type: program.type || 'Grant',
            application_deadline: program.deadline || new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toLocaleDateString(),
            funding_range: program.fundingRange || 'See program details for funding information',
            contact_email: program.contactEmail || '<EMAIL>'
          },
          url: program.url || 'https://www.ers.usda.gov/developer/data-apis/arms-data-api/'
        }));
        console.log(`Successfully fetched ${grants.length} grants from USDA ARMS API`);
      } catch (apiError) {
        console.warn('Failed to fetch from USDA ARMS API, using mock data as fallback:', apiError);
        // Use mock data as fallback
        grants = generateMockUSDAGrants(limit);
      }
    }

    res.json(grants.slice(0, limit));
  } catch (error) {
    console.error('Error fetching USDA grants:', error);
    res.status(500).json({ error: 'Failed to fetch grants from USDA' });
  }
};

/**
 * Fetch grants from farmers.gov API
 */
export const fetchFarmersGovGrants = async (req, res) => {
  try {
    const { limit = 20 } = req.query;

    let grants = [];

    // Check if API key is configured
    if (!FARMERS_GOV_API_KEY || FARMERS_GOV_API_KEY === 'your_farmers_gov_api_key') {
      console.warn('Farmers.gov API key not configured. Using mock data as fallback.');
      // Use mock data as fallback
      grants = generateMockFarmersGovGrants(limit);
    } else {
      try {
        // Try to fetch from the real API
        console.log('Fetching grants from Farmers.gov API...');
        const response = await farmersGovClient.get('/programs', {
          params: {
            type: 'grant',
            limit: limit
          }
        });

        // Transform the API response to match our expected format
        grants = response.data.programs.map(program => ({
          id: `farmers-${program.id}`,
          title: program.title,
          description: program.description || 'No description available',
          agency: program.agency || 'USDA',
          category: program.category || 'agriculture',
          eligibility: program.eligibility || 'See program details for eligibility information',
          fundingAmount: program.fundingAmount || 'See program details for funding information',
          closeDate: program.deadline || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          url: program.url || 'https://www.farmers.gov/fund',
          createdAt: program.publishDate || new Date().toISOString()
        }));
        console.log(`Successfully fetched ${grants.length} grants from Farmers.gov API`);
      } catch (apiError) {
        console.warn('Failed to fetch from farmers.gov API, using mock data as fallback:', apiError);
        // Use mock data as fallback
        grants = generateMockFarmersGovGrants(limit);
      }
    }

    res.json(grants.slice(0, limit));
  } catch (error) {
    console.error('Error fetching farmers.gov grants:', error);
    res.status(500).json({ error: 'Failed to fetch grants from farmers.gov' });
  }
};

/**
 * Fetch grants from Farm Service Agency API
 */
export const fetchFSAGrants = async (req, res) => {
  try {
    const { limit = 20 } = req.query;

    let grants = [];

    // Check if API key is configured
    if (!FARM_SERVICE_AGENCY_API_KEY || FARM_SERVICE_AGENCY_API_KEY === 'your_fsa_api_key') {
      console.warn('Farm Service Agency API key not configured. Using mock data as fallback.');
      // Use mock data as fallback
      grants = generateMockFSAGrants(limit);
    } else {
      try {
        // Try to fetch from the real API
        console.log('Fetching grants from Farm Service Agency API...');
        const response = await farmServiceAgencyClient.get('/programs', {
          params: {
            type: 'grant',
            limit: limit
          }
        });

        // Transform the API response to match our expected format
        grants = response.data.programs.map(program => ({
          id: `fsa-${program.id}`,
          title: program.title,
          description: program.description || 'No description available',
          agency: 'Farm Service Agency',
          category: program.category || 'agriculture',
          eligibility: program.eligibility || 'See program details for eligibility information',
          details: {
            program_type: program.type || 'Grant',
            application_deadline: program.deadline || new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toLocaleDateString(),
            funding_range: program.fundingRange || 'See program details for funding information',
            contact_email: program.contactEmail || '<EMAIL>'
          },
          url: program.url || 'https://www.fsa.usda.gov/programs-and-services/index',
          createdAt: program.publishDate || new Date().toISOString()
        }));
        console.log(`Successfully fetched ${grants.length} grants from Farm Service Agency API`);
      } catch (apiError) {
        console.warn('Failed to fetch from Farm Service Agency API, using mock data as fallback:', apiError);
        // Use mock data as fallback
        grants = generateMockFSAGrants(limit);
      }
    }

    res.json(grants.slice(0, limit));
  } catch (error) {
    console.error('Error fetching FSA grants:', error);
    res.status(500).json({ error: 'Failed to fetch grants from Farm Service Agency' });
  }
};

/**
 * Fetch grants from USDA Rural Development API
 */
export const fetchRuralDevelopmentGrants = async (req, res) => {
  try {
    const { limit = 20 } = req.query;

    let grants = [];

    // Check if API key is configured
    if (!RURAL_DEVELOPMENT_API_KEY || RURAL_DEVELOPMENT_API_KEY === 'your_rural_development_api_key') {
      console.warn('USDA Rural Development API key not configured. Using mock data as fallback.');
      // Use mock data as fallback
      grants = generateMockRuralDevelopmentGrants(limit);
    } else {
      try {
        // Try to fetch from the real API
        console.log('Fetching grants from USDA Rural Development API...');
        const response = await ruralDevelopmentClient.get('/programs', {
          params: {
            type: 'grant',
            limit: limit
          }
        });

        // Transform the API response to match our expected format
        grants = response.data.programs.map(program => ({
          id: `rd-${program.id}`,
          title: program.title,
          description: program.description || 'No description available',
          agency: 'USDA Rural Development',
          category: program.category || 'rural development',
          eligibility: program.eligibility || 'See program details for eligibility information',
          fundingAmount: program.fundingAmount || 'See program details for funding information',
          closeDate: program.deadline || new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(),
          url: program.url || 'https://www.rd.usda.gov/programs-services',
          createdAt: program.publishDate || new Date().toISOString(),
          programType: program.programType || 'Grant',
          ruralAreaEligibility: program.ruralAreaEligibility || true
        }));
        console.log(`Successfully fetched ${grants.length} grants from USDA Rural Development API`);
      } catch (apiError) {
        console.warn('Failed to fetch from Rural Development API, using mock data as fallback:', apiError);
        // Use mock data as fallback
        grants = generateMockRuralDevelopmentGrants(limit);
      }
    }

    res.json(grants.slice(0, limit));
  } catch (error) {
    console.error('Error fetching Rural Development grants:', error);
    res.status(500).json({ error: 'Failed to fetch grants from USDA Rural Development' });
  }
};

/**
 * Fetch grants from NRCS (Natural Resources Conservation Service) API
 */
export const fetchNRCSGrants = async (req, res) => {
  try {
    const { limit = 20 } = req.query;

    let grants = [];

    // Check if API key is configured
    if (!NRCS_API_KEY || NRCS_API_KEY === 'your_nrcs_api_key') {
      console.warn('NRCS API key not configured. Using mock data as fallback.');
      // Use mock data as fallback
      grants = generateMockNRCSGrants(limit);
    } else {
      try {
        // Try to fetch from the real API
        console.log('Fetching grants from NRCS API...');
        const response = await nrcsClient.get('/programs', {
          params: {
            type: 'grant',
            limit: limit
          }
        });

        // Transform the API response to match our expected format
        grants = response.data.programs.map(program => ({
          id: `nrcs-${program.id}`,
          title: program.title,
          description: program.description || 'No description available',
          agency: 'Natural Resources Conservation Service',
          category: program.category || 'conservation',
          eligibility: program.eligibility || 'See program details for eligibility information',
          details: {
            program_type: program.type || 'Grant',
            application_deadline: program.deadline || new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toLocaleDateString(),
            funding_range: program.fundingRange || 'See program details for funding information',
            contact_email: program.contactEmail || '<EMAIL>'
          },
          url: program.url || 'https://www.nrcs.usda.gov/programs-initiatives',
          createdAt: program.publishDate || new Date().toISOString(),
          conservationFocus: program.conservationFocus || ['soil health', 'water quality', 'wildlife habitat']
        }));
        console.log(`Successfully fetched ${grants.length} grants from NRCS API`);
      } catch (apiError) {
        console.warn('Failed to fetch from NRCS API, using mock data as fallback:', apiError);
        // Use mock data as fallback
        grants = generateMockNRCSGrants(limit);
      }
    }

    res.json(grants.slice(0, limit));
  } catch (error) {
    console.error('Error fetching NRCS grants:', error);
    res.status(500).json({ error: 'Failed to fetch grants from Natural Resources Conservation Service' });
  }
};

/**
 * Fetch grants from NIFA (National Institute of Food and Agriculture) API
 */
export const fetchNIFAGrants = async (req, res) => {
  try {
    const { limit = 20 } = req.query;

    let grants = [];

    // Check if API key is configured
    if (!NIFA_API_KEY || NIFA_API_KEY === 'your_nifa_api_key') {
      console.warn('NIFA API key not configured. Using mock data as fallback.');
      // Use mock data as fallback
      grants = generateMockNIFAGrants(limit);
    } else {
      try {
        // Try to fetch from the real API
        console.log('Fetching grants from NIFA API...');
        const response = await nifaClient.get('/programs', {
          params: {
            type: 'grant',
            limit: limit
          }
        });

        // Transform the API response to match our expected format
        grants = response.data.programs.map(program => ({
          id: `nifa-${program.id}`,
          title: program.title,
          description: program.description || 'No description available',
          agency: 'National Institute of Food and Agriculture',
          category: program.category || 'research',
          eligibility: program.eligibility || 'See program details for eligibility information',
          fundingAmount: program.fundingAmount || 'See program details for funding information',
          closeDate: program.deadline || new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(),
          url: program.url || 'https://www.nifa.usda.gov/grants',
          createdAt: program.publishDate || new Date().toISOString(),
          researchFocus: program.researchFocus || ['agricultural research', 'education', 'extension']
        }));
        console.log(`Successfully fetched ${grants.length} grants from NIFA API`);
      } catch (apiError) {
        console.warn('Failed to fetch from NIFA API, using mock data as fallback:', apiError);
        // Use mock data as fallback
        grants = generateMockNIFAGrants(limit);
      }
    }

    res.json(grants.slice(0, limit));
  } catch (error) {
    console.error('Error fetching NIFA grants:', error);
    res.status(500).json({ error: 'Failed to fetch grants from National Institute of Food and Agriculture' });
  }
};

/**
 * Fetch programs from RMA (Risk Management Agency) API
 */
export const fetchRMAPrograms = async (req, res) => {
  try {
    const { limit = 20 } = req.query;

    let programs = [];

    // Check if API key is configured
    if (!RMA_API_KEY || RMA_API_KEY === 'your_rma_api_key') {
      console.warn('RMA API key not configured. Returning empty results.');
      // Return empty array instead of using mock data
      programs = [];
    } else {
      try {
        // Try to fetch from the real API
        console.log('Fetching programs from RMA API...');
        const response = await rmaClient.get('/programs', {
          params: {
            limit: limit
          }
        });

        // Transform the API response to match our expected format
        programs = response.data.programs.map(program => ({
          id: `rma-${program.id}`,
          title: program.title,
          description: program.description || 'No description available',
          agency: 'Risk Management Agency',
          category: program.category || 'risk management',
          eligibility: program.eligibility || 'See program details for eligibility information',
          details: {
            program_type: program.type || 'Insurance',
            application_deadline: program.deadline || new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toLocaleDateString(),
            coverage_range: program.coverageRange || 'See program details for coverage information',
            contact_email: program.contactEmail || '<EMAIL>'
          },
          url: program.url || 'https://www.rma.usda.gov/en/Topics',
          createdAt: program.publishDate || new Date().toISOString(),
          riskManagementType: program.riskManagementType || 'Crop Insurance',
          cropsCovered: program.cropsCovered || ['corn', 'soybeans', 'wheat']
        }));
        console.log(`Successfully fetched ${programs.length} programs from RMA API`);
      } catch (apiError) {
        console.warn('Failed to fetch from RMA API, returning empty results:', apiError);
        // Return empty array instead of using mock data
        programs = [];
      }
    }

    res.json(programs.slice(0, limit));
  } catch (error) {
    console.error('Error fetching RMA programs:', error);
    res.status(500).json({ error: 'Failed to fetch programs from Risk Management Agency' });
  }
};

/**
 * Fetch grants from AMS (Agricultural Marketing Service) API
 */
export const fetchAMSGrants = async (req, res) => {
  try {
    const { limit = 20 } = req.query;

    let grants = [];

    // Check if API key is configured
    if (!AMS_API_KEY || AMS_API_KEY === 'your_ams_api_key') {
      console.warn('AMS API key not configured. Using mock data as fallback.');
      // Use mock data as fallback
      grants = generateMockAMSGrants(limit);
    } else {
      try {
        // Try to fetch from the real API
        console.log('Fetching grants from AMS API...');
        const response = await amsClient.get('/programs', {
          params: {
            type: 'grant',
            limit: limit
          }
        });

        // Transform the API response to match our expected format
        grants = response.data.programs.map(program => ({
          id: `ams-${program.id}`,
          title: program.title,
          description: program.description || 'No description available',
          agency: 'Agricultural Marketing Service',
          category: program.category || 'marketing',
          eligibility: program.eligibility || 'See program details for eligibility information',
          fundingAmount: program.fundingAmount || 'See program details for funding information',
          closeDate: program.deadline || new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(),
          url: program.url || 'https://www.ams.usda.gov/services/grants',
          createdAt: program.publishDate || new Date().toISOString(),
          marketingFocus: program.marketingFocus || ['local food', 'farmers markets', 'value-added']
        }));
        console.log(`Successfully fetched ${grants.length} grants from AMS API`);
      } catch (apiError) {
        console.warn('Failed to fetch from AMS API, using mock data as fallback:', apiError);
        // Use mock data as fallback
        grants = generateMockAMSGrants(limit);
      }
    }

    res.json(grants.slice(0, limit));
  } catch (error) {
    console.error('Error fetching AMS grants:', error);
    res.status(500).json({ error: 'Failed to fetch grants from Agricultural Marketing Service' });
  }
};

/**
 * Fetch agricultural grants from data.gov API
 */
export const fetchDataGovGrants = async (req, res) => {
  try {
    const { category = 'agriculture', limit = 20 } = req.query;

    let grants = [];

    // Check if API key is configured
    if (!DATA_GOV_API_KEY || DATA_GOV_API_KEY === 'your_data_gov_api_key') {
      console.warn('Data.gov API key not configured. Using mock data as fallback.');
      // Use mock data as fallback
      grants = generateMockDataGovGrants(category, limit);
    } else {
      try {
        // Try to fetch from the data.gov API
        console.log('Fetching grants from Data.gov API...');
        // The CFDA (Catalog of Federal Domestic Assistance) dataset contains information about federal grants
        const response = await dataGovClient.get('/catalog.json', {
          params: {
            q: category,
            fq: 'grant',
            rows: limit
          }
        });

        // Transform the API response to match our expected format
        if (response.data && response.data.dataset) {
          grants = response.data.dataset.map(dataset => ({
            id: `datagov-${dataset.identifier || Math.random().toString(36).substring(2, 15)}`,
            title: dataset.title || 'Untitled Grant',
            description: dataset.description || 'No description available',
            agency: dataset.publisher?.name || 'U.S. Government',
            opportunityNumber: dataset.identifier || '',
            category: dataset.theme?.[0] || category,
            eligibility: 'See grant details for eligibility information',
            fundingAmount: 'See grant details for funding information',
            closeDate: dataset.modified || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            url: dataset.landingPage || 'https://www.data.gov',
            createdAt: dataset.issued || new Date().toISOString(),
            source: 'data.gov'
          }));
          console.log(`Successfully fetched ${grants.length} grants from Data.gov API`);
        } else {
          console.warn('No dataset found in Data.gov API response, using mock data as fallback.');
          grants = generateMockDataGovGrants(category, limit);
        }
      } catch (apiError) {
        console.warn('Failed to fetch from data.gov API, using mock data as fallback:', apiError);
        // Use mock data as fallback
        grants = generateMockDataGovGrants(category, limit);
      }
    }

    res.json(grants.slice(0, limit));
  } catch (error) {
    console.error('Error fetching data.gov grants:', error);
    res.status(500).json({ error: 'Failed to fetch grants from data.gov' });
  }
};

/**
 * Search for grants by keyword
 */
export const searchGrants = async (req, res) => {
  try {
    const { keyword, source = 'all', category, limit = 50 } = req.query;

    // Fetch grants from all sources based on the source parameter
    let results = [];

    if (source === 'all' || source === 'grants-gov') {
      try {
        const grantsGovResponse = await axios.get(`${req.protocol}://${req.get('host')}/api/grants/grants-gov`, {
          params: { category, limit }
        });
        results = [...results, ...grantsGovResponse.data];
      } catch (error) {
        console.error('Error fetching grants.gov grants for search:', error);
      }
    }

    if (source === 'all' || source === 'usda') {
      try {
        const usdaResponse = await axios.get(`${req.protocol}://${req.get('host')}/api/grants/usda`, {
          params: { limit }
        });
        results = [...results, ...usdaResponse.data];
      } catch (error) {
        console.error('Error fetching USDA grants for search:', error);
      }
    }

    if (source === 'all' || source === 'farmers-gov') {
      try {
        const farmersGovResponse = await axios.get(`${req.protocol}://${req.get('host')}/api/grants/farmers-gov`, {
          params: { limit }
        });
        results = [...results, ...farmersGovResponse.data];
      } catch (error) {
        console.error('Error fetching farmers.gov grants for search:', error);
      }
    }

    if (source === 'all' || source === 'fsa') {
      try {
        const fsaResponse = await axios.get(`${req.protocol}://${req.get('host')}/api/grants/fsa`, {
          params: { limit }
        });
        results = [...results, ...fsaResponse.data];
      } catch (error) {
        console.error('Error fetching FSA grants for search:', error);
      }
    }

    if (source === 'all' || source === 'rural-development') {
      try {
        const ruralDevelopmentResponse = await axios.get(`${req.protocol}://${req.get('host')}/api/grants/rural-development`, {
          params: { limit }
        });
        results = [...results, ...ruralDevelopmentResponse.data];
      } catch (error) {
        console.error('Error fetching Rural Development grants for search:', error);
      }
    }

    if (source === 'all' || source === 'nrcs') {
      try {
        const nrcsResponse = await axios.get(`${req.protocol}://${req.get('host')}/api/grants/nrcs`, {
          params: { limit }
        });
        results = [...results, ...nrcsResponse.data];
      } catch (error) {
        console.error('Error fetching NRCS grants for search:', error);
      }
    }

    if (source === 'all' || source === 'nifa') {
      try {
        const nifaResponse = await axios.get(`${req.protocol}://${req.get('host')}/api/grants/nifa`, {
          params: { limit }
        });
        results = [...results, ...nifaResponse.data];
      } catch (error) {
        console.error('Error fetching NIFA grants for search:', error);
      }
    }

    if (source === 'all' || source === 'rma') {
      try {
        const rmaResponse = await axios.get(`${req.protocol}://${req.get('host')}/api/grants/rma`, {
          params: { limit }
        });
        results = [...results, ...rmaResponse.data];
      } catch (error) {
        console.error('Error fetching RMA programs for search:', error);
      }
    }

    if (source === 'all' || source === 'ams') {
      try {
        const amsResponse = await axios.get(`${req.protocol}://${req.get('host')}/api/grants/ams`, {
          params: { limit }
        });
        results = [...results, ...amsResponse.data];
      } catch (error) {
        console.error('Error fetching AMS grants for search:', error);
      }
    }

    if (source === 'all' || source === 'data-gov') {
      try {
        const dataGovResponse = await axios.get(`${req.protocol}://${req.get('host')}/api/grants/data-gov`, {
          params: { category, limit }
        });
        results = [...results, ...dataGovResponse.data];
      } catch (error) {
        console.error('Error fetching data.gov grants for search:', error);
      }
    }

    // Filter by keyword if provided
    if (keyword) {
      const term = keyword.toLowerCase();
      results = results.filter(grant => 
        grant.title.toLowerCase().includes(term) || 
        grant.description.toLowerCase().includes(term) ||
        (grant.agency && grant.agency.toLowerCase().includes(term)) ||
        (grant.category && grant.category.toLowerCase().includes(term)) ||
        (grant.eligibility && grant.eligibility.toLowerCase().includes(term))
      );
    }

    // Filter by category if provided
    if (category && category !== 'all') {
      results = results.filter(grant => 
        grant.category && grant.category.toLowerCase().includes(category.toLowerCase())
      );
    }

    // Sort results by close date (if available) or created date
    results.sort((a, b) => {
      if (a.closeDate && b.closeDate) {
        return new Date(a.closeDate) - new Date(b.closeDate);
      } else if (a.closeDate) {
        return -1;
      } else if (b.closeDate) {
        return 1;
      } else {
        return new Date(b.createdAt || Date.now()) - new Date(a.createdAt || Date.now());
      }
    });

    // Limit the number of results
    results = results.slice(0, limit);

    res.json(results);
  } catch (error) {
    console.error('Error searching grants:', error);
    res.status(500).json({ error: 'Failed to search grants' });
  }
};

/**
 * Get grant details by ID
 */
export const getGrantDetails = async (req, res) => {
  try {
    const { id } = req.params;
    const { source } = req.params;

    // Attempt to fetch from the appropriate API based on the source
    let grant = null;

    if (source === 'grants-gov') {
      try {
        // Try to fetch from the real API
        const response = await grantsGovClient.get(`/opportunities/${id}`);

        // Transform the API response to match our expected format
        const opportunity = response.data;
        grant = {
          id: opportunity.id,
          title: opportunity.title,
          description: opportunity.description || 'No description available',
          agency: opportunity.agency,
          opportunityNumber: opportunity.opportunityNumber,
          category: opportunity.category || 'agriculture',
          eligibility: opportunity.eligibility || 'See grant details for eligibility information',
          fundingAmount: opportunity.awardCeiling ? `$${opportunity.awardCeiling}` : 'See grant details for funding information',
          closeDate: opportunity.closeDate,
          url: `https://www.grants.gov/web/grants/view-opportunity.html?oppId=${opportunity.id}`,
          createdAt: opportunity.postDate || new Date().toISOString()
        };
      } catch (apiError) {
        console.warn(`Failed to fetch grant details from grants.gov API for ID ${id}:`, apiError);
        throw new Error(`Could not retrieve grant details from grants.gov API for ID ${id}`);
      }
    } else if (source === 'usda') {
      try {
        // Try to fetch from the real API
        const response = await usdaArmsClient.get(`/programs/${id}`);

        // Transform the API response to match our expected format
        const program = response.data;
        grant = {
          id: program.id,
          title: program.title,
          description: program.description || 'No description available',
          category: program.category || 'agriculture',
          eligibility: program.eligibility || 'See program details for eligibility information',
          details: {
            program_type: program.type || 'Grant',
            application_deadline: program.deadline || new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toLocaleDateString(),
            funding_range: program.fundingRange || 'See program details for funding information',
            contact_email: program.contactEmail || '<EMAIL>',
            program_objectives: program.objectives || 'To support sustainable agricultural practices and improve farm productivity.',
            eligible_expenses: program.eligibleExpenses || 'Equipment purchases, land improvements, conservation measures, training and education.'
          },
          url: program.url || 'https://www.ers.usda.gov/developer/data-apis/arms-data-api/'
        };
      } catch (apiError) {
        console.warn(`Failed to fetch grant details from USDA ARMS API for ID ${id}:`, apiError);
        throw new Error(`Could not retrieve grant details from USDA ARMS API for ID ${id}`);
      }
    } else if (source === 'farmers-gov') {
      try {
        // Try to fetch from the real API
        const response = await farmersGovClient.get(`/programs/${id.replace('farmers-', '')}`);

        // Transform the API response to match our expected format
        const program = response.data;
        grant = {
          id: `farmers-${program.id}`,
          title: program.title,
          description: program.description || 'No description available',
          agency: program.agency || 'USDA',
          category: program.category || 'agriculture',
          eligibility: program.eligibility || 'See program details for eligibility information',
          fundingAmount: program.fundingAmount || 'See program details for funding information',
          closeDate: program.deadline || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          url: program.url || 'https://www.farmers.gov/fund',
          createdAt: program.publishDate || new Date().toISOString()
        };
      } catch (apiError) {
        console.warn(`Failed to fetch grant details from farmers.gov API for ID ${id}:`, apiError);
        throw new Error(`Could not retrieve grant details from farmers.gov API for ID ${id}`);
      }
    } else if (source === 'fsa') {
      try {
        // Try to fetch from the real API
        const response = await farmServiceAgencyClient.get(`/programs/${id.replace('fsa-', '')}`);

        // Transform the API response to match our expected format
        const program = response.data;
        grant = {
          id: `fsa-${program.id}`,
          title: program.title,
          description: program.description || 'No description available',
          agency: 'Farm Service Agency',
          category: program.category || 'agriculture',
          eligibility: program.eligibility || 'See program details for eligibility information',
          details: {
            program_type: program.type || 'Grant',
            application_deadline: program.deadline || new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toLocaleDateString(),
            funding_range: program.fundingRange || 'See program details for funding information',
            contact_email: program.contactEmail || '<EMAIL>',
            program_objectives: program.objectives || 'To support agricultural operations and conservation efforts.',
            eligible_expenses: program.eligibleExpenses || 'Varies by program. See program details for more information.'
          },
          url: program.url || 'https://www.fsa.usda.gov/programs-and-services/index',
          createdAt: program.publishDate || new Date().toISOString()
        };
      } catch (apiError) {
        console.warn(`Failed to fetch grant details from FSA API for ID ${id}:`, apiError);
        throw new Error(`Could not retrieve grant details from FSA API for ID ${id}`);
      }
    } else if (source === 'rural-development') {
      try {
        // Try to fetch from the real API
        const response = await ruralDevelopmentClient.get(`/programs/${id.replace('rd-', '')}`);

        // Transform the API response to match our expected format
        const program = response.data;
        grant = {
          id: `rd-${program.id}`,
          title: program.title,
          description: program.description || 'No description available',
          agency: 'USDA Rural Development',
          category: program.category || 'rural development',
          eligibility: program.eligibility || 'See program details for eligibility information',
          fundingAmount: program.fundingAmount || 'See program details for funding information',
          closeDate: program.deadline || new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(),
          url: program.url || 'https://www.rd.usda.gov/programs-services',
          createdAt: program.publishDate || new Date().toISOString(),
          programType: program.programType || 'Grant',
          ruralAreaEligibility: program.ruralAreaEligibility || true
        };
      } catch (apiError) {
        console.warn(`Failed to fetch grant details from Rural Development API for ID ${id}:`, apiError);
        throw new Error(`Could not retrieve grant details from Rural Development API for ID ${id}`);
      }
    } else if (source === 'nrcs') {
      try {
        // Try to fetch from the real API
        const response = await nrcsClient.get(`/programs/${id.replace('nrcs-', '')}`);

        // Transform the API response to match our expected format
        const program = response.data;
        grant = {
          id: `nrcs-${program.id}`,
          title: program.title,
          description: program.description || 'No description available',
          agency: 'Natural Resources Conservation Service',
          category: program.category || 'conservation',
          eligibility: program.eligibility || 'See program details for eligibility information',
          details: {
            program_type: program.type || 'Grant',
            application_deadline: program.deadline || new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toLocaleDateString(),
            funding_range: program.fundingRange || 'See program details for funding information',
            contact_email: program.contactEmail || '<EMAIL>'
          },
          url: program.url || 'https://www.nrcs.usda.gov/programs-initiatives',
          createdAt: program.publishDate || new Date().toISOString(),
          conservationFocus: program.conservationFocus || ['soil health', 'water quality', 'wildlife habitat']
        };
      } catch (apiError) {
        console.warn(`Failed to fetch grant details from NRCS API for ID ${id}, using mock data instead:`, apiError);

        // Fallback to mock data if the API call fails
        grant = {
          id,
          title: `NRCS Conservation Program ${id.split('-')[1] || '1'}`,
          description: 'This is a detailed description of the NRCS program. It includes information about the conservation benefits, eligibility criteria, and how to apply.',
          agency: 'Natural Resources Conservation Service',
          category: 'conservation',
          eligibility: 'Farmers, ranchers, and agricultural landowners',
          details: {
            program_type: ['Grant', 'Cost Share', 'Technical Assistance', 'Easement'][Math.floor(Math.random() * 4)],
            application_deadline: new Date(Date.now() + Math.random() * 60 * 24 * 60 * 60 * 1000).toLocaleDateString(),
            funding_range: `$${(Math.random() * 100000 + 10000).toFixed(2)} - $${(Math.random() * 500000 + 100000).toFixed(2)}`,
            contact_email: '<EMAIL>'
          },
          url: 'https://www.nrcs.usda.gov/programs-initiatives',
          createdAt: new Date().toISOString(),
          conservationFocus: [
            ['soil health', 'erosion control', 'nutrient management'][Math.floor(Math.random() * 3)],
            ['water quality', 'water conservation', 'irrigation efficiency'][Math.floor(Math.random() * 3)],
            ['wildlife habitat', 'pollinator habitat', 'wetland restoration'][Math.floor(Math.random() * 3)]
          ].slice(0, Math.floor(Math.random() * 3) + 1) // Random 1-3 focus areas
        };
      }
    } else if (source === 'nifa') {
      try {
        // Try to fetch from the real API
        const response = await nifaClient.get(`/programs/${id.replace('nifa-', '')}`);

        // Transform the API response to match our expected format
        const program = response.data;
        grant = {
          id: `nifa-${program.id}`,
          title: program.title,
          description: program.description || 'No description available',
          agency: 'National Institute of Food and Agriculture',
          category: program.category || 'research',
          eligibility: program.eligibility || 'See program details for eligibility information',
          fundingAmount: program.fundingAmount || 'See program details for funding information',
          closeDate: program.deadline || new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(),
          url: program.url || 'https://www.nifa.usda.gov/grants',
          createdAt: program.publishDate || new Date().toISOString(),
          researchFocus: program.researchFocus || ['agricultural research', 'education', 'extension']
        };
      } catch (apiError) {
        console.warn(`Failed to fetch grant details from NIFA API for ID ${id}, using mock data instead:`, apiError);

        // Fallback to mock data if the API call fails
        grant = {
          id,
          title: `NIFA Research Grant ${id.split('-')[1] || '1'}`,
          description: 'This is a detailed description of the NIFA grant. It includes information about the research focus, eligibility criteria, and how to apply.',
          agency: 'National Institute of Food and Agriculture',
          category: 'research',
          eligibility: 'Universities, colleges, research institutions, and individuals',
          fundingAmount: `$${(Math.random() * 500000 + 100000).toFixed(2)}`,
          closeDate: new Date(Date.now() + Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString(),
          url: 'https://www.nifa.usda.gov/grants',
          createdAt: new Date().toISOString(),
          researchFocus: [
            ['agricultural research', 'crop improvement', 'livestock health'][Math.floor(Math.random() * 3)],
            ['education', 'agricultural literacy', 'workforce development'][Math.floor(Math.random() * 3)],
            ['extension', 'technology transfer', 'community outreach'][Math.floor(Math.random() * 3)]
          ].slice(0, Math.floor(Math.random() * 3) + 1) // Random 1-3 focus areas
        };
      }
    } else if (source === 'rma') {
      try {
        // Try to fetch from the real API
        const response = await rmaClient.get(`/programs/${id.replace('rma-', '')}`);

        // Transform the API response to match our expected format
        const program = response.data;
        grant = {
          id: `rma-${program.id}`,
          title: program.title,
          description: program.description || 'No description available',
          agency: 'Risk Management Agency',
          category: program.category || 'risk management',
          eligibility: program.eligibility || 'See program details for eligibility information',
          details: {
            program_type: program.type || 'Insurance',
            application_deadline: program.deadline || new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toLocaleDateString(),
            coverage_range: program.coverageRange || 'See program details for coverage information',
            contact_email: program.contactEmail || '<EMAIL>'
          },
          url: program.url || 'https://www.rma.usda.gov/en/Topics',
          createdAt: program.publishDate || new Date().toISOString(),
          riskManagementType: program.riskManagementType || 'Crop Insurance',
          cropsCovered: program.cropsCovered || ['corn', 'soybeans', 'wheat']
        };
      } catch (apiError) {
        console.warn(`Failed to fetch program details from RMA API for ID ${id}, using mock data instead:`, apiError);

        // Fallback to mock data if the API call fails
        grant = {
          id,
          title: `RMA Risk Management Program ${id.split('-')[1] || '1'}`,
          description: 'This is a detailed description of the RMA program. It includes information about the risk management tools, eligibility criteria, and how to apply.',
          agency: 'Risk Management Agency',
          category: 'risk management',
          eligibility: 'Agricultural producers',
          details: {
            program_type: ['Insurance', 'Education', 'Outreach', 'Research'][Math.floor(Math.random() * 4)],
            application_deadline: new Date(Date.now() + Math.random() * 60 * 24 * 60 * 60 * 1000).toLocaleDateString(),
            coverage_range: `${(Math.random() * 50 + 50).toFixed(0)}% - ${(Math.random() * 15 + 85).toFixed(0)}%`,
            contact_email: '<EMAIL>'
          },
          url: 'https://www.rma.usda.gov/en/Topics',
          createdAt: new Date().toISOString(),
          riskManagementType: ['Crop Insurance', 'Revenue Protection', 'Yield Protection', 'Whole-Farm Revenue Protection'][Math.floor(Math.random() * 4)],
          cropsCovered: [
            ['corn', 'soybeans', 'wheat', 'cotton', 'rice', 'sorghum', 'barley', 'oats', 'rye', 'potatoes', 'tomatoes', 'apples', 'grapes', 'oranges', 'almonds', 'walnuts', 'pecans']
            .sort(() => 0.5 - Math.random()).slice(0, Math.floor(Math.random() * 5) + 1) // Random 1-5 crops
          ]
        };
      }
    } else if (source === 'ams') {
      try {
        // Try to fetch from the real API
        const response = await amsClient.get(`/programs/${id.replace('ams-', '')}`);

        // Transform the API response to match our expected format
        const program = response.data;
        grant = {
          id: `ams-${program.id}`,
          title: program.title,
          description: program.description || 'No description available',
          agency: 'Agricultural Marketing Service',
          category: program.category || 'marketing',
          eligibility: program.eligibility || 'See program details for eligibility information',
          fundingAmount: program.fundingAmount || 'See program details for funding information',
          closeDate: program.deadline || new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(),
          url: program.url || 'https://www.ams.usda.gov/services/grants',
          createdAt: program.publishDate || new Date().toISOString(),
          marketingFocus: program.marketingFocus || ['local food', 'farmers markets', 'value-added']
        };
      } catch (apiError) {
        console.warn(`Failed to fetch grant details from AMS API for ID ${id}, using mock data instead:`, apiError);

        // Fallback to mock data if the API call fails
        grant = {
          id,
          title: `AMS Marketing Grant ${id.split('-')[1] || '1'}`,
          description: 'This is a detailed description of the AMS grant. It includes information about the marketing initiatives, eligibility criteria, and how to apply.',
          agency: 'Agricultural Marketing Service',
          category: 'marketing',
          eligibility: 'Farmers, ranchers, producer groups, and agricultural businesses',
          fundingAmount: `$${(Math.random() * 200000 + 50000).toFixed(2)}`,
          closeDate: new Date(Date.now() + Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString(),
          url: 'https://www.ams.usda.gov/services/grants',
          createdAt: new Date().toISOString(),
          marketingFocus: [
            ['local food', 'regional food systems', 'farm to school'][Math.floor(Math.random() * 3)],
            ['farmers markets', 'food hubs', 'community supported agriculture'][Math.floor(Math.random() * 3)],
            ['value-added', 'organic', 'specialty crops'][Math.floor(Math.random() * 3)]
          ].slice(0, Math.floor(Math.random() * 3) + 1) // Random 1-3 focus areas
        };
      }
    } else if (source === 'data-gov') {
      try {
        // Try to fetch from the real API
        // For data.gov, we would typically need to fetch the dataset details
        // Since we don't have a direct endpoint for a specific grant, we'll use mock data
        // In a real implementation, you might store the grant details in your database
        // or use a more specific data.gov API endpoint

        console.warn(`No direct API endpoint for data.gov grant details with ID ${id}, using mock data instead.`);

        // Fallback to mock data
        grant = {
          id,
          title: `Data.gov Agricultural Grant ${id.split('-')[1] || '1'}`,
          description: 'This grant provides funding for agricultural projects that promote sustainable farming practices, technology adoption, and rural development. The program aims to enhance agricultural productivity, environmental stewardship, and economic viability for farmers and rural communities.',
          agency: ['Department of Agriculture', 'Department of Commerce', 'Department of Energy', 'Environmental Protection Agency'][Math.floor(Math.random() * 4)],
          opportunityNumber: `DATAGOV-AG-2023-${id.split('-')[1] || '1'}`,
          category: 'agriculture',
          eligibility: 'Farmers, ranchers, agricultural businesses, research institutions, and non-profit organizations',
          fundingAmount: `$${(Math.random() * 500000 + 50000).toFixed(2)}`,
          closeDate: new Date(Date.now() + Math.random() * 60 * 24 * 60 * 60 * 1000).toISOString(),
          url: 'https://www.data.gov',
          createdAt: new Date().toISOString(),
          source: 'data.gov',
          details: {
            program_objectives: 'To support sustainable agricultural practices and improve farm productivity while promoting environmental stewardship.',
            eligible_expenses: 'Equipment purchases, land improvements, conservation measures, technology adoption, training and education.',
            application_process: 'Applications must be submitted online through the grants portal. Required documents include a project proposal, budget, timeline, and supporting documentation.',
            reporting_requirements: 'Quarterly progress reports and a final report are required. Financial reporting must be submitted according to federal guidelines.',
            contact_information: {
              email: '<EMAIL>',
              phone: '(*************',
              website: 'https://www.data.gov/agriculture/grants'
            }
          }
        };
      } catch (apiError) {
        console.warn(`Failed to fetch grant details from data.gov API for ID ${id}, using mock data instead:`, apiError);

        // Fallback to mock data if the API call fails
        grant = {
          id,
          title: `Data.gov Agricultural Grant ${id.split('-')[1] || '1'}`,
          description: 'This grant provides funding for agricultural projects that promote sustainable farming practices, technology adoption, and rural development.',
          agency: ['Department of Agriculture', 'Department of Commerce', 'Department of Energy', 'Environmental Protection Agency'][Math.floor(Math.random() * 4)],
          opportunityNumber: `DATAGOV-AG-2023-${id.split('-')[1] || '1'}`,
          category: 'agriculture',
          eligibility: 'Farmers, ranchers, agricultural businesses, research institutions, and non-profit organizations',
          fundingAmount: `$${(Math.random() * 500000 + 50000).toFixed(2)}`,
          closeDate: new Date(Date.now() + Math.random() * 60 * 24 * 60 * 60 * 1000).toISOString(),
          url: 'https://www.data.gov',
          createdAt: new Date().toISOString(),
          source: 'data.gov'
        };
      }
    } else {
      return res.status(400).json({ error: 'Invalid source specified' });
    }

    res.json(grant);
  } catch (error) {
    console.error('Error fetching grant details:', error);
    res.status(500).json({ error: 'Failed to fetch grant details' });
  }
};
