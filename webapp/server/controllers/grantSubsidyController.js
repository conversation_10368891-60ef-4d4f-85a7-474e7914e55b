import { Op } from 'sequelize';
import Farm from '../models/Farm.js';
import FarmGrant from '../models/FarmGrant.js';
import Subsidy from '../models/Subsidy.js';

/**
 * Get grants for a farm
 */
export const getGrants = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { status, searchTerm } = req.query;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Build query conditions
    const whereConditions = {
      farm_id: farmId
    };

    if (status && status !== 'all') {
      whereConditions.status = status;
    }

    if (searchTerm) {
      whereConditions[Op.or] = [
        { name: { [Op.iLike]: `%${searchTerm}%` } },
        { provider: { [Op.iLike]: `%${searchTerm}%` } },
        { description: { [Op.iLike]: `%${searchTerm}%` } }
      ];
    }

    // Get grants for the farm
    const grants = await FarmGrant.findAll({
      where: whereConditions,
      order: [['deadline', 'ASC']]
    });

    // Format the grants
    const formattedGrants = grants.map(grant => ({
      id: grant.id,
      name: grant.name,
      provider: grant.provider,
      amount: grant.amount,
      status: grant.status,
      deadline: grant.deadline,
      description: grant.description,
      requirements: grant.requirements,
      applicationDate: grant.application_date,
      approvalDate: grant.approval_date,
      disbursementDate: grant.disbursement_date,
      createdAt: grant.created_at,
      updatedAt: grant.updated_at
    }));

    return res.status(200).json({
      grants: formattedGrants
    });
  } catch (error) {
    console.error('Error getting grants:', error);
    return res.status(500).json({ error: 'Failed to get grants' });
  }
};

/**
 * Get a specific grant
 */
export const getGrant = async (req, res) => {
  try {
    const { grantId } = req.params;

    // Find the grant
    const grant = await FarmGrant.findByPk(grantId);
    if (!grant) {
      return res.status(404).json({ error: 'Grant not found' });
    }

    // Format the grant
    const formattedGrant = {
      id: grant.id,
      name: grant.name,
      provider: grant.provider,
      amount: grant.amount,
      status: grant.status,
      deadline: grant.deadline,
      description: grant.description,
      requirements: grant.requirements,
      applicationDate: grant.application_date,
      approvalDate: grant.approval_date,
      disbursementDate: grant.disbursement_date,
      createdAt: grant.created_at,
      updatedAt: grant.updated_at
    };

    return res.status(200).json({
      grant: formattedGrant
    });
  } catch (error) {
    console.error('Error getting grant:', error);
    return res.status(500).json({ error: 'Failed to get grant' });
  }
};

/**
 * Create a new grant
 */
export const createGrant = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { 
      name, 
      provider, 
      amount, 
      status = 'applied', 
      deadline, 
      description, 
      requirements = [],
      applicationDate
    } = req.body;

    // Validate required fields
    if (!name || !provider || !amount || !deadline) {
      return res.status(400).json({ error: 'Name, provider, amount, and deadline are required' });
    }

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Create the grant
    const grant = await FarmGrant.create({
      farm_id: farmId,
      name,
      provider,
      amount,
      status,
      deadline,
      description,
      requirements,
      application_date: applicationDate
    });

    return res.status(201).json({
      grant: {
        id: grant.id,
        name: grant.name,
        provider: grant.provider,
        amount: grant.amount,
        status: grant.status,
        deadline: grant.deadline,
        description: grant.description,
        requirements: grant.requirements,
        applicationDate: grant.application_date,
        createdAt: grant.created_at,
        updatedAt: grant.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating grant:', error);
    return res.status(500).json({ error: 'Failed to create grant' });
  }
};

/**
 * Update a grant
 */
export const updateGrant = async (req, res) => {
  try {
    const { grantId } = req.params;
    const { 
      name, 
      provider, 
      amount, 
      status, 
      deadline, 
      description, 
      requirements,
      applicationDate,
      approvalDate,
      disbursementDate
    } = req.body;

    // Find the grant
    const grant = await FarmGrant.findByPk(grantId);
    if (!grant) {
      return res.status(404).json({ error: 'Grant not found' });
    }

    // Update the grant
    if (name) grant.name = name;
    if (provider) grant.provider = provider;
    if (amount) grant.amount = amount;
    if (status) grant.status = status;
    if (deadline) grant.deadline = deadline;
    if (description !== undefined) grant.description = description;
    if (requirements) grant.requirements = requirements;
    if (applicationDate) grant.application_date = applicationDate;
    if (approvalDate) grant.approval_date = approvalDate;
    if (disbursementDate) grant.disbursement_date = disbursementDate;

    await grant.save();

    return res.status(200).json({
      grant: {
        id: grant.id,
        name: grant.name,
        provider: grant.provider,
        amount: grant.amount,
        status: grant.status,
        deadline: grant.deadline,
        description: grant.description,
        requirements: grant.requirements,
        applicationDate: grant.application_date,
        approvalDate: grant.approval_date,
        disbursementDate: grant.disbursement_date,
        createdAt: grant.created_at,
        updatedAt: grant.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating grant:', error);
    return res.status(500).json({ error: 'Failed to update grant' });
  }
};

/**
 * Delete a grant
 */
export const deleteGrant = async (req, res) => {
  try {
    const { grantId } = req.params;

    // Find the grant
    const grant = await FarmGrant.findByPk(grantId);
    if (!grant) {
      return res.status(404).json({ error: 'Grant not found' });
    }

    // Delete the grant
    await grant.destroy();

    return res.status(200).json({
      message: 'Grant deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting grant:', error);
    return res.status(500).json({ error: 'Failed to delete grant' });
  }
};

/**
 * Get subsidies for a farm
 */
export const getSubsidies = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { status, searchTerm } = req.query;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Build query conditions
    const whereConditions = {
      farm_id: farmId
    };

    if (status && status !== 'all') {
      whereConditions.status = status;
    }

    if (searchTerm) {
      whereConditions[Op.or] = [
        { name: { [Op.iLike]: `%${searchTerm}%` } },
        { provider: { [Op.iLike]: `%${searchTerm}%` } },
        { description: { [Op.iLike]: `%${searchTerm}%` } }
      ];
    }

    // Get subsidies for the farm
    const subsidies = await Subsidy.findAll({
      where: whereConditions,
      order: [['end_date', 'ASC']]
    });

    // Format the subsidies
    const formattedSubsidies = subsidies.map(subsidy => ({
      id: subsidy.id,
      name: subsidy.name,
      provider: subsidy.provider,
      amount: subsidy.amount,
      status: subsidy.status,
      startDate: subsidy.start_date,
      endDate: subsidy.end_date,
      description: subsidy.description,
      requirements: subsidy.requirements,
      lastPaymentDate: subsidy.last_payment_date,
      nextPaymentDate: subsidy.next_payment_date,
      createdAt: subsidy.created_at,
      updatedAt: subsidy.updated_at
    }));

    return res.status(200).json({
      subsidies: formattedSubsidies
    });
  } catch (error) {
    console.error('Error getting subsidies:', error);
    return res.status(500).json({ error: 'Failed to get subsidies' });
  }
};

/**
 * Get a specific subsidy
 */
export const getSubsidy = async (req, res) => {
  try {
    const { subsidyId } = req.params;

    // Find the subsidy
    const subsidy = await Subsidy.findByPk(subsidyId);
    if (!subsidy) {
      return res.status(404).json({ error: 'Subsidy not found' });
    }

    // Format the subsidy
    const formattedSubsidy = {
      id: subsidy.id,
      name: subsidy.name,
      provider: subsidy.provider,
      amount: subsidy.amount,
      status: subsidy.status,
      startDate: subsidy.start_date,
      endDate: subsidy.end_date,
      description: subsidy.description,
      requirements: subsidy.requirements,
      lastPaymentDate: subsidy.last_payment_date,
      nextPaymentDate: subsidy.next_payment_date,
      createdAt: subsidy.created_at,
      updatedAt: subsidy.updated_at
    };

    return res.status(200).json({
      subsidy: formattedSubsidy
    });
  } catch (error) {
    console.error('Error getting subsidy:', error);
    return res.status(500).json({ error: 'Failed to get subsidy' });
  }
};

/**
 * Create a new subsidy
 */
export const createSubsidy = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { 
      name, 
      provider, 
      amount, 
      status = 'active', 
      startDate, 
      endDate, 
      description, 
      requirements = [],
      lastPaymentDate,
      nextPaymentDate
    } = req.body;

    // Validate required fields
    if (!name || !provider || !amount || !startDate || !endDate) {
      return res.status(400).json({ error: 'Name, provider, amount, start date, and end date are required' });
    }

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Create the subsidy
    const subsidy = await Subsidy.create({
      farm_id: farmId,
      name,
      provider,
      amount,
      status,
      start_date: startDate,
      end_date: endDate,
      description,
      requirements,
      last_payment_date: lastPaymentDate,
      next_payment_date: nextPaymentDate
    });

    return res.status(201).json({
      subsidy: {
        id: subsidy.id,
        name: subsidy.name,
        provider: subsidy.provider,
        amount: subsidy.amount,
        status: subsidy.status,
        startDate: subsidy.start_date,
        endDate: subsidy.end_date,
        description: subsidy.description,
        requirements: subsidy.requirements,
        lastPaymentDate: subsidy.last_payment_date,
        nextPaymentDate: subsidy.next_payment_date,
        createdAt: subsidy.created_at,
        updatedAt: subsidy.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating subsidy:', error);
    return res.status(500).json({ error: 'Failed to create subsidy' });
  }
};

/**
 * Update a subsidy
 */
export const updateSubsidy = async (req, res) => {
  try {
    const { subsidyId } = req.params;
    const { 
      name, 
      provider, 
      amount, 
      status, 
      startDate, 
      endDate, 
      description, 
      requirements,
      lastPaymentDate,
      nextPaymentDate
    } = req.body;

    // Find the subsidy
    const subsidy = await Subsidy.findByPk(subsidyId);
    if (!subsidy) {
      return res.status(404).json({ error: 'Subsidy not found' });
    }

    // Update the subsidy
    if (name) subsidy.name = name;
    if (provider) subsidy.provider = provider;
    if (amount) subsidy.amount = amount;
    if (status) subsidy.status = status;
    if (startDate) subsidy.start_date = startDate;
    if (endDate) subsidy.end_date = endDate;
    if (description !== undefined) subsidy.description = description;
    if (requirements) subsidy.requirements = requirements;
    if (lastPaymentDate) subsidy.last_payment_date = lastPaymentDate;
    if (nextPaymentDate) subsidy.next_payment_date = nextPaymentDate;

    await subsidy.save();

    return res.status(200).json({
      subsidy: {
        id: subsidy.id,
        name: subsidy.name,
        provider: subsidy.provider,
        amount: subsidy.amount,
        status: subsidy.status,
        startDate: subsidy.start_date,
        endDate: subsidy.end_date,
        description: subsidy.description,
        requirements: subsidy.requirements,
        lastPaymentDate: subsidy.last_payment_date,
        nextPaymentDate: subsidy.next_payment_date,
        createdAt: subsidy.created_at,
        updatedAt: subsidy.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating subsidy:', error);
    return res.status(500).json({ error: 'Failed to update subsidy' });
  }
};

/**
 * Delete a subsidy
 */
export const deleteSubsidy = async (req, res) => {
  try {
    const { subsidyId } = req.params;

    // Find the subsidy
    const subsidy = await Subsidy.findByPk(subsidyId);
    if (!subsidy) {
      return res.status(404).json({ error: 'Subsidy not found' });
    }

    // Delete the subsidy
    await subsidy.destroy();

    return res.status(200).json({
      message: 'Subsidy deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting subsidy:', error);
    return res.status(500).json({ error: 'Failed to delete subsidy' });
  }
};
