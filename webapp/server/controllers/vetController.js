import Vet from '../models/Vet.js';
import Farm from '../models/Farm.js';
import { sequelize } from '../config/database.js';

// Get all vets (global and farm-specific)
export const getAllVets = async (req, res) => {
  try {
    const vets = await Vet.findAll({
      order: [['name', 'ASC']]
    });

    return res.status(200).json({ vets });
  } catch (error) {
    console.error('Error getting all vets:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all global vets (managed by admin)
export const getGlobalVets = async (req, res) => {
  try {
    const vets = await Vet.findAll({
      where: { is_global: true },
      order: [['name', 'ASC']]
    });

    return res.status(200).json({ vets });
  } catch (error) {
    console.error('Error getting global vets:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all vets for a specific farm (including global vets)
export const getFarmVets = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get farm-specific vets
    const farmVets = await Vet.findAll({
      where: { farm_id: farmId },
      order: [['name', 'ASC']]
    });

    // Get global vets
    const globalVets = await Vet.findAll({
      where: { is_global: true },
      order: [['name', 'ASC']]
    });

    // Combine and return both sets
    return res.status(200).json({ 
      farmVets,
      globalVets
    });
  } catch (error) {
    console.error('Error getting farm vets:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single vet by ID
export const getVetById = async (req, res) => {
  try {
    const { vetId } = req.params;

    const vet = await Vet.findByPk(vetId);

    if (!vet) {
      return res.status(404).json({ error: 'Vet not found' });
    }

    return res.status(200).json({ vet });
  } catch (error) {
    console.error('Error getting vet:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new vet
export const createVet = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      name, 
      specialization, 
      phone, 
      email, 
      address, 
      city, 
      state, 
      zipCode, 
      licenseNumber, 
      isGlobal, 
      farmId, 
      notes 
    } = req.body;

    // Validate required fields
    if (!name) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Vet name is required' });
    }

    // If it's a farm-specific vet, validate farm exists
    if (!isGlobal && farmId) {
      const farm = await Farm.findByPk(farmId);
      if (!farm) {
        await transaction.rollback();
        return res.status(404).json({ error: 'Farm not found' });
      }
    }

    // Create vet
    const vet = await Vet.create({
      name,
      specialization,
      phone,
      email,
      address,
      city,
      state,
      zip_code: zipCode,
      license_number: licenseNumber,
      is_global: isGlobal || false,
      farm_id: !isGlobal ? farmId : null,
      notes
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({ 
      message: 'Vet created successfully',
      vet 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating vet:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a vet
export const updateVet = async (req, res) => {
  try {
    const { vetId } = req.params;
    const { 
      name, 
      specialization, 
      phone, 
      email, 
      address, 
      city, 
      state, 
      zipCode, 
      licenseNumber, 
      isGlobal, 
      farmId, 
      notes 
    } = req.body;

    // Find vet to ensure it exists
    const vet = await Vet.findByPk(vetId);
    if (!vet) {
      return res.status(404).json({ error: 'Vet not found' });
    }

    // If changing to farm-specific, validate farm exists
    if (!isGlobal && farmId && farmId !== vet.farm_id) {
      const farm = await Farm.findByPk(farmId);
      if (!farm) {
        return res.status(404).json({ error: 'Farm not found' });
      }
    }

    // Update vet
    await vet.update({
      name: name || vet.name,
      specialization: specialization !== undefined ? specialization : vet.specialization,
      phone: phone !== undefined ? phone : vet.phone,
      email: email !== undefined ? email : vet.email,
      address: address !== undefined ? address : vet.address,
      city: city !== undefined ? city : vet.city,
      state: state !== undefined ? state : vet.state,
      zip_code: zipCode !== undefined ? zipCode : vet.zip_code,
      license_number: licenseNumber !== undefined ? licenseNumber : vet.license_number,
      is_global: isGlobal !== undefined ? isGlobal : vet.is_global,
      farm_id: isGlobal ? null : (farmId !== undefined ? farmId : vet.farm_id),
      notes: notes !== undefined ? notes : vet.notes
    });

    return res.status(200).json({ 
      message: 'Vet updated successfully',
      vet 
    });
  } catch (error) {
    console.error('Error updating vet:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a vet
export const deleteVet = async (req, res) => {
  try {
    const { vetId } = req.params;

    // Find vet to ensure it exists
    const vet = await Vet.findByPk(vetId);
    if (!vet) {
      return res.status(404).json({ error: 'Vet not found' });
    }

    // Delete vet
    await vet.destroy();

    return res.status(200).json({ 
      message: 'Vet deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting vet:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Search vets by name, specialization, or location
export const searchVets = async (req, res) => {
  try {
    const { query, farmId } = req.query;
    
    if (!query) {
      return res.status(400).json({ error: 'Search query is required' });
    }

    const searchCondition = {
      [sequelize.Op.or]: [
        { name: { [sequelize.Op.iLike]: `%${query}%` } },
        { specialization: { [sequelize.Op.iLike]: `%${query}%` } },
        { city: { [sequelize.Op.iLike]: `%${query}%` } },
        { state: { [sequelize.Op.iLike]: `%${query}%` } }
      ]
    };

    // If farmId is provided, search only global vets and farm-specific vets for that farm
    if (farmId) {
      searchCondition[sequelize.Op.and] = [
        {
          [sequelize.Op.or]: [
            { is_global: true },
            { farm_id: farmId }
          ]
        }
      ];
    }

    const vets = await Vet.findAll({
      where: searchCondition,
      order: [['name', 'ASC']]
    });

    return res.status(200).json({ vets });
  } catch (error) {
    console.error('Error searching vets:', error);
    return res.status(500).json({ error: error.message });
  }
};