import UserRecovery<PERSON>ey from '../models/UserRecoveryKey.js';
import * as passwordEncryption from '../utils/passwordEncryption.js';
import { sequelize } from '../config/database.js';

/**
 * Controller for managing user recovery keys
 */
class RecoveryKeyController {
  /**
   * Generate a new recovery key for a user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async generateRecoveryKey(req, res) {
    const transaction = await sequelize.transaction();
    
    try {
      const userId = req.user.id;
      const { masterPassword } = req.body;
      
      if (!masterPassword) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Master password is required to generate a recovery key'
        });
      }
      
      // Generate a new recovery key
      const recoveryKey = passwordEncryption.generateRecoveryKey();
      
      // Encrypt the recovery key with the server's master key
      const encryptedRecoveryKey = passwordEncryption.encryptRecoveryKey(recoveryKey);
      
      // Store or update the recovery key
      const [userR<PERSON>over<PERSON><PERSON><PERSON>, created] = await UserRecoveryKey.findOrCreate({
        where: { user_id: userId },
        defaults: {
          encrypted_recovery_key: encryptedRecoveryKey
        },
        transaction
      });
      
      if (!created) {
        // Update existing recovery key
        await userRecoveryKey.update({
          encrypted_recovery_key: encryptedRecoveryKey
        }, { transaction });
      }
      
      await transaction.commit();
      
      return res.status(200).json({
        success: true,
        data: {
          recoveryKey: recoveryKey
        },
        message: 'Recovery key generated successfully. Please save this key in a secure location.'
      });
    } catch (error) {
      await transaction.rollback();
      console.error('Error generating recovery key:', error);
      return res.status(500).json({
        success: false,
        message: 'An error occurred while generating the recovery key',
        error: error.message
      });
    }
  }
  
  /**
   * Check if a user has a recovery key
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async hasRecoveryKey(req, res) {
    try {
      const userId = req.user.id;
      
      // Check if user has a recovery key
      const userRecoveryKey = await UserRecoveryKey.findOne({
        where: { user_id: userId }
      });
      
      return res.status(200).json({
        success: true,
        data: {
          hasRecoveryKey: !!userRecoveryKey
        }
      });
    } catch (error) {
      console.error('Error checking recovery key:', error);
      return res.status(500).json({
        success: false,
        message: 'An error occurred while checking for recovery key',
        error: error.message
      });
    }
  }
  
  /**
   * Recover access to passwords using a recovery key
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async recoverAccess(req, res) {
    try {
      const userId = req.user.id;
      const { recoveryKey, newMasterPassword } = req.body;
      
      if (!recoveryKey || !newMasterPassword) {
        return res.status(400).json({
          success: false,
          message: 'Recovery key and new master password are required'
        });
      }
      
      // Find the user's recovery key
      const userRecoveryKey = await UserRecoveryKey.findOne({
        where: { user_id: userId }
      });
      
      if (!userRecoveryKey) {
        return res.status(404).json({
          success: false,
          message: 'No recovery key found for this user'
        });
      }
      
      // Decrypt the stored recovery key
      let decryptedRecoveryKey;
      try {
        decryptedRecoveryKey = passwordEncryption.decryptRecoveryKey(
          userRecoveryKey.encrypted_recovery_key
        );
      } catch (error) {
        console.error('Error decrypting recovery key:', error);
        return res.status(500).json({
          success: false,
          message: 'Error decrypting recovery key',
          error: error.message
        });
      }
      
      // Verify the provided recovery key
      if (recoveryKey !== decryptedRecoveryKey) {
        return res.status(400).json({
          success: false,
          message: 'Invalid recovery key'
        });
      }
      
      // At this point, the recovery key is valid
      // In a real implementation, we would re-encrypt all passwords with the new master password
      // For now, we'll just return success
      
      return res.status(200).json({
        success: true,
        message: 'Recovery successful. You can now use your new master password.'
      });
    } catch (error) {
      console.error('Error recovering access:', error);
      return res.status(500).json({
        success: false,
        message: 'An error occurred during recovery',
        error: error.message
      });
    }
  }
  
  /**
   * Delete a user's recovery key
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async deleteRecoveryKey(req, res) {
    try {
      const userId = req.user.id;
      const { masterPassword } = req.body;
      
      if (!masterPassword) {
        return res.status(400).json({
          success: false,
          message: 'Master password is required to delete the recovery key'
        });
      }
      
      // Find the user's recovery key
      const userRecoveryKey = await UserRecoveryKey.findOne({
        where: { user_id: userId }
      });
      
      if (!userRecoveryKey) {
        return res.status(404).json({
          success: false,
          message: 'No recovery key found for this user'
        });
      }
      
      // Delete the recovery key
      await userRecoveryKey.destroy();
      
      return res.status(200).json({
        success: true,
        message: 'Recovery key deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting recovery key:', error);
      return res.status(500).json({
        success: false,
        message: 'An error occurred while deleting the recovery key',
        error: error.message
      });
    }
  }
}

export default new RecoveryKeyController();