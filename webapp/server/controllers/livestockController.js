import Livestock from '../models/Livestock.js';
import Farm from '../models/Farm.js';
import { sequelize } from '../config/database.js';

// Get all livestock for a farm
export const getFarmLivestock = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all livestock for the farm
    const livestock = await Livestock.findAll({
      where: { farm_id: farmId },
      order: [['type', 'ASC'], ['breed', 'ASC']]
    });

    return res.status(200).json({ livestock });
  } catch (error) {
    console.error('Error getting farm livestock:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single livestock item by ID
export const getLivestockById = async (req, res) => {
  try {
    const { livestockId } = req.params;

    const livestock = await Livestock.findByPk(livestockId);

    if (!livestock) {
      return res.status(404).json({ error: 'Livestock not found' });
    }

    return res.status(200).json({ livestock });
  } catch (error) {
    console.error('Error getting livestock:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new livestock item
export const createLivestock = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      farmId, 
      type, 
      breed, 
      quantity, 
      acquisitionDate, 
      acquisitionCost, 
      status, 
      notes 
    } = req.body;

    // Validate required fields
    if (!farmId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    if (!type) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Livestock type is required' });
    }

    if (!quantity || quantity <= 0) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Valid quantity is required' });
    }

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Create livestock
    const livestock = await Livestock.create({
      farm_id: farmId,
      type,
      breed,
      quantity,
      acquisition_date: acquisitionDate,
      acquisition_cost: acquisitionCost,
      status,
      notes
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({ 
      message: 'Livestock created successfully',
      livestock 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating livestock:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a livestock item
export const updateLivestock = async (req, res) => {
  try {
    const { livestockId } = req.params;
    const { 
      type, 
      breed, 
      quantity, 
      acquisitionDate, 
      acquisitionCost, 
      status, 
      notes 
    } = req.body;

    // Find livestock to ensure it exists
    const livestock = await Livestock.findByPk(livestockId);
    if (!livestock) {
      return res.status(404).json({ error: 'Livestock not found' });
    }

    // Update livestock
    await livestock.update({
      type: type || livestock.type,
      breed: breed !== undefined ? breed : livestock.breed,
      quantity: quantity !== undefined ? quantity : livestock.quantity,
      acquisition_date: acquisitionDate !== undefined ? acquisitionDate : livestock.acquisition_date,
      acquisition_cost: acquisitionCost !== undefined ? acquisitionCost : livestock.acquisition_cost,
      status: status !== undefined ? status : livestock.status,
      notes: notes !== undefined ? notes : livestock.notes
    });

    return res.status(200).json({ 
      message: 'Livestock updated successfully',
      livestock 
    });
  } catch (error) {
    console.error('Error updating livestock:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a livestock item
export const deleteLivestock = async (req, res) => {
  try {
    const { livestockId } = req.params;

    // Find livestock to ensure it exists
    const livestock = await Livestock.findByPk(livestockId);
    if (!livestock) {
      return res.status(404).json({ error: 'Livestock not found' });
    }

    // Delete livestock
    await livestock.destroy();

    return res.status(200).json({ 
      message: 'Livestock deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting livestock:', error);
    return res.status(500).json({ error: error.message });
  }
};