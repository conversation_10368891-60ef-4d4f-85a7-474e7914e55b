import axios from 'axios';
import { sequelize } from '../config/database.js';
import SoilSample from '../models/SoilSample.js';
import Field from '../models/Field.js';
import Farm from '../models/Farm.js';
import Weather from '../models/Weather.js';

// USDA Soil Survey API base URL
const SOIL_DATA_API_BASE = 'https://casoilresource.lawr.ucdavis.edu/soil_web/api/v1';

// Data.gov Soil Data API URLs
const DATA_GOV_API_URL = process.env.DATA_GOV_API_URL || 'https://api.data.gov';
const DATA_GOV_API_KEY = process.env.DATA_GOV_API_KEY || '';
const USDA_NRCS_SOIL_API_URL = process.env.USDA_NRCS_SOIL_API_URL || 'https://sdmdataaccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx';
const SOIL_DATA_ACCESS_API_URL = 'https://sdmdataaccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx';

// National Weather Service API base URL
const NWS_API_BASE = 'https://api.weather.gov';

// OpenWeatherMap API base URL for rainfall data
const OPENWEATHER_API_BASE = 'https://api.openweathermap.org/data/2.5';
const OPENWEATHER_API_KEY = process.env.OPENWEATHER_API_KEY || ''; // Add this to .env file

// Visual Crossing Weather API base URL and key
const VISUALCROSSING_API_BASE = 'https://weather.visualcrossing.com/VisualCrossingWebServices/rest/services/timeline';
const VISUALCROSSING_API_KEY = process.env.VISUALCROSSING_API_KEY || ''; // Add this to .env file

/**
 * Get detailed soil information for a field
 */
export const getFieldSoilData = async (req, res) => {
  try {
    const { fieldId } = req.params;

    // Find field to ensure it exists and get its location
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Get field location data (center point)
    const fieldLocation = field.location_data;
    if (!fieldLocation || !fieldLocation.center || !fieldLocation.center.latitude || !fieldLocation.center.longitude) {
      return res.status(400).json({ error: 'Field location data is missing or invalid' });
    }

    // Fetch soil data from USDA Soil Survey API
    const soilData = await fetchSoilData(fieldLocation.center.latitude, fieldLocation.center.longitude);

    // Fetch rainfall data from OpenWeatherMap API
    const rainfallData = await fetchRainfallData(fieldLocation.center.latitude, fieldLocation.center.longitude);

    // Combine soil and rainfall data
    const combinedData = {
      ...soilData,
      rainfall: rainfallData
    };

    // Generate recommendations based on soil data
    const recommendations = generateSoilRecommendations(combinedData);

    // Return combined data with recommendations
    return res.status(200).json({
      soilData: combinedData,
      recommendations
    });
  } catch (error) {
    console.error('Error getting field soil data:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get detailed soil information for a farm
 */
export const getFarmSoilData = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists and get its location
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get farm location data
    const farmLocation = farm.location_data;
    if (!farmLocation || !farmLocation.latitude || !farmLocation.longitude) {
      return res.status(400).json({ error: 'Farm location data is missing or invalid' });
    }

    // Fetch soil data from USDA Soil Survey API
    const soilData = await fetchSoilData(farmLocation.latitude, farmLocation.longitude);

    // Fetch rainfall data from OpenWeatherMap API
    const rainfallData = await fetchRainfallData(farmLocation.latitude, farmLocation.longitude);

    // Combine soil and rainfall data
    const combinedData = {
      ...soilData,
      rainfall: rainfallData
    };

    // Generate recommendations based on soil data
    const recommendations = generateSoilRecommendations(combinedData);

    // Return combined data with recommendations
    return res.status(200).json({
      soilData: combinedData,
      recommendations
    });
  } catch (error) {
    console.error('Error getting farm soil data:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get soil data for a specific soil sample
 */
export const getSoilSampleData = async (req, res) => {
  try {
    const { sampleId } = req.params;

    // Find soil sample to ensure it exists
    const soilSample = await SoilSample.findByPk(sampleId);
    if (!soilSample) {
      return res.status(404).json({ error: 'Soil sample not found' });
    }

    // Get field to get location data
    let latitude, longitude;
    if (soilSample.field_id) {
      const field = await Field.findByPk(soilSample.field_id);
      if (field && field.location_data && field.location_data.center) {
        latitude = field.location_data.center.latitude;
        longitude = field.location_data.center.longitude;
      }
    }

    // If no field location, use farm location
    if (!latitude || !longitude) {
      const farm = await Farm.findByPk(soilSample.farm_id);
      if (farm && farm.location_data) {
        latitude = farm.location_data.latitude;
        longitude = farm.location_data.longitude;
      } else {
        return res.status(400).json({ error: 'Location data is missing or invalid' });
      }
    }

    // Fetch soil data from USDA Soil Survey API
    const soilData = await fetchSoilData(latitude, longitude);

    // Fetch rainfall data from OpenWeatherMap API
    const rainfallData = await fetchRainfallData(latitude, longitude);

    // Combine soil and rainfall data
    const combinedData = {
      ...soilData,
      rainfall: rainfallData
    };

    // Generate recommendations based on soil data
    const recommendations = generateSoilRecommendations(combinedData);

    // Return combined data with recommendations
    return res.status(200).json({
      soilData: combinedData,
      recommendations
    });
  } catch (error) {
    console.error('Error getting soil sample data:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Helper function to fetch soil data from USDA Soil Survey API and data.gov
 */
export const fetchSoilData = async (latitude, longitude) => {
  try {
    // Fetch soil data from SoilWeb API
    const soilWebUrl = `${SOIL_DATA_API_BASE}/soil-survey/point?lat=${latitude}&lon=${longitude}`;
    const soilWebResponse = await axios.get(soilWebUrl);

    // Try to fetch additional soil data from data.gov NRCS Soil Data Access API
    let dataGovSoilData = null;
    try {
      dataGovSoilData = await fetchDataGovSoilData(latitude, longitude);
    } catch (dataGovError) {
      console.warn('Error fetching soil data from data.gov:', dataGovError);
    }

    // Extract relevant soil data, prioritizing data.gov data when available
    const soilData = {
      soilType: dataGovSoilData?.soilType || soilWebResponse.data?.soils?.[0]?.taxonomic_class || 'Unknown',
      soilSeries: dataGovSoilData?.soilSeries || soilWebResponse.data?.soils?.[0]?.soil_series || 'Unknown',
      soilComponents: dataGovSoilData?.soilComponents || soilWebResponse.data?.soils || [],
      soilProperties: {
        ph: dataGovSoilData?.soilProperties?.ph !== undefined ? dataGovSoilData.soilProperties.ph : extractSoilProperty(soilWebResponse.data, 'ph'),
        organicMatter: dataGovSoilData?.soilProperties?.organicMatter !== undefined ? dataGovSoilData.soilProperties.organicMatter : extractSoilProperty(soilWebResponse.data, 'om'),
        cec: dataGovSoilData?.soilProperties?.cec !== undefined ? dataGovSoilData.soilProperties.cec : extractSoilProperty(soilWebResponse.data, 'cec'),
        clayContent: dataGovSoilData?.soilProperties?.clayContent !== undefined ? dataGovSoilData.soilProperties.clayContent : extractSoilProperty(soilWebResponse.data, 'clay'),
        sandContent: dataGovSoilData?.soilProperties?.sandContent !== undefined ? dataGovSoilData.soilProperties.sandContent : extractSoilProperty(soilWebResponse.data, 'sand'),
        siltContent: dataGovSoilData?.soilProperties?.siltContent !== undefined ? dataGovSoilData.soilProperties.siltContent : extractSoilProperty(soilWebResponse.data, 'silt'),
        drainageClass: dataGovSoilData?.soilProperties?.drainageClass || soilWebResponse.data?.soils?.[0]?.drainage_class || 'Unknown',
        erosionClass: dataGovSoilData?.soilProperties?.erosionClass || soilWebResponse.data?.soils?.[0]?.erosion_class || 'Unknown',
        floodFrequency: dataGovSoilData?.soilProperties?.floodFrequency || soilWebResponse.data?.soils?.[0]?.flood_frequency || 'Unknown',
        depthToWaterTable: dataGovSoilData?.soilProperties?.depthToWaterTable !== undefined ? dataGovSoilData.soilProperties.depthToWaterTable : extractSoilProperty(soilWebResponse.data, 'water_table_depth'),
        availableWaterCapacity: dataGovSoilData?.soilProperties?.availableWaterCapacity !== undefined ? dataGovSoilData.soilProperties.availableWaterCapacity : extractSoilProperty(soilWebResponse.data, 'awc')
      },
      soilHorizons: dataGovSoilData?.soilHorizons || soilWebResponse.data?.soils?.[0]?.horizons || [],
      soilLimitations: dataGovSoilData?.soilLimitations || extractSoilLimitations(soilWebResponse.data),
      soilSuitability: {
        cropProduction: dataGovSoilData?.soilSuitability?.cropProduction || assessSoilSuitability(soilWebResponse.data, 'crop'),
        grazing: dataGovSoilData?.soilSuitability?.grazing || assessSoilSuitability(soilWebResponse.data, 'grazing'),
        irrigation: dataGovSoilData?.soilSuitability?.irrigation || assessSoilSuitability(soilWebResponse.data, 'irrigation')
      }
    };

    return soilData;
  } catch (error) {
    console.error('Error fetching soil data:', error);

    // Try to fetch from data.gov as a fallback if SoilWeb API fails
    try {
      const dataGovSoilData = await fetchDataGovSoilData(latitude, longitude);
      if (dataGovSoilData) {
        return dataGovSoilData;
      }
    } catch (dataGovError) {
      console.warn('Fallback to data.gov also failed:', dataGovError);
    }

    // Return basic soil data as fallback if all APIs fail
    return {
      soilType: 'Unknown',
      soilSeries: 'Unknown',
      soilComponents: [],
      soilProperties: {
        ph: null,
        organicMatter: null,
        cec: null,
        clayContent: null,
        sandContent: null,
        siltContent: null,
        drainageClass: 'Unknown',
        erosionClass: 'Unknown',
        floodFrequency: 'Unknown',
        depthToWaterTable: null,
        availableWaterCapacity: null
      },
      soilHorizons: [],
      soilLimitations: [],
      soilSuitability: {
        cropProduction: 'Unknown',
        grazing: 'Unknown',
        irrigation: 'Unknown'
      }
    };
  }
};

/**
 * Helper function to fetch soil data from data.gov NRCS Soil Data Access API
 */
const fetchDataGovSoilData = async (latitude, longitude) => {
  try {
    // Check if API key is available
    if (!DATA_GOV_API_KEY) {
      console.warn('DATA_GOV_API_KEY is not set. Cannot fetch soil data from data.gov.');
      return null;
    }

    // First, get the soil survey area (SSA) for the given coordinates
    // Using the Soil Data Access API to get soil survey information
    const ssaXml = `<?xml version="1.0" encoding="utf-8"?>
      <soap12:Envelope xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
        <soap12:Body>
          <GetSoilSurveyAreaByLocation xmlns="http://SDMDataAccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx">
            <latitude>${latitude}</latitude>
            <longitude>${longitude}</longitude>
          </GetSoilSurveyAreaByLocation>
        </soap12:Body>
      </soap12:Envelope>`;

    const ssaResponse = await axios.post(SOIL_DATA_ACCESS_API_URL, ssaXml, {
      headers: {
        'Content-Type': 'application/soap+xml',
        'X-Api-Key': DATA_GOV_API_KEY
      }
    });

    // Parse the XML response to get the soil survey area ID
    const ssaXmlResponse = ssaResponse.data;
    // Extract areasymbol from XML response (simplified parsing for example)
    const areaSymbolMatch = ssaXmlResponse.match(/<areasymbol>(.*?)<\/areasymbol>/);
    const areaSymbol = areaSymbolMatch ? areaSymbolMatch[1] : null;

    if (!areaSymbol) {
      console.warn('No soil survey area found for the given coordinates.');
      return null;
    }

    // Now get the soil map unit for the given coordinates
    const muXml = `<?xml version="1.0" encoding="utf-8"?>
      <soap12:Envelope xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
        <soap12:Body>
          <GetMapUnitByLocation xmlns="http://SDMDataAccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx">
            <latitude>${latitude}</latitude>
            <longitude>${longitude}</longitude>
          </GetMapUnitByLocation>
        </soap12:Body>
      </soap12:Envelope>`;

    const muResponse = await axios.post(SOIL_DATA_ACCESS_API_URL, muXml, {
      headers: {
        'Content-Type': 'application/soap+xml',
        'X-Api-Key': DATA_GOV_API_KEY
      }
    });

    // Parse the XML response to get the map unit key
    const muXmlResponse = muResponse.data;
    // Extract mukey from XML response (simplified parsing for example)
    const mukeyMatch = muXmlResponse.match(/<mukey>(.*?)<\/mukey>/);
    const mukey = mukeyMatch ? mukeyMatch[1] : null;

    if (!mukey) {
      console.warn('No map unit found for the given coordinates.');
      return null;
    }

    // Now get the soil properties for the map unit
    const propertiesXml = `<?xml version="1.0" encoding="utf-8"?>
      <soap12:Envelope xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
        <soap12:Body>
          <RunQuery xmlns="http://SDMDataAccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx">
            <Query>
              SELECT 
                component.mukey, 
                component.cokey, 
                component.compname, 
                component.comppct_r, 
                component.majcompflag, 
                component.taxclname, 
                component.taxorder, 
                component.taxsuborder, 
                component.taxgrtgroup, 
                component.drainagecl, 
                chorizon.hzname, 
                chorizon.hzdept_r, 
                chorizon.hzdepb_r, 
                chorizon.ph1to1h2o_r, 
                chorizon.om_r, 
                chorizon.cec7_r, 
                chorizon.sandtotal_r, 
                chorizon.silttotal_r, 
                chorizon.claytotal_r, 
                chorizon.awc_r, 
                copmgrp.flodfreqcl, 
                copmgrp.floddurcl, 
                copmgrp.pondfreqcl, 
                copmgrp.ponddurcl, 
                copmgrp.droughty, 
                copmgrp.hydgrp
              FROM component 
              LEFT JOIN chorizon ON component.cokey = chorizon.cokey 
              LEFT JOIN copmgrp ON component.cokey = copmgrp.cokey 
              WHERE component.mukey = '${mukey}'
              ORDER BY component.comppct_r DESC, chorizon.hzdept_r ASC
            </Query>
          </RunQuery>
        </soap12:Body>
      </soap12:Envelope>`;

    const propertiesResponse = await axios.post(SOIL_DATA_ACCESS_API_URL, propertiesXml, {
      headers: {
        'Content-Type': 'application/soap+xml',
        'X-Api-Key': DATA_GOV_API_KEY
      }
    });

    // Parse the XML response to extract soil properties
    const propertiesXmlResponse = propertiesResponse.data;

    // Extract soil properties from XML (simplified parsing for example)
    // In a real implementation, you would use a proper XML parser
    const rows = propertiesXmlResponse.match(/<Table>(.*?)<\/Table>/gs);

    if (!rows || rows.length === 0) {
      console.warn('No soil properties found for the given map unit.');
      return null;
    }

    // Process the first component (major component)
    const firstRow = rows[0];

    // Extract values from the XML (simplified)
    const compnameMatch = firstRow.match(/<compname>(.*?)<\/compname>/);
    const compname = compnameMatch ? compnameMatch[1] : 'Unknown';

    const taxclnameMatch = firstRow.match(/<taxclname>(.*?)<\/taxclname>/);
    const taxclname = taxclnameMatch ? taxclnameMatch[1] : 'Unknown';

    const drainageclMatch = firstRow.match(/<drainagecl>(.*?)<\/drainagecl>/);
    const drainagecl = drainageclMatch ? drainageclMatch[1] : 'Unknown';

    const phMatch = firstRow.match(/<ph1to1h2o_r>(.*?)<\/ph1to1h2o_r>/);
    const ph = phMatch ? parseFloat(phMatch[1]) : null;

    const omMatch = firstRow.match(/<om_r>(.*?)<\/om_r>/);
    const om = omMatch ? parseFloat(omMatch[1]) : null;

    const cecMatch = firstRow.match(/<cec7_r>(.*?)<\/cec7_r>/);
    const cec = cecMatch ? parseFloat(cecMatch[1]) : null;

    const sandMatch = firstRow.match(/<sandtotal_r>(.*?)<\/sandtotal_r>/);
    const sand = sandMatch ? parseFloat(sandMatch[1]) : null;

    const siltMatch = firstRow.match(/<silttotal_r>(.*?)<\/silttotal_r>/);
    const silt = siltMatch ? parseFloat(siltMatch[1]) : null;

    const clayMatch = firstRow.match(/<claytotal_r>(.*?)<\/claytotal_r>/);
    const clay = clayMatch ? parseFloat(clayMatch[1]) : null;

    const awcMatch = firstRow.match(/<awc_r>(.*?)<\/awc_r>/);
    const awc = awcMatch ? parseFloat(awcMatch[1]) : null;

    const flodfreqclMatch = firstRow.match(/<flodfreqcl>(.*?)<\/flodfreqcl>/);
    const flodfreqcl = flodfreqclMatch ? flodfreqclMatch[1] : 'Unknown';

    // Extract horizons from all rows
    const horizons = rows.map(row => {
      const hznameMatch = row.match(/<hzname>(.*?)<\/hzname>/);
      const hzname = hznameMatch ? hznameMatch[1] : 'Unknown';

      const hzdeptMatch = row.match(/<hzdept_r>(.*?)<\/hzdept_r>/);
      const hzdept = hzdeptMatch ? parseFloat(hzdeptMatch[1]) : null;

      const hzdepbMatch = row.match(/<hzdepb_r>(.*?)<\/hzdepb_r>/);
      const hzdepb = hzdepbMatch ? parseFloat(hzdepbMatch[1]) : null;

      const phHorizonMatch = row.match(/<ph1to1h2o_r>(.*?)<\/ph1to1h2o_r>/);
      const phHorizon = phHorizonMatch ? parseFloat(phHorizonMatch[1]) : null;

      const omHorizonMatch = row.match(/<om_r>(.*?)<\/om_r>/);
      const omHorizon = omHorizonMatch ? parseFloat(omHorizonMatch[1]) : null;

      return {
        name: hzname,
        depth_upper: hzdept,
        depth_lower: hzdepb,
        texture: 'Unknown', // Would need additional queries to get texture
        ph: phHorizon,
        organic_matter: omHorizon
      };
    }).filter(h => h.name && h.depth_upper !== null && h.depth_lower !== null);

    // Determine soil limitations
    const limitations = [];

    if (drainagecl === 'poorly drained' || drainagecl === 'very poorly drained') {
      limitations.push('Poor drainage');
    }

    if (flodfreqcl && flodfreqcl !== 'none') {
      limitations.push('Flood risk');
    }

    if (ph !== null) {
      if (ph < 5.5) {
        limitations.push('Acidic soil (pH < 5.5)');
      } else if (ph > 8.0) {
        limitations.push('Alkaline soil (pH > 8.0)');
      }
    }

    if (om !== null && om < 2) {
      limitations.push('Low organic matter');
    }

    // Assess soil suitability
    let cropSuitability = 'Unknown';
    let grazingSuitability = 'Unknown';
    let irrigationSuitability = 'Unknown';

    if (drainagecl && ph !== null) {
      // Crop suitability
      if (drainagecl === 'well drained' && ph >= 5.5 && ph <= 7.5) {
        cropSuitability = 'High';
      } else if (drainagecl === 'moderately well drained' && ph >= 5.0 && ph <= 8.0) {
        cropSuitability = 'Moderate';
      } else {
        cropSuitability = 'Low';
      }

      // Grazing suitability
      if (drainagecl !== 'very poorly drained' && om !== null && om >= 2) {
        grazingSuitability = 'High';
      } else if (drainagecl !== 'very poorly drained') {
        grazingSuitability = 'Moderate';
      } else {
        grazingSuitability = 'Low';
      }

      // Irrigation suitability
      if (drainagecl === 'well drained' || drainagecl === 'moderately well drained') {
        irrigationSuitability = 'High';
      } else if (drainagecl === 'somewhat poorly drained') {
        irrigationSuitability = 'Moderate';
      } else {
        irrigationSuitability = 'Low';
      }
    }

    // Construct the soil data object
    return {
      soilType: taxclname,
      soilSeries: compname,
      soilComponents: [], // Would need additional processing to extract components
      soilProperties: {
        ph: ph,
        organicMatter: om,
        cec: cec,
        clayContent: clay,
        sandContent: sand,
        siltContent: silt,
        drainageClass: drainagecl,
        erosionClass: 'Unknown', // Not directly available in this query
        floodFrequency: flodfreqcl,
        depthToWaterTable: null, // Would need additional queries
        availableWaterCapacity: awc
      },
      soilHorizons: horizons,
      soilLimitations: limitations,
      soilSuitability: {
        cropProduction: cropSuitability,
        grazing: grazingSuitability,
        irrigation: irrigationSuitability
      }
    };
  } catch (error) {
    console.error('Error fetching soil data from data.gov:', error);
    return null;
  }
};

/**
 * Helper function to fetch rainfall data from Weather model and NWS API
 */
const fetchRainfallData = async (latitude, longitude) => {
  try {
    // Import required modules
    const { Op } = sequelize;

    // Get current date and 5 days ago
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 5);

    // First, try to get historical rainfall data from our Weather model
    const historicalWeatherData = await Weather.findAll({
      where: {
        latitude: {
          [Op.between]: [parseFloat(latitude) - 0.1, parseFloat(latitude) + 0.1]
        },
        longitude: {
          [Op.between]: [parseFloat(longitude) - 0.1, parseFloat(longitude) + 0.1]
        },
        timestamp: {
          [Op.between]: [startDate, endDate]
        },
        precipitation: {
          [Op.not]: null
        }
      },
      order: [['timestamp', 'ASC']]
    });

    // If we have historical data in our database, use it
    if (historicalWeatherData && historicalWeatherData.length > 0) {
      console.log(`Found ${historicalWeatherData.length} historical weather records in database`);

      // Process historical data
      const historicalRainfall = historicalWeatherData.map(record => ({
        time: record.timestamp.toISOString(),
        amount: parseFloat(record.precipitation)
      }));

      // Calculate total rainfall
      const totalRainfall = historicalRainfall.reduce((sum, hour) => sum + hour.amount, 0);

      // Get forecast data from NWS API
      let forecastData = [];
      try {
        // Step 1: Get the grid point for the location
        const pointsUrl = `${NWS_API_BASE}/points/${latitude},${longitude}`;
        const pointsResponse = await axios.get(pointsUrl, {
          headers: {
            'User-Agent': '(NxtAcre Farm Management, <EMAIL>)',
            'Accept': 'application/geo+json'
          }
        });

        const { gridId, gridX, gridY } = pointsResponse.data.properties;

        // Step 2: Get the forecast for the grid point
        const forecastUrl = `${NWS_API_BASE}/gridpoints/${gridId}/${gridX},${gridY}/forecast`;
        const forecastResponse = await axios.get(forecastUrl, {
          headers: {
            'User-Agent': '(NxtAcre Farm Management, <EMAIL>)',
            'Accept': 'application/geo+json'
          }
        });

        // Process forecast data
        const dailyPeriods = forecastResponse.data.properties.periods;
        const dailyForecasts = {};

        // NWS API returns separate periods for day and night
        // We need to combine them to get precipitation data for each day
        dailyPeriods.forEach((period) => {
          const date = new Date(period.startTime);
          const day = date.toISOString().split('T')[0];

          if (!dailyForecasts[day]) {
            dailyForecasts[day] = {
              date: day,
              precipitation: 0,
              probability: period.probabilityOfPrecipitation?.value || 0
            };
          } else {
            // Update probability if it's higher
            if ((period.probabilityOfPrecipitation?.value || 0) > dailyForecasts[day].probability) {
              dailyForecasts[day].probability = period.probabilityOfPrecipitation?.value || 0;
            }
          }
        });

        // Convert to array and limit to 7 days
        forecastData = Object.values(dailyForecasts)
          .sort((a, b) => a.date.localeCompare(b.date))
          .slice(0, 7);
      } catch (forecastError) {
        console.error('Error fetching forecast data from NWS API:', forecastError);
        // Use empty forecast data if API call fails
        forecastData = Array.from({ length: 7 }, (_, i) => {
          const date = new Date();
          date.setDate(date.getDate() + i);
          return {
            date: date.toISOString().split('T')[0],
            precipitation: 0,
            probability: 0
          };
        });
      }

      return {
        historical: historicalRainfall,
        forecast: forecastData,
        total: totalRainfall,
        average: totalRainfall / 5 // Average per day
      };
    }

    // If we don't have historical data in our database, try to fetch from NWS API
    console.log('No historical weather data found in database, fetching from NWS API');

    try {
      // Step 1: Get the grid point for the location
      const pointsUrl = `${NWS_API_BASE}/points/${latitude},${longitude}`;
      const pointsResponse = await axios.get(pointsUrl, {
        headers: {
          'User-Agent': '(NxtAcre Farm Management, <EMAIL>)',
          'Accept': 'application/geo+json'
        }
      });

      const { gridId, gridX, gridY } = pointsResponse.data.properties;

      // Step 2: Get the hourly forecast for historical data
      const hourlyForecastUrl = `${NWS_API_BASE}/gridpoints/${gridId}/${gridX},${gridY}/forecast/hourly`;
      const hourlyForecastResponse = await axios.get(hourlyForecastUrl, {
        headers: {
          'User-Agent': '(NxtAcre Farm Management, <EMAIL>)',
          'Accept': 'application/geo+json'
        }
      });

      // Process hourly forecast data for historical rainfall
      const hourlyPeriods = hourlyForecastResponse.data.properties.periods;
      const historicalRainfall = hourlyPeriods
        .filter(period => period.probabilityOfPrecipitation?.value > 0)
        .map(period => ({
          time: period.startTime,
          // Estimate rainfall amount based on probability
          amount: (period.probabilityOfPrecipitation?.value || 0) / 100 * 0.1 // Rough estimate
        }));

      // Step 3: Get the forecast for the grid point
      const forecastUrl = `${NWS_API_BASE}/gridpoints/${gridId}/${gridX},${gridY}/forecast`;
      const forecastResponse = await axios.get(forecastUrl, {
        headers: {
          'User-Agent': '(NxtAcre Farm Management, <EMAIL>)',
          'Accept': 'application/geo+json'
        }
      });

      // Process forecast data
      const dailyPeriods = forecastResponse.data.properties.periods;
      const dailyForecasts = {};

      // NWS API returns separate periods for day and night
      // We need to combine them to get precipitation data for each day
      dailyPeriods.forEach((period) => {
        const date = new Date(period.startTime);
        const day = date.toISOString().split('T')[0];

        if (!dailyForecasts[day]) {
          dailyForecasts[day] = {
            date: day,
            precipitation: 0,
            probability: period.probabilityOfPrecipitation?.value || 0
          };
        } else {
          // Update probability if it's higher
          if ((period.probabilityOfPrecipitation?.value || 0) > dailyForecasts[day].probability) {
            dailyForecasts[day].probability = period.probabilityOfPrecipitation?.value || 0;
          }
        }
      });

      // Convert to array and limit to 7 days
      const forecastData = Object.values(dailyForecasts)
        .sort((a, b) => a.date.localeCompare(b.date))
        .slice(0, 7);

      // Calculate total rainfall
      const totalRainfall = historicalRainfall.reduce((sum, hour) => sum + hour.amount, 0);

      // Store the successful API response in the database for future use
      try {
        // Store each hour of rainfall data
        for (const rainfall of historicalRainfall) {
          await Weather.create({
            latitude,
            longitude,
            timestamp: new Date(rainfall.time),
            temperature: null, // We don't have temperature data here
            precipitation: rainfall.amount,
            humidity: null,
            wind_speed: null,
            wind_direction: null,
            conditions: 'Unknown', // We don't have conditions data here
            source: 'NWS API'
          });
        }
        console.log(`Stored ${historicalRainfall.length} weather records from NWS API`);
      } catch (dbError) {
        console.error('Error storing weather data in database:', dbError);
      }

      return {
        historical: historicalRainfall,
        forecast: forecastData,
        total: totalRainfall,
        average: totalRainfall / 5 // Average per day
      };
    } catch (nwsError) {
      console.error('Error fetching data from NWS API:', nwsError);
      // If NWS API fails, try OpenWeatherMap API if key is available
      if (OPENWEATHER_API_KEY) {
        console.log('Trying OpenWeatherMap API as fallback');
        try {
          // Fetch historical rainfall data (last 5 days)
          const endTime = Math.floor(Date.now() / 1000);
          const startTime = endTime - (5 * 24 * 60 * 60); // 5 days ago

          const historicalUrl = `${OPENWEATHER_API_BASE}/onecall/timemachine?lat=${latitude}&lon=${longitude}&dt=${startTime}&appid=${OPENWEATHER_API_KEY}&units=imperial`;
          const forecastUrl = `${OPENWEATHER_API_BASE}/onecall?lat=${latitude}&lon=${longitude}&exclude=minutely,alerts&appid=${OPENWEATHER_API_KEY}&units=imperial`;

          const [historicalResponse, forecastResponse] = await Promise.all([
            axios.get(historicalUrl),
            axios.get(forecastUrl)
          ]);

          // Process historical data
          const historicalRainfall = historicalResponse.data.hourly
            .filter(hour => hour.rain && hour.rain['1h'])
            .map(hour => ({
              time: new Date(hour.dt * 1000).toISOString(),
              amount: hour.rain['1h']
            }));

          // Process forecast data
          const dailyForecast = forecastResponse.data.daily.map(day => ({
            date: new Date(day.dt * 1000).toISOString().split('T')[0],
            precipitation: day.rain || 0,
            probability: day.pop * 100 // Convert to percentage
          }));

          // Calculate total rainfall for the past 5 days
          const totalRainfall = historicalRainfall.reduce((sum, hour) => sum + hour.amount, 0);

          // Store the successful API response in the database for future use
          try {
            // Store each hour of rainfall data
            for (const rainfall of historicalRainfall) {
              await Weather.create({
                latitude,
                longitude,
                timestamp: new Date(rainfall.time),
                temperature: null, // We don't have temperature data here
                precipitation: rainfall.amount,
                humidity: null,
                wind_speed: null,
                wind_direction: null,
                conditions: 'Unknown', // We don't have conditions data here
                source: 'OpenWeatherMap API'
              });
            }
            console.log(`Stored ${historicalRainfall.length} weather records from OpenWeatherMap API`);
          } catch (dbError) {
            console.error('Error storing weather data in database:', dbError);
          }

          return {
            historical: historicalRainfall,
            forecast: dailyForecast,
            total: totalRainfall,
            average: totalRainfall / 5 // Average per day
          };
        } catch (owmError) {
          console.error('Error fetching data from OpenWeatherMap API:', owmError);
          return getMockRainfallData();
        }
      } else {
        // Try Visual Crossing Weather API as a third fallback
        if (VISUALCROSSING_API_KEY) {
          console.log('Trying Visual Crossing Weather API as fallback');
          try {
            // Format dates for the API request
            const endDate = new Date().toISOString().split('T')[0];
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 5);
            const startDateStr = startDate.toISOString().split('T')[0];

            // Fetch historical and forecast data in one request
            const url = `${VISUALCROSSING_API_BASE}/${latitude},${longitude}/${startDateStr}/${endDate}?key=${VISUALCROSSING_API_KEY}&include=hours,days&elements=datetime,temp,precip,precipprob`;

            const response = await axios.get(url);

            if (response.data && response.data.days) {
              // Process historical data (past days)
              const historicalRainfall = [];

              // Process each day's hourly data
              response.data.days.forEach(day => {
                if (day.hours) {
                  day.hours.forEach(hour => {
                    if (hour.precip > 0) {
                      historicalRainfall.push({
                        time: new Date(`${day.datetime}T${hour.datetime}`).toISOString(),
                        amount: hour.precip
                      });
                    }
                  });
                }
              });

              // Process forecast data (next 7 days from the API)
              const forecastData = response.data.days.map(day => ({
                date: day.datetime,
                precipitation: day.precip || 0,
                probability: day.precipprob || 0
              }));

              // Calculate total rainfall
              const totalRainfall = historicalRainfall.reduce((sum, hour) => sum + hour.amount, 0);

              // Store the successful API response in the database for future use
              try {
                // Store each hour of rainfall data
                for (const rainfall of historicalRainfall) {
                  await Weather.create({
                    latitude,
                    longitude,
                    timestamp: new Date(rainfall.time),
                    temperature: null, // We don't have temperature data here
                    precipitation: rainfall.amount,
                    humidity: null,
                    wind_speed: null,
                    wind_direction: null,
                    conditions: 'Unknown', // We don't have conditions data here
                    source: 'Visual Crossing API'
                  });
                }
                console.log(`Stored ${historicalRainfall.length} weather records from Visual Crossing API`);
              } catch (dbError) {
                console.error('Error storing weather data in database:', dbError);
              }

              return {
                historical: historicalRainfall,
                forecast: forecastData,
                total: totalRainfall,
                average: totalRainfall / 5 // Average per day
              };
            }
          } catch (vcError) {
            console.error('Error fetching data from Visual Crossing Weather API:', vcError);
            return getMockRainfallData();
          }
        } else {
          return getMockRainfallData();
        }
      }
    }
  } catch (error) {
    console.error('Error fetching rainfall data:', error);
    return getMockRainfallData();
  }
};

/**
 * Generate mock rainfall data when API is unavailable
 */
const getMockRainfallData = () => {
  const now = new Date();
  const historical = [];
  const forecast = [];

  // Generate mock historical data (past 5 days)
  for (let i = 5; i >= 1; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);

    // Random rainfall between 0 and 0.5 inches
    const rainfall = Math.random() * 0.5;

    historical.push({
      time: date.toISOString(),
      amount: rainfall
    });
  }

  // Generate mock forecast data (next 7 days)
  for (let i = 0; i < 7; i++) {
    const date = new Date(now);
    date.setDate(date.getDate() + i);

    // Random rainfall between 0 and 0.7 inches
    const rainfall = Math.random() * 0.7;

    forecast.push({
      date: date.toISOString().split('T')[0],
      precipitation: rainfall,
      probability: Math.random() * 100
    });
  }

  // Calculate total rainfall
  const totalRainfall = historical.reduce((sum, day) => sum + day.amount, 0);

  return {
    historical,
    forecast,
    total: totalRainfall,
    average: totalRainfall / 5
  };
};

/**
 * Helper function to extract soil property from API response
 */
const extractSoilProperty = (data, property) => {
  if (!data || !data.soils || !data.soils[0] || !data.soils[0].properties) {
    return null;
  }

  const properties = data.soils[0].properties;
  return properties[property] !== undefined ? properties[property] : null;
};

/**
 * Helper function to extract soil limitations from API response
 */
const extractSoilLimitations = (data) => {
  if (!data || !data.soils || !data.soils[0]) {
    return [];
  }

  const limitations = [];
  const soil = data.soils[0];

  // Check for common limitations
  if (soil.drainage_class === 'poorly drained' || soil.drainage_class === 'very poorly drained') {
    limitations.push('Poor drainage');
  }

  if (soil.flood_frequency && soil.flood_frequency !== 'none') {
    limitations.push('Flood risk');
  }

  if (soil.erosion_class && soil.erosion_class !== 'none') {
    limitations.push('Erosion risk');
  }

  // Check pH limitations
  const ph = extractSoilProperty(data, 'ph');
  if (ph !== null) {
    if (ph < 5.5) {
      limitations.push('Acidic soil (pH < 5.5)');
    } else if (ph > 8.0) {
      limitations.push('Alkaline soil (pH > 8.0)');
    }
  }

  // Check organic matter limitations
  const om = extractSoilProperty(data, 'om');
  if (om !== null && om < 2) {
    limitations.push('Low organic matter');
  }

  return limitations;
};

/**
 * Helper function to assess soil suitability for different uses
 */
const assessSoilSuitability = (data, use) => {
  if (!data || !data.soils || !data.soils[0]) {
    return 'Unknown';
  }

  const soil = data.soils[0];
  const properties = soil.properties || {};

  // Assess suitability based on soil properties
  switch (use) {
    case 'crop':
      // Assess crop production suitability
      if (soil.drainage_class === 'well drained' && properties.ph >= 5.5 && properties.ph <= 7.5) {
        return 'High';
      } else if (soil.drainage_class === 'moderately well drained' && properties.ph >= 5.0 && properties.ph <= 8.0) {
        return 'Moderate';
      } else {
        return 'Low';
      }

    case 'grazing':
      // Assess grazing suitability
      if (soil.drainage_class !== 'very poorly drained' && properties.om >= 2) {
        return 'High';
      } else if (soil.drainage_class !== 'very poorly drained') {
        return 'Moderate';
      } else {
        return 'Low';
      }

    case 'irrigation':
      // Assess irrigation suitability
      if (soil.drainage_class === 'well drained' || soil.drainage_class === 'moderately well drained') {
        return 'High';
      } else if (soil.drainage_class === 'somewhat poorly drained') {
        return 'Moderate';
      } else {
        return 'Low';
      }

    default:
      return 'Unknown';
  }
};

/**
 * Generate soil recommendations based on soil data
 */
export const generateSoilRecommendations = (soilData) => {
  const recommendations = [];

  // Check soil pH
  if (soilData.soilProperties.ph !== null) {
    if (soilData.soilProperties.ph < 5.5) {
      recommendations.push({
        type: 'pH',
        issue: 'Soil is acidic',
        recommendation: 'Consider applying lime to raise pH to 6.0-7.0 for most crops',
        priority: 'High'
      });
    } else if (soilData.soilProperties.ph > 7.5) {
      recommendations.push({
        type: 'pH',
        issue: 'Soil is alkaline',
        recommendation: 'Consider applying sulfur or gypsum to lower pH for acid-loving crops',
        priority: 'Medium'
      });
    }
  }

  // Check organic matter
  if (soilData.soilProperties.organicMatter !== null && soilData.soilProperties.organicMatter < 2) {
    recommendations.push({
      type: 'Organic Matter',
      issue: 'Low organic matter content',
      recommendation: 'Add compost, manure, or implement cover crops to increase organic matter',
      priority: 'High'
    });
  }

  // Check drainage
  if (soilData.soilProperties.drainageClass === 'poorly drained' || soilData.soilProperties.drainageClass === 'very poorly drained') {
    recommendations.push({
      type: 'Drainage',
      issue: 'Poor soil drainage',
      recommendation: 'Consider installing drainage tiles or creating raised beds for better drainage',
      priority: 'High'
    });
  }

  // Check erosion risk
  if (soilData.soilProperties.erosionClass && soilData.soilProperties.erosionClass !== 'none') {
    recommendations.push({
      type: 'Erosion',
      issue: 'Soil erosion risk',
      recommendation: 'Implement contour farming, strip cropping, or cover crops to reduce erosion',
      priority: 'High'
    });
  }

  // Check rainfall
  if (soilData.rainfall && soilData.rainfall.average !== undefined) {
    if (soilData.rainfall.average < 0.1) {
      recommendations.push({
        type: 'Water',
        issue: 'Low rainfall',
        recommendation: 'Consider irrigation or drought-resistant crops',
        priority: 'Medium'
      });
    } else if (soilData.rainfall.average > 0.5) {
      recommendations.push({
        type: 'Water',
        issue: 'High rainfall',
        recommendation: 'Ensure proper drainage and consider crops that tolerate wet conditions',
        priority: 'Medium'
      });
    }
  }

  // Add general recommendations based on soil type
  if (soilData.soilType && soilData.soilType !== 'Unknown') {
    const soilTypeRecommendation = getSoilTypeRecommendation(soilData.soilType);
    if (soilTypeRecommendation) {
      recommendations.push(soilTypeRecommendation);
    }
  }

  return recommendations;
};

/**
 * Get recommendations based on soil type
 */
const getSoilTypeRecommendation = (soilType) => {
  const soilTypeLower = soilType.toLowerCase();

  if (soilTypeLower.includes('clay')) {
    return {
      type: 'Soil Type',
      issue: 'Clay soil',
      recommendation: 'Add organic matter to improve structure. Avoid working when wet. Consider deep-rooted crops to break up clay.',
      priority: 'Medium'
    };
  } else if (soilTypeLower.includes('sand')) {
    return {
      type: 'Soil Type',
      issue: 'Sandy soil',
      recommendation: 'Add organic matter to improve water retention. Consider more frequent irrigation with smaller amounts of water.',
      priority: 'Medium'
    };
  } else if (soilTypeLower.includes('silt')) {
    return {
      type: 'Soil Type',
      issue: 'Silty soil',
      recommendation: 'Avoid compaction by limiting traffic when wet. Add organic matter to improve structure.',
      priority: 'Medium'
    };
  } else if (soilTypeLower.includes('loam')) {
    return {
      type: 'Soil Type',
      issue: 'Loam soil',
      recommendation: 'Maintain organic matter levels to preserve good soil structure. Suitable for most crops.',
      priority: 'Low'
    };
  }

  return null;
};
