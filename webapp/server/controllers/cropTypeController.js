import CropType from '../models/CropType.js';
import Farm from '../models/Farm.js';
import { sequelize } from '../config/database.js';

// Get all crop types for a farm
export const getFarmCropTypes = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all crop types for the farm
    const cropTypes = await CropType.findAll({
      where: { farm_id: farmId },
      order: [['name', 'ASC']]
    });

    return res.status(200).json({ cropTypes });
  } catch (error) {
    console.error('Error getting farm crop types:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single crop type by ID
export const getCropTypeById = async (req, res) => {
  try {
    const { cropTypeId } = req.params;

    const cropType = await CropType.findByPk(cropTypeId);

    if (!cropType) {
      return res.status(404).json({ error: 'Crop type not found' });
    }

    return res.status(200).json({ cropType });
  } catch (error) {
    console.error('Error getting crop type:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new crop type
export const createCropType = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      farmId, 
      name, 
      description, 
      growingSeason, 
      daysToMaturity, 
      plantingDepth,
      rowSpacing,
      plantSpacing,
      idealSoilPh,
      idealTemperature
    } = req.body;

    // Validate required fields
    if (!farmId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    if (!name) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Crop type name is required' });
    }

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Create crop type
    const cropType = await CropType.create({
      farm_id: farmId,
      name,
      description,
      growing_season: growingSeason,
      days_to_maturity: daysToMaturity,
      planting_depth: plantingDepth,
      row_spacing: rowSpacing,
      plant_spacing: plantSpacing,
      ideal_soil_ph: idealSoilPh,
      ideal_temperature: idealTemperature
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({ 
      message: 'Crop type created successfully',
      cropType 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating crop type:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a crop type
export const updateCropType = async (req, res) => {
  try {
    const { cropTypeId } = req.params;
    const { 
      name, 
      description, 
      growingSeason, 
      daysToMaturity, 
      plantingDepth,
      rowSpacing,
      plantSpacing,
      idealSoilPh,
      idealTemperature
    } = req.body;

    // Find crop type to ensure it exists
    const cropType = await CropType.findByPk(cropTypeId);
    if (!cropType) {
      return res.status(404).json({ error: 'Crop type not found' });
    }

    // Update crop type
    await cropType.update({
      name: name || cropType.name,
      description: description !== undefined ? description : cropType.description,
      growing_season: growingSeason !== undefined ? growingSeason : cropType.growing_season,
      days_to_maturity: daysToMaturity !== undefined ? daysToMaturity : cropType.days_to_maturity,
      planting_depth: plantingDepth !== undefined ? plantingDepth : cropType.planting_depth,
      row_spacing: rowSpacing !== undefined ? rowSpacing : cropType.row_spacing,
      plant_spacing: plantSpacing !== undefined ? plantSpacing : cropType.plant_spacing,
      ideal_soil_ph: idealSoilPh !== undefined ? idealSoilPh : cropType.ideal_soil_ph,
      ideal_temperature: idealTemperature !== undefined ? idealTemperature : cropType.ideal_temperature
    });

    return res.status(200).json({ 
      message: 'Crop type updated successfully',
      cropType 
    });
  } catch (error) {
    console.error('Error updating crop type:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a crop type
export const deleteCropType = async (req, res) => {
  try {
    const { cropTypeId } = req.params;

    // Find crop type to ensure it exists
    const cropType = await CropType.findByPk(cropTypeId);
    if (!cropType) {
      return res.status(404).json({ error: 'Crop type not found' });
    }

    // Delete crop type
    await cropType.destroy();

    return res.status(200).json({ 
      message: 'Crop type deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting crop type:', error);
    return res.status(500).json({ error: error.message });
  }
};