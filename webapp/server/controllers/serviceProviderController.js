import ServiceProvider from '../models/ServiceProvider.js';
import { sequelize } from '../config/database.js';

// Get all service providers for a farm
export const getServiceProviders = async (req, res) => {
  try {
    const { farmId, serviceType } = req.query;

    if (!farmId) {
      return res.status(400).json({ message: 'Farm ID is required' });
    }

    const whereClause = { farm_id: farmId };
    
    // Filter by service type if provided
    if (serviceType) {
      whereClause.service_type = serviceType;
    }

    const serviceProviders = await ServiceProvider.findAll({
      where: whereClause,
      order: [['name', 'ASC']]
    });

    res.status(200).json(serviceProviders);
  } catch (error) {
    console.error('Error fetching service providers:', error);
    res.status(500).json({ message: 'Failed to fetch service providers', error: error.message });
  }
};

// Get a single service provider by ID
export const getServiceProvider = async (req, res) => {
  try {
    const { id } = req.params;

    const serviceProvider = await ServiceProvider.findByPk(id);

    if (!serviceProvider) {
      return res.status(404).json({ message: 'Service provider not found' });
    }

    res.status(200).json(serviceProvider);
  } catch (error) {
    console.error('Error fetching service provider:', error);
    res.status(500).json({ message: 'Failed to fetch service provider', error: error.message });
  }
};

// Create a new service provider
export const createServiceProvider = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const {
      farm_id,
      name,
      service_type,
      contact_name,
      email,
      phone,
      address,
      website,
      description,
      service_area,
      rates,
      availability,
      certifications,
      notes,
      is_verified,
      is_active
    } = req.body;

    if (!farm_id || !name || !service_type) {
      await transaction.rollback();
      return res.status(400).json({ message: 'Farm ID, name, and service type are required' });
    }

    const newServiceProvider = await ServiceProvider.create({
      farm_id,
      name,
      service_type,
      contact_name,
      email,
      phone,
      address,
      website,
      description,
      service_area,
      rates,
      availability,
      certifications,
      notes,
      is_verified: is_verified !== undefined ? is_verified : false,
      is_active: is_active !== undefined ? is_active : true
    }, { transaction });

    await transaction.commit();
    res.status(201).json(newServiceProvider);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating service provider:', error);
    res.status(500).json({ message: 'Failed to create service provider', error: error.message });
  }
};

// Update a service provider
export const updateServiceProvider = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const {
      name,
      service_type,
      contact_name,
      email,
      phone,
      address,
      website,
      description,
      service_area,
      rates,
      availability,
      certifications,
      notes,
      is_verified,
      is_active
    } = req.body;

    const serviceProvider = await ServiceProvider.findByPk(id, { transaction });

    if (!serviceProvider) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Service provider not found' });
    }

    // Update service provider fields
    if (name !== undefined) serviceProvider.name = name;
    if (service_type !== undefined) serviceProvider.service_type = service_type;
    if (contact_name !== undefined) serviceProvider.contact_name = contact_name;
    if (email !== undefined) serviceProvider.email = email;
    if (phone !== undefined) serviceProvider.phone = phone;
    if (address !== undefined) serviceProvider.address = address;
    if (website !== undefined) serviceProvider.website = website;
    if (description !== undefined) serviceProvider.description = description;
    if (service_area !== undefined) serviceProvider.service_area = service_area;
    if (rates !== undefined) serviceProvider.rates = rates;
    if (availability !== undefined) serviceProvider.availability = availability;
    if (certifications !== undefined) serviceProvider.certifications = certifications;
    if (notes !== undefined) serviceProvider.notes = notes;
    if (is_verified !== undefined) serviceProvider.is_verified = is_verified;
    if (is_active !== undefined) serviceProvider.is_active = is_active;

    await serviceProvider.save({ transaction });
    await transaction.commit();

    res.status(200).json(serviceProvider);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating service provider:', error);
    res.status(500).json({ message: 'Failed to update service provider', error: error.message });
  }
};

// Delete a service provider
export const deleteServiceProvider = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    const serviceProvider = await ServiceProvider.findByPk(id, { transaction });

    if (!serviceProvider) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Service provider not found' });
    }

    await serviceProvider.destroy({ transaction });
    await transaction.commit();

    res.status(200).json({ message: 'Service provider deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting service provider:', error);
    res.status(500).json({ message: 'Failed to delete service provider', error: error.message });
  }
};

// Search service providers
export const searchServiceProviders = async (req, res) => {
  try {
    const { query, serviceType, location } = req.query;

    if (!query && !serviceType && !location) {
      return res.status(400).json({ message: 'At least one search parameter is required' });
    }

    const whereClause = {};
    
    // Search by name or description
    if (query) {
      whereClause[sequelize.Op.or] = [
        { name: { [sequelize.Op.iLike]: `%${query}%` } },
        { description: { [sequelize.Op.iLike]: `%${query}%` } }
      ];
    }
    
    // Filter by service type
    if (serviceType) {
      whereClause.service_type = serviceType;
    }
    
    // Filter by location (simple text match for now)
    if (location) {
      whereClause.service_area = { [sequelize.Op.iLike]: `%${location}%` };
    }

    const serviceProviders = await ServiceProvider.findAll({
      where: whereClause,
      order: [['name', 'ASC']]
    });

    res.status(200).json(serviceProviders);
  } catch (error) {
    console.error('Error searching service providers:', error);
    res.status(500).json({ message: 'Failed to search service providers', error: error.message });
  }
};

// Rate a service provider
export const rateServiceProvider = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { rating, review } = req.body;

    if (!rating || rating < 1 || rating > 5) {
      await transaction.rollback();
      return res.status(400).json({ message: 'Rating is required and must be between 1 and 5' });
    }

    const serviceProvider = await ServiceProvider.findByPk(id, { transaction });

    if (!serviceProvider) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Service provider not found' });
    }

    // In a real application, you would store the rating in a separate table
    // and calculate the average rating for the service provider
    // For simplicity, we're just updating the rating directly
    serviceProvider.rating = rating;

    await serviceProvider.save({ transaction });
    await transaction.commit();

    res.status(200).json({ message: 'Service provider rated successfully', rating });
  } catch (error) {
    await transaction.rollback();
    console.error('Error rating service provider:', error);
    res.status(500).json({ message: 'Failed to rate service provider', error: error.message });
  }
};