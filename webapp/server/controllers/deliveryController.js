import Delivery from '../models/Delivery.js';
import Farm from '../models/Farm.js';
import Driver from '../models/Driver.js';
import Customer from '../models/Customer.js';
import Order from '../models/Order.js';
import { sequelize } from '../config/database.js';

// Create a new delivery
export const createDelivery = async (req, res) => {
  try {
    const { 
      farmId,
      driverId,
      customerId,
      orderId,
      deliveryType = 'product',
      status = 'scheduled',
      scheduledDate,
      estimatedArrival,
      actualDeliveryDate,
      deliveryAddress,
      deliveryCity,
      deliveryState,
      deliveryZip,
      deliveryCountry = 'USA',
      deliveryInstructions,
      signatureRequired = false,
      notes
    } = req.body;

    // Validate required fields
    if (!farmId || !scheduledDate || !deliveryAddress || !deliveryCity || !deliveryState || !deliveryZip) {
      return res.status(400).json({ 
        error: 'Farm ID, scheduled date, and delivery address details are required' 
      });
    }

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // If driverId is provided, check if driver exists
    if (driverId) {
      const driver = await Driver.findByPk(driverId);
      if (!driver) {
        return res.status(404).json({ error: 'Driver not found' });
      }
    }

    // If customerId is provided, check if customer exists
    if (customerId) {
      const customer = await Customer.findByPk(customerId);
      if (!customer) {
        return res.status(404).json({ error: 'Customer not found' });
      }
    }

    // If orderId is provided, check if order exists
    if (orderId) {
      const order = await Order.findByPk(orderId);
      if (!order) {
        return res.status(404).json({ error: 'Order not found' });
      }
    }

    // Create new delivery
    const delivery = await Delivery.create({
      farm_id: farmId,
      driver_id: driverId || null,
      customer_id: customerId || null,
      order_id: orderId || null,
      delivery_type: deliveryType,
      status,
      scheduled_date: scheduledDate,
      estimated_arrival: estimatedArrival || null,
      actual_delivery_date: actualDeliveryDate || null,
      delivery_address: deliveryAddress,
      delivery_city: deliveryCity,
      delivery_state: deliveryState,
      delivery_zip: deliveryZip,
      delivery_country: deliveryCountry,
      delivery_instructions: deliveryInstructions || null,
      signature_required: signatureRequired,
      notes: notes || null
    });

    return res.status(201).json({
      delivery: {
        id: delivery.id,
        farmId: delivery.farm_id,
        driverId: delivery.driver_id,
        customerId: delivery.customer_id,
        orderId: delivery.order_id,
        deliveryType: delivery.delivery_type,
        status: delivery.status,
        scheduledDate: delivery.scheduled_date,
        estimatedArrival: delivery.estimated_arrival,
        actualDeliveryDate: delivery.actual_delivery_date,
        deliveryAddress: delivery.delivery_address,
        deliveryCity: delivery.delivery_city,
        deliveryState: delivery.delivery_state,
        deliveryZip: delivery.delivery_zip,
        deliveryCountry: delivery.delivery_country,
        deliveryInstructions: delivery.delivery_instructions,
        signatureRequired: delivery.signature_required,
        signatureImage: delivery.signature_image,
        proofOfDeliveryImage: delivery.proof_of_delivery_image,
        notes: delivery.notes,
        createdAt: delivery.created_at,
        updatedAt: delivery.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating delivery:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all deliveries with optional filtering
export const getDeliveries = async (req, res) => {
  try {
    const { 
      farmId, 
      driverId,
      customerId,
      orderId,
      status,
      deliveryType,
      startDate,
      endDate,
      search,
      limit = 100,
      offset = 0
    } = req.query;

    // Build the where clause based on query parameters
    const whereClause = {};

    if (farmId) {
      whereClause.farm_id = farmId;
    }

    if (driverId) {
      whereClause.driver_id = driverId;
    }

    if (customerId) {
      whereClause.customer_id = customerId;
    }

    if (orderId) {
      whereClause.order_id = orderId;
    }

    if (status) {
      whereClause.status = status;
    }

    if (deliveryType) {
      whereClause.delivery_type = deliveryType;
    }

    // Date range filtering
    if (startDate || endDate) {
      whereClause.scheduled_date = {};

      if (startDate) {
        whereClause.scheduled_date[sequelize.Op.gte] = new Date(startDate);
      }

      if (endDate) {
        whereClause.scheduled_date[sequelize.Op.lte] = new Date(endDate);
      }
    }

    if (search) {
      whereClause[sequelize.Op.or] = [
        { delivery_address: { [sequelize.Op.iLike]: `%${search}%` } },
        { delivery_city: { [sequelize.Op.iLike]: `%${search}%` } },
        { delivery_state: { [sequelize.Op.iLike]: `%${search}%` } },
        { delivery_zip: { [sequelize.Op.iLike]: `%${search}%` } },
        { delivery_instructions: { [sequelize.Op.iLike]: `%${search}%` } },
        { notes: { [sequelize.Op.iLike]: `%${search}%` } }
      ];
    }

    // Get deliveries with pagination
    const deliveries = await Delivery.findAll({
      where: whereClause,
      include: [
        { model: Farm, as: 'deliveryFarm', attributes: ['id', 'name'] },
        { model: Driver, as: 'driver', attributes: ['id', 'first_name', 'last_name'] },
        { model: Customer, as: 'deliveryCustomer', attributes: ['id', 'name', 'email'] },
        { model: Order, as: 'order', attributes: ['id', 'order_number', 'total'] }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['scheduled_date', 'ASC']]
    });

    // Get total count for pagination
    const totalCount = await Delivery.count({ where: whereClause });

    return res.status(200).json({
      deliveries: deliveries.map(delivery => ({
        id: delivery.id,
        farmId: delivery.farm_id,
        driverId: delivery.driver_id,
        customerId: delivery.customer_id,
        orderId: delivery.order_id,
        deliveryType: delivery.delivery_type,
        status: delivery.status,
        scheduledDate: delivery.scheduled_date,
        estimatedArrival: delivery.estimated_arrival,
        actualDeliveryDate: delivery.actual_delivery_date,
        deliveryAddress: delivery.delivery_address,
        deliveryCity: delivery.delivery_city,
        deliveryState: delivery.delivery_state,
        deliveryZip: delivery.delivery_zip,
        deliveryCountry: delivery.delivery_country,
        deliveryInstructions: delivery.delivery_instructions,
        signatureRequired: delivery.signature_required,
        signatureImage: delivery.signature_image,
        proofOfDeliveryImage: delivery.proof_of_delivery_image,
        notes: delivery.notes,
        farm: delivery.deliveryFarm,
        driver: delivery.driver,
        customer: delivery.deliveryCustomer,
        order: delivery.order,
        createdAt: delivery.created_at,
        updatedAt: delivery.updated_at
      })),
      totalCount
    });
  } catch (error) {
    console.error('Error getting deliveries:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get delivery by ID
export const getDeliveryById = async (req, res) => {
  try {
    const { deliveryId } = req.params;

    const delivery = await Delivery.findByPk(deliveryId, {
      include: [
        { model: Farm, as: 'deliveryFarm', attributes: ['id', 'name'] },
        { model: Driver, as: 'driver', attributes: ['id', 'first_name', 'last_name'] },
        { model: Customer, as: 'deliveryCustomer', attributes: ['id', 'name', 'email'] },
        { model: Order, as: 'order', attributes: ['id', 'order_number', 'total'] }
      ]
    });

    if (!delivery) {
      return res.status(404).json({ error: 'Delivery not found' });
    }

    return res.status(200).json({
      delivery: {
        id: delivery.id,
        farmId: delivery.farm_id,
        driverId: delivery.driver_id,
        customerId: delivery.customer_id,
        orderId: delivery.order_id,
        deliveryType: delivery.delivery_type,
        status: delivery.status,
        scheduledDate: delivery.scheduled_date,
        estimatedArrival: delivery.estimated_arrival,
        actualDeliveryDate: delivery.actual_delivery_date,
        deliveryAddress: delivery.delivery_address,
        deliveryCity: delivery.delivery_city,
        deliveryState: delivery.delivery_state,
        deliveryZip: delivery.delivery_zip,
        deliveryCountry: delivery.delivery_country,
        deliveryInstructions: delivery.delivery_instructions,
        signatureRequired: delivery.signature_required,
        signatureImage: delivery.signature_image,
        proofOfDeliveryImage: delivery.proof_of_delivery_image,
        notes: delivery.notes,
        farm: delivery.deliveryFarm,
        driver: delivery.driver,
        customer: delivery.deliveryCustomer,
        order: delivery.order,
        createdAt: delivery.created_at,
        updatedAt: delivery.updated_at
      }
    });
  } catch (error) {
    console.error('Error getting delivery by ID:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update delivery
export const updateDelivery = async (req, res) => {
  try {
    const { deliveryId } = req.params;
    const { 
      driverId,
      customerId,
      orderId,
      deliveryType,
      status,
      scheduledDate,
      estimatedArrival,
      actualDeliveryDate,
      deliveryAddress,
      deliveryCity,
      deliveryState,
      deliveryZip,
      deliveryCountry,
      deliveryInstructions,
      signatureRequired,
      signatureImage,
      proofOfDeliveryImage,
      notes
    } = req.body;

    const delivery = await Delivery.findByPk(deliveryId);
    if (!delivery) {
      return res.status(404).json({ error: 'Delivery not found' });
    }

    // If driverId is provided, check if driver exists
    if (driverId !== undefined) {
      if (driverId) {
        const driver = await Driver.findByPk(driverId);
        if (!driver) {
          return res.status(404).json({ error: 'Driver not found' });
        }
        delivery.driver_id = driverId;
      } else {
        delivery.driver_id = null;
      }
    }

    // If customerId is provided, check if customer exists
    if (customerId !== undefined) {
      if (customerId) {
        const customer = await Customer.findByPk(customerId);
        if (!customer) {
          return res.status(404).json({ error: 'Customer not found' });
        }
        delivery.customer_id = customerId;
      } else {
        delivery.customer_id = null;
      }
    }

    // If orderId is provided, check if order exists
    if (orderId !== undefined) {
      if (orderId) {
        const order = await Order.findByPk(orderId);
        if (!order) {
          return res.status(404).json({ error: 'Order not found' });
        }
        delivery.order_id = orderId;
      } else {
        delivery.order_id = null;
      }
    }

    // Update delivery fields if provided
    if (deliveryType !== undefined) delivery.delivery_type = deliveryType;
    if (status !== undefined) delivery.status = status;
    if (scheduledDate !== undefined) delivery.scheduled_date = scheduledDate;
    if (estimatedArrival !== undefined) delivery.estimated_arrival = estimatedArrival;
    if (actualDeliveryDate !== undefined) delivery.actual_delivery_date = actualDeliveryDate;
    if (deliveryAddress !== undefined) delivery.delivery_address = deliveryAddress;
    if (deliveryCity !== undefined) delivery.delivery_city = deliveryCity;
    if (deliveryState !== undefined) delivery.delivery_state = deliveryState;
    if (deliveryZip !== undefined) delivery.delivery_zip = deliveryZip;
    if (deliveryCountry !== undefined) delivery.delivery_country = deliveryCountry;
    if (deliveryInstructions !== undefined) delivery.delivery_instructions = deliveryInstructions;
    if (signatureRequired !== undefined) delivery.signature_required = signatureRequired;
    if (signatureImage !== undefined) delivery.signature_image = signatureImage;
    if (proofOfDeliveryImage !== undefined) delivery.proof_of_delivery_image = proofOfDeliveryImage;
    if (notes !== undefined) delivery.notes = notes;

    await delivery.save();

    return res.status(200).json({
      delivery: {
        id: delivery.id,
        farmId: delivery.farm_id,
        driverId: delivery.driver_id,
        customerId: delivery.customer_id,
        orderId: delivery.order_id,
        deliveryType: delivery.delivery_type,
        status: delivery.status,
        scheduledDate: delivery.scheduled_date,
        estimatedArrival: delivery.estimated_arrival,
        actualDeliveryDate: delivery.actual_delivery_date,
        deliveryAddress: delivery.delivery_address,
        deliveryCity: delivery.delivery_city,
        deliveryState: delivery.delivery_state,
        deliveryZip: delivery.delivery_zip,
        deliveryCountry: delivery.delivery_country,
        deliveryInstructions: delivery.delivery_instructions,
        signatureRequired: delivery.signature_required,
        signatureImage: delivery.signature_image,
        proofOfDeliveryImage: delivery.proof_of_delivery_image,
        notes: delivery.notes,
        createdAt: delivery.created_at,
        updatedAt: delivery.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating delivery:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete delivery
export const deleteDelivery = async (req, res) => {
  try {
    const { deliveryId } = req.params;

    const delivery = await Delivery.findByPk(deliveryId);
    if (!delivery) {
      return res.status(404).json({ error: 'Delivery not found' });
    }

    await delivery.destroy();

    return res.status(200).json({
      message: 'Delivery deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting delivery:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update delivery status
export const updateDeliveryStatus = async (req, res) => {
  try {
    const { deliveryId } = req.params;
    const { status, actualDeliveryDate, signatureImage, proofOfDeliveryImage, notes } = req.body;

    if (!status) {
      return res.status(400).json({ error: 'Status is required' });
    }

    const delivery = await Delivery.findByPk(deliveryId);
    if (!delivery) {
      return res.status(404).json({ error: 'Delivery not found' });
    }

    // Update status and related fields
    delivery.status = status;

    if (status === 'delivered' && !delivery.actual_delivery_date) {
      delivery.actual_delivery_date = actualDeliveryDate || new Date();
    }

    if (signatureImage) {
      delivery.signature_image = signatureImage;
    }

    if (proofOfDeliveryImage) {
      delivery.proof_of_delivery_image = proofOfDeliveryImage;
    }

    if (notes) {
      delivery.notes = delivery.notes 
        ? `${delivery.notes}\n${new Date().toISOString()}: ${notes}`
        : `${new Date().toISOString()}: ${notes}`;
    }

    await delivery.save();

    return res.status(200).json({
      delivery: {
        id: delivery.id,
        status: delivery.status,
        actualDeliveryDate: delivery.actual_delivery_date,
        signatureImage: delivery.signature_image,
        proofOfDeliveryImage: delivery.proof_of_delivery_image,
        notes: delivery.notes,
        updatedAt: delivery.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating delivery status:', error);
    return res.status(500).json({ error: error.message });
  }
};
