import CropRotation from '../models/CropRotation.js';
import Farm from '../models/Farm.js';
import Field from '../models/Field.js';
import { Op } from 'sequelize';
import { sequelize } from '../config/database.js';
import NodeCache from 'node-cache';
import { generateCropRotationAnalysis } from '../services/aiAnalysisService.js';

// Initialize cache with standard TTL of 10 minutes and check period of 60 seconds
const cache = new NodeCache({ stdTTL: 600, checkperiod: 60 });

// Get all crop rotation plans for a farm
export const getCropRotationPlans = async (req, res) => {
  try {
    const { farmId, fieldId } = req.query;

    if (!farmId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Farm ID is required' 
      });
    }

    // Create a cache key based on the query parameters
    const cacheKey = `rotation_plans_${farmId}${fieldId ? '_' + fieldId : ''}`;

    // Check if data exists in cache
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached crop rotation plans for farm ${farmId}`);
      return res.status(200).json({ 
        success: true, 
        rotationPlans: cachedData,
        fromCache: true
      });
    }

    // Build where clause for filtering
    const whereClause = { farm_id: farmId };

    if (fieldId) whereClause.field_id = fieldId;

    // Get all rotation plans with filtering
    const rotationPlans = await CropRotation.findAll({
      where: whereClause,
      include: [
        {
          model: Field,
          attributes: ['id', 'name', 'area', 'area_unit']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    // Transform the data
    const transformedPlans = rotationPlans.map(plan => ({
      id: plan.id,
      field_id: plan.field_id,
      field_name: plan.Field ? plan.Field.name : 'Unknown Field',
      current_crop: plan.current_crop,
      recommended_sequence: plan.recommended_sequence,
      benefits: plan.benefits || [],
      rotation_years: plan.rotation_years,
      soil_health_impact: plan.soil_health_impact,
      created_at: plan.created_at
    }));

    // Store in cache
    cache.set(cacheKey, transformedPlans);
    console.log(`Cached crop rotation plans for farm ${farmId}`);

    return res.status(200).json({ 
      success: true, 
      rotationPlans: transformedPlans
    });
  } catch (error) {
    console.error('Error getting crop rotation plans:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to get crop rotation plans', 
      error: error.message 
    });
  }
};

// Get a single crop rotation plan by ID
export const getCropRotationPlanById = async (req, res) => {
  try {
    const { planId } = req.params;

    // Create a cache key for this specific plan
    const cacheKey = `rotation_plan_${planId}`;

    // Check if data exists in cache
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached crop rotation plan for ID ${planId}`);
      return res.status(200).json({ 
        success: true, 
        rotationPlan: cachedData,
        fromCache: true
      });
    }

    const rotationPlan = await CropRotation.findByPk(planId, {
      include: [
        {
          model: Field,
          attributes: ['id', 'name', 'area', 'area_unit']
        },
        {
          model: Farm,
          attributes: ['id', 'name']
        }
      ]
    });

    if (!rotationPlan) {
      return res.status(404).json({ 
        success: false, 
        message: 'Crop rotation plan not found' 
      });
    }

    // Transform the data
    const transformedPlan = {
      id: rotationPlan.id,
      farm_id: rotationPlan.farm_id,
      farm_name: rotationPlan.Farm ? rotationPlan.Farm.name : 'Unknown Farm',
      field_id: rotationPlan.field_id,
      field_name: rotationPlan.Field ? rotationPlan.Field.name : 'Unknown Field',
      current_crop: rotationPlan.current_crop,
      recommended_sequence: rotationPlan.recommended_sequence,
      benefits: rotationPlan.benefits || [],
      rotation_years: rotationPlan.rotation_years,
      soil_health_impact: rotationPlan.soil_health_impact,
      created_at: rotationPlan.created_at,
      updated_at: rotationPlan.updated_at
    };

    // Store in cache
    cache.set(cacheKey, transformedPlan);
    console.log(`Cached crop rotation plan for ID ${planId}`);

    return res.status(200).json({ 
      success: true, 
      rotationPlan: transformedPlan
    });
  } catch (error) {
    console.error('Error getting crop rotation plan by ID:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to get crop rotation plan', 
      error: error.message 
    });
  }
};

// Create a new crop rotation plan
export const createCropRotationPlan = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      farmId, 
      fieldId, 
      currentCrop, 
      recommendedSequence, 
      benefits, 
      rotationYears, 
      soilHealthImpact 
    } = req.body;

    // Validate required fields
    if (!farmId || !fieldId || !currentCrop || !recommendedSequence) {
      await transaction.rollback();
      return res.status(400).json({ 
        success: false, 
        message: 'Farm ID, field ID, current crop, and recommended sequence are required' 
      });
    }

    // Create rotation plan
    const rotationPlan = await CropRotation.create({
      farm_id: farmId,
      field_id: fieldId,
      current_crop: currentCrop,
      recommended_sequence: recommendedSequence,
      benefits: benefits || [],
      rotation_years: rotationYears || recommendedSequence.length,
      soil_health_impact: soilHealthImpact || 'positive'
    }, { transaction });

    await transaction.commit();

    // Fetch the created rotation plan with associations
    const createdPlan = await CropRotation.findByPk(rotationPlan.id, {
      include: [
        {
          model: Field,
          attributes: ['id', 'name', 'area', 'area_unit']
        }
      ]
    });

    // Transform the data
    const transformedPlan = {
      id: createdPlan.id,
      field_id: createdPlan.field_id,
      field_name: createdPlan.Field ? createdPlan.Field.name : 'Unknown Field',
      current_crop: createdPlan.current_crop,
      recommended_sequence: createdPlan.recommended_sequence,
      benefits: createdPlan.benefits || [],
      rotation_years: createdPlan.rotation_years,
      soil_health_impact: createdPlan.soil_health_impact,
      created_at: createdPlan.created_at
    };

    // Invalidate farm cache
    const farmCacheKey = `rotation_plans_${farmId}`;
    const fieldCacheKey = `rotation_plans_${farmId}_${fieldId}`;
    cache.del(farmCacheKey);
    cache.del(fieldCacheKey);
    console.log(`Invalidated cache for farm ${farmId} after creating new rotation plan`);

    // Cache the individual plan
    const planCacheKey = `rotation_plan_${createdPlan.id}`;
    cache.set(planCacheKey, transformedPlan);
    console.log(`Cached new rotation plan with ID ${createdPlan.id}`);

    return res.status(201).json({ 
      success: true, 
      message: 'Crop rotation plan created successfully',
      rotationPlan: transformedPlan
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating crop rotation plan:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to create crop rotation plan', 
      error: error.message 
    });
  }
};

// Generate a crop rotation plan using AI
export const generateCropRotationPlan = async (req, res) => {
  try {
    const { farmId, fieldId } = req.body;

    if (!farmId || !fieldId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Farm ID and field ID are required' 
      });
    }

    // Get field data
    const field = await Field.findByPk(fieldId);

    if (!field) {
      return res.status(404).json({ 
        success: false, 
        message: 'Field not found' 
      });
    }

    // Get farm data
    const farm = await Farm.findByPk(farmId);

    if (!farm) {
      return res.status(404).json({ 
        success: false, 
        message: 'Farm not found' 
      });
    }

    // Get previous crops data for the field
    const previousCropsData = await sequelize.query(
      `SELECT c.crop_name, c.planting_date, c.harvest_date, c.yield_amount, c.yield_unit
       FROM crops c
       WHERE c.field_id = $1
       ORDER BY c.planting_date DESC
       LIMIT 5`,
      { 
        replacements: [fieldId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    // Prepare crop data for AI analysis
    const cropData = {
      currentCrop: field.current_crop || null,
      previousCrops: previousCropsData
    };

    // Generate AI crop rotation analysis
    const aiAnalysis = await generateCropRotationAnalysis(farmId, fieldId, cropData);

    // Extract recommended crops and benefits from AI analysis
    const recommendedSequence = [];

    // Add recommended next crops to the sequence
    if (aiAnalysis.recommended_next_crops && aiAnalysis.recommended_next_crops.length > 0) {
      for (const crop of aiAnalysis.recommended_next_crops) {
        recommendedSequence.push(crop.crop);
      }
    }

    // Add rotation plan years if available
    if (aiAnalysis.rotation_plan) {
      const rotationPlanYears = Object.values(aiAnalysis.rotation_plan);
      for (const crop of rotationPlanYears) {
        if (!recommendedSequence.includes(crop)) {
          recommendedSequence.push(crop);
        }
      }
    }

    // If we still don't have enough crops, add some defaults
    if (recommendedSequence.length < 3) {
      const defaultCrops = ['Soybeans', 'Winter Wheat', 'Cover Crop', 'Corn'];
      for (const crop of defaultCrops) {
        if (!recommendedSequence.includes(crop)) {
          recommendedSequence.push(crop);
          if (recommendedSequence.length >= 4) break;
        }
      }
    }

    // Add current crop at the end if it's not already in the sequence
    if (field.current_crop && !recommendedSequence.includes(field.current_crop)) {
      recommendedSequence.push(field.current_crop);
    }

    // Generate benefits from AI analysis
    const selectedBenefits = [];

    // Add soil health impact
    if (aiAnalysis.soil_health_impact) {
      selectedBenefits.push(aiAnalysis.soil_health_impact);
    }

    // Add pest management impact
    if (aiAnalysis.pest_management_impact) {
      selectedBenefits.push(aiAnalysis.pest_management_impact);
    }

    // Add yield impact
    if (aiAnalysis.yield_impact) {
      selectedBenefits.push(aiAnalysis.yield_impact);
    }

    // If we don't have enough benefits, add some defaults
    if (selectedBenefits.length < 3) {
      const defaultBenefits = [
        'Improved soil nitrogen levels',
        'Reduced pest pressure',
        'Enhanced soil structure',
        'Increased biodiversity',
        'Balanced nutrient utilization'
      ];

      for (const benefit of defaultBenefits) {
        if (!selectedBenefits.includes(benefit)) {
          selectedBenefits.push(benefit);
          if (selectedBenefits.length >= 5) break;
        }
      }
    }

    // Create the rotation plan
    const rotationPlan = await CropRotation.create({
      farm_id: farmId,
      field_id: fieldId,
      current_crop: field.current_crop || (aiAnalysis.current_crop || 'Unknown'),
      recommended_sequence: recommendedSequence,
      benefits: selectedBenefits,
      rotation_years: recommendedSequence.length,
      soil_health_impact: aiAnalysis.soil_health_impact ? 'positive' : 'neutral',
      confidence_score: aiAnalysis.confidence_score || null
    });

    // Fetch the created rotation plan with associations
    const createdPlan = await CropRotation.findByPk(rotationPlan.id, {
      include: [
        {
          model: Field,
          attributes: ['id', 'name', 'area', 'area_unit']
        }
      ]
    });

    // Transform the data
    const transformedPlan = {
      id: createdPlan.id,
      field_id: createdPlan.field_id,
      field_name: createdPlan.Field ? createdPlan.Field.name : 'Unknown Field',
      current_crop: createdPlan.current_crop,
      recommended_sequence: createdPlan.recommended_sequence,
      benefits: createdPlan.benefits || [],
      rotation_years: createdPlan.rotation_years,
      soil_health_impact: createdPlan.soil_health_impact,
      confidence_score: createdPlan.confidence_score,
      created_at: createdPlan.created_at
    };

    // Invalidate farm cache
    const farmCacheKey = `rotation_plans_${farmId}`;
    const fieldCacheKey = `rotation_plans_${farmId}_${fieldId}`;
    cache.del(farmCacheKey);
    cache.del(fieldCacheKey);
    console.log(`Invalidated cache for farm ${farmId} after generating new rotation plan`);

    // Cache the individual plan
    const planCacheKey = `rotation_plan_${createdPlan.id}`;
    cache.set(planCacheKey, transformedPlan);
    console.log(`Cached new rotation plan with ID ${createdPlan.id}`);

    return res.status(201).json({ 
      success: true, 
      message: 'Crop rotation plan generated successfully',
      rotationPlan: transformedPlan
    });
  } catch (error) {
    console.error('Error generating crop rotation plan:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to generate crop rotation plan', 
      error: error.message 
    });
  }
};

// Delete a crop rotation plan
export const deleteCropRotationPlan = async (req, res) => {
  try {
    const { planId } = req.params;

    // Find the rotation plan
    const rotationPlan = await CropRotation.findByPk(planId);

    if (!rotationPlan) {
      return res.status(404).json({ 
        success: false, 
        message: 'Crop rotation plan not found' 
      });
    }

    // Get farm_id and field_id before deleting
    const { farm_id, field_id } = rotationPlan;

    // Delete the rotation plan
    await rotationPlan.destroy();

    // Invalidate caches
    const planCacheKey = `rotation_plan_${planId}`;
    const farmCacheKey = `rotation_plans_${farm_id}`;
    const fieldCacheKey = `rotation_plans_${farm_id}_${field_id}`;

    cache.del(planCacheKey);
    cache.del(farmCacheKey);
    cache.del(fieldCacheKey);

    console.log(`Invalidated caches after deleting rotation plan ${planId}`);

    return res.status(200).json({ 
      success: true, 
      message: 'Crop rotation plan deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting crop rotation plan:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to delete crop rotation plan', 
      error: error.message 
    });
  }
};
