import Supplier from '../models/Supplier.js';
import SupplierProduct from '../models/SupplierProduct.js';
import SupplierReview from '../models/SupplierReview.js';
import Product from '../models/Product.js';
import User from '../models/User.js';
import { sequelize } from '../config/database.js';
import axios from 'axios';

// Get all suppliers (for global admin)
export const getAllSuppliers = async (req, res) => {
  try {
    const suppliers = await Supplier.findAll({
      order: [['name', 'ASC']]
    });

    res.status(200).json(suppliers);
  } catch (error) {
    console.error('Error fetching all suppliers:', error);
    res.status(500).json({ message: 'Failed to fetch suppliers', error: error.message });
  }
};

// Get all suppliers for a farm
export const getSuppliers = async (req, res) => {
  try {
    const { farmId } = req.query;

    // If farmId is not provided, fall back to getting all suppliers
    if (!farmId) {
      return getAllSuppliers(req, res);
    }

    const suppliers = await Supplier.findAll({
      where: {
        farm_id: farmId
      },
      order: [['name', 'ASC']]
    });

    res.status(200).json(suppliers);
  } catch (error) {
    console.error('Error fetching suppliers:', error);
    res.status(500).json({ message: 'Failed to fetch suppliers', error: error.message });
  }
};

// Get a single supplier by ID
export const getSupplier = async (req, res) => {
  try {
    const { id } = req.params;

    const supplier = await Supplier.findByPk(id);

    if (!supplier) {
      return res.status(404).json({ message: 'Supplier not found' });
    }

    res.status(200).json(supplier);
  } catch (error) {
    console.error('Error fetching supplier:', error);
    res.status(500).json({ message: 'Failed to fetch supplier', error: error.message });
  }
};

// Create a new supplier
export const createSupplier = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const {
      farm_id,
      user_id,
      name,
      contact_name,
      email,
      phone,
      address,
      website,
      description,
      payment_terms,
      notes,
      is_active,
      latitude,
      longitude,
      product_types,
      availability,
      is_preferred,
      api_integration,
      api_key,
      api_endpoint,
      business_hours
    } = req.body;

    // Farm ID is now optional, but name is still required
    if (!name) {
      await transaction.rollback();
      return res.status(400).json({ message: 'Name is required' });
    }

    const newSupplier = await Supplier.create({
      farm_id,
      user_id,
      name,
      contact_name,
      email,
      phone,
      address,
      website,
      description,
      payment_terms,
      notes,
      is_active: is_active !== undefined ? is_active : true,
      latitude,
      longitude,
      product_types,
      availability,
      is_preferred: is_preferred !== undefined ? is_preferred : false,
      api_integration,
      api_key,
      api_endpoint,
      business_hours
    }, { transaction });

    await transaction.commit();
    res.status(201).json(newSupplier);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating supplier:', error);
    res.status(500).json({ message: 'Failed to create supplier', error: error.message });
  }
};

// Update a supplier
export const updateSupplier = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const {
      name,
      contact_name,
      email,
      phone,
      address,
      website,
      description,
      payment_terms,
      notes,
      is_active,
      user_id,
      latitude,
      longitude,
      product_types,
      availability,
      is_preferred,
      api_integration,
      api_key,
      api_endpoint,
      business_hours
    } = req.body;

    const supplier = await Supplier.findByPk(id, { transaction });

    if (!supplier) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Supplier not found' });
    }

    // Update supplier fields
    if (name !== undefined) supplier.name = name;
    if (contact_name !== undefined) supplier.contact_name = contact_name;
    if (email !== undefined) supplier.email = email;
    if (phone !== undefined) supplier.phone = phone;
    if (address !== undefined) supplier.address = address;
    if (website !== undefined) supplier.website = website;
    if (description !== undefined) supplier.description = description;
    if (payment_terms !== undefined) supplier.payment_terms = payment_terms;
    if (notes !== undefined) supplier.notes = notes;
    if (is_active !== undefined) supplier.is_active = is_active;
    if (user_id !== undefined) supplier.user_id = user_id;
    if (latitude !== undefined) supplier.latitude = latitude;
    if (longitude !== undefined) supplier.longitude = longitude;
    if (product_types !== undefined) supplier.product_types = product_types;
    if (availability !== undefined) supplier.availability = availability;
    if (is_preferred !== undefined) supplier.is_preferred = is_preferred;
    if (api_integration !== undefined) supplier.api_integration = api_integration;
    if (api_key !== undefined) supplier.api_key = api_key;
    if (api_endpoint !== undefined) supplier.api_endpoint = api_endpoint;
    if (business_hours !== undefined) supplier.business_hours = business_hours;

    await supplier.save({ transaction });
    await transaction.commit();

    res.status(200).json(supplier);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating supplier:', error);
    res.status(500).json({ message: 'Failed to update supplier', error: error.message });
  }
};

// Delete a supplier
export const deleteSupplier = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    const supplier = await Supplier.findByPk(id, { transaction });

    if (!supplier) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Supplier not found' });
    }

    await supplier.destroy({ transaction });
    await transaction.commit();

    res.status(200).json({ message: 'Supplier deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting supplier:', error);
    res.status(500).json({ message: 'Failed to delete supplier', error: error.message });
  }
};

// ===== Supplier Product Management =====

// Get all products for a supplier
export const getSupplierProducts = async (req, res) => {
  try {
    const { supplierId } = req.params;

    const supplier = await Supplier.findByPk(supplierId);
    if (!supplier) {
      return res.status(404).json({ message: 'Supplier not found' });
    }

    const products = await SupplierProduct.findAll({
      where: { supplier_id: supplierId },
      include: [
        {
          model: Product,
          attributes: ['id', 'name', 'description', 'price', 'unit', 'type'],
          required: false
        }
      ],
      order: [['name', 'ASC']]
    });

    res.status(200).json(products);
  } catch (error) {
    console.error('Error fetching supplier products:', error);
    res.status(500).json({ message: 'Failed to fetch supplier products', error: error.message });
  }
};

// Get a single product for a supplier
export const getSupplierProduct = async (req, res) => {
  try {
    const { id } = req.params;

    const product = await SupplierProduct.findByPk(id, {
      include: [
        {
          model: Product,
          attributes: ['id', 'name', 'description', 'price', 'unit', 'type'],
          required: false
        },
        {
          model: Supplier,
          attributes: ['id', 'name', 'contact_name', 'email', 'phone']
        }
      ]
    });

    if (!product) {
      return res.status(404).json({ message: 'Supplier product not found' });
    }

    res.status(200).json(product);
  } catch (error) {
    console.error('Error fetching supplier product:', error);
    res.status(500).json({ message: 'Failed to fetch supplier product', error: error.message });
  }
};

// Create a new product for a supplier
export const createSupplierProduct = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const {
      supplier_id,
      product_id,
      name,
      description,
      sku,
      price,
      unit,
      category,
      type,
      availability,
      lead_time,
      minimum_order_quantity,
      is_active,
      external_id,
      external_data
    } = req.body;

    if (!supplier_id || !name) {
      await transaction.rollback();
      return res.status(400).json({ message: 'Supplier ID and name are required' });
    }

    // Check if supplier exists
    const supplier = await Supplier.findByPk(supplier_id);
    if (!supplier) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Supplier not found' });
    }

    // If product_id is provided, check if it exists
    if (product_id) {
      const product = await Product.findByPk(product_id);
      if (!product) {
        await transaction.rollback();
        return res.status(404).json({ message: 'Product not found' });
      }
    }

    const newProduct = await SupplierProduct.create({
      supplier_id,
      product_id,
      name,
      description,
      sku,
      price,
      unit,
      category,
      type,
      availability,
      lead_time,
      minimum_order_quantity,
      is_active: is_active !== undefined ? is_active : true,
      external_id,
      external_data
    }, { transaction });

    await transaction.commit();

    // Return the created product with related data
    const result = await SupplierProduct.findByPk(newProduct.id, {
      include: [
        {
          model: Product,
          attributes: ['id', 'name', 'description', 'price', 'unit', 'type'],
          required: false
        },
        {
          model: Supplier,
          attributes: ['id', 'name', 'contact_name', 'email', 'phone']
        }
      ]
    });

    res.status(201).json(result);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating supplier product:', error);
    res.status(500).json({ message: 'Failed to create supplier product', error: error.message });
  }
};

// Update a supplier product
export const updateSupplierProduct = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const {
      product_id,
      name,
      description,
      sku,
      price,
      unit,
      category,
      type,
      availability,
      lead_time,
      minimum_order_quantity,
      is_active,
      external_id,
      external_data
    } = req.body;

    const product = await SupplierProduct.findByPk(id, { transaction });

    if (!product) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Supplier product not found' });
    }

    // If product_id is provided, check if it exists
    if (product_id && product_id !== product.product_id) {
      const systemProduct = await Product.findByPk(product_id);
      if (!systemProduct) {
        await transaction.rollback();
        return res.status(404).json({ message: 'Product not found' });
      }
    }

    // Update product fields
    if (product_id !== undefined) product.product_id = product_id;
    if (name !== undefined) product.name = name;
    if (description !== undefined) product.description = description;
    if (sku !== undefined) product.sku = sku;
    if (price !== undefined) product.price = price;
    if (unit !== undefined) product.unit = unit;
    if (category !== undefined) product.category = category;
    if (type !== undefined) product.type = type;
    if (availability !== undefined) product.availability = availability;
    if (lead_time !== undefined) product.lead_time = lead_time;
    if (minimum_order_quantity !== undefined) product.minimum_order_quantity = minimum_order_quantity;
    if (is_active !== undefined) product.is_active = is_active;
    if (external_id !== undefined) product.external_id = external_id;
    if (external_data !== undefined) product.external_data = external_data;

    await product.save({ transaction });
    await transaction.commit();

    // Return the updated product with related data
    const result = await SupplierProduct.findByPk(id, {
      include: [
        {
          model: Product,
          attributes: ['id', 'name', 'description', 'price', 'unit', 'type'],
          required: false
        },
        {
          model: Supplier,
          attributes: ['id', 'name', 'contact_name', 'email', 'phone']
        }
      ]
    });

    res.status(200).json(result);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating supplier product:', error);
    res.status(500).json({ message: 'Failed to update supplier product', error: error.message });
  }
};

// Delete a supplier product
export const deleteSupplierProduct = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    const product = await SupplierProduct.findByPk(id, { transaction });

    if (!product) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Supplier product not found' });
    }

    await product.destroy({ transaction });
    await transaction.commit();

    res.status(200).json({ message: 'Supplier product deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting supplier product:', error);
    res.status(500).json({ message: 'Failed to delete supplier product', error: error.message });
  }
};

// ===== Supplier Review Management =====

// Get all reviews for a supplier
export const getSupplierReviews = async (req, res) => {
  try {
    const { supplierId } = req.params;

    const supplier = await Supplier.findByPk(supplierId);
    if (!supplier) {
      return res.status(404).json({ message: 'Supplier not found' });
    }

    const reviews = await SupplierReview.findAll({
      where: { supplier_id: supplierId },
      include: [
        {
          model: User,
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    // Calculate average rating
    let totalRating = 0;
    reviews.forEach(review => {
      totalRating += review.rating;
    });
    const averageRating = reviews.length > 0 ? (totalRating / reviews.length).toFixed(2) : 0;

    res.status(200).json({
      reviews,
      meta: {
        count: reviews.length,
        average_rating: parseFloat(averageRating)
      }
    });
  } catch (error) {
    console.error('Error fetching supplier reviews:', error);
    res.status(500).json({ message: 'Failed to fetch supplier reviews', error: error.message });
  }
};

// Get a single review
export const getSupplierReview = async (req, res) => {
  try {
    const { id } = req.params;

    const review = await SupplierReview.findByPk(id, {
      include: [
        {
          model: User,
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Supplier,
          attributes: ['id', 'name', 'contact_name', 'email', 'phone']
        }
      ]
    });

    if (!review) {
      return res.status(404).json({ message: 'Review not found' });
    }

    res.status(200).json(review);
  } catch (error) {
    console.error('Error fetching supplier review:', error);
    res.status(500).json({ message: 'Failed to fetch supplier review', error: error.message });
  }
};

// Create a new review for a supplier
export const createSupplierReview = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const {
      supplier_id,
      user_id,
      rating,
      title,
      review,
      order_id,
      is_verified_purchase
    } = req.body;

    if (!supplier_id || !user_id || !rating) {
      await transaction.rollback();
      return res.status(400).json({ message: 'Supplier ID, user ID, and rating are required' });
    }

    // Validate rating
    if (rating < 1 || rating > 5) {
      await transaction.rollback();
      return res.status(400).json({ message: 'Rating must be between 1 and 5' });
    }

    // Check if supplier exists
    const supplier = await Supplier.findByPk(supplier_id);
    if (!supplier) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Supplier not found' });
    }

    // Check if user exists
    const user = await User.findByPk(user_id);
    if (!user) {
      await transaction.rollback();
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if user has already reviewed this supplier
    const existingReview = await SupplierReview.findOne({
      where: {
        supplier_id,
        user_id
      }
    });

    if (existingReview) {
      await transaction.rollback();
      return res.status(400).json({ message: 'User has already reviewed this supplier' });
    }

    const newReview = await SupplierReview.create({
      supplier_id,
      user_id,
      rating,
      title,
      review,
      order_id,
      is_verified_purchase: is_verified_purchase !== undefined ? is_verified_purchase : false,
      is_approved: true // Default to approved
    }, { transaction });

    // Update supplier's rating and review count
    const allReviews = await SupplierReview.findAll({
      where: { supplier_id }
    });

    let totalRating = 0;
    allReviews.forEach(r => {
      totalRating += r.rating;
    });
    const averageRating = (totalRating / allReviews.length).toFixed(2);

    await supplier.update({
      rating: averageRating,
      review_count: allReviews.length
    }, { transaction });

    await transaction.commit();

    // Return the created review with related data
    const result = await SupplierReview.findByPk(newReview.id, {
      include: [
        {
          model: User,
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Supplier,
          attributes: ['id', 'name', 'contact_name', 'email', 'phone']
        }
      ]
    });

    res.status(201).json(result);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating supplier review:', error);
    res.status(500).json({ message: 'Failed to create supplier review', error: error.message });
  }
};

// Update a supplier review
export const updateSupplierReview = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const {
      rating,
      title,
      review,
      is_verified_purchase,
      is_approved
    } = req.body;

    const reviewObj = await SupplierReview.findByPk(id, { transaction });

    if (!reviewObj) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Review not found' });
    }

    // Validate rating if provided
    if (rating !== undefined && (rating < 1 || rating > 5)) {
      await transaction.rollback();
      return res.status(400).json({ message: 'Rating must be between 1 and 5' });
    }

    // Update review fields
    if (rating !== undefined) reviewObj.rating = rating;
    if (title !== undefined) reviewObj.title = title;
    if (review !== undefined) reviewObj.review = review;
    if (is_verified_purchase !== undefined) reviewObj.is_verified_purchase = is_verified_purchase;
    if (is_approved !== undefined) reviewObj.is_approved = is_approved;

    await reviewObj.save({ transaction });

    // If rating changed, update supplier's average rating
    if (rating !== undefined) {
      const supplier = await Supplier.findByPk(reviewObj.supplier_id, { transaction });

      if (supplier) {
        const allReviews = await SupplierReview.findAll({
          where: { supplier_id: reviewObj.supplier_id }
        });

        let totalRating = 0;
        allReviews.forEach(r => {
          totalRating += r.rating;
        });
        const averageRating = (totalRating / allReviews.length).toFixed(2);

        await supplier.update({
          rating: averageRating
        }, { transaction });
      }
    }

    await transaction.commit();

    // Return the updated review with related data
    const result = await SupplierReview.findByPk(id, {
      include: [
        {
          model: User,
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Supplier,
          attributes: ['id', 'name', 'contact_name', 'email', 'phone']
        }
      ]
    });

    res.status(200).json(result);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating supplier review:', error);
    res.status(500).json({ message: 'Failed to update supplier review', error: error.message });
  }
};

// Delete a supplier review
export const deleteSupplierReview = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    const review = await SupplierReview.findByPk(id, { transaction });

    if (!review) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Review not found' });
    }

    const supplier_id = review.supplier_id;

    await review.destroy({ transaction });

    // Update supplier's rating and review count
    const supplier = await Supplier.findByPk(supplier_id, { transaction });

    if (supplier) {
      const allReviews = await SupplierReview.findAll({
        where: { supplier_id }
      });

      let totalRating = 0;
      allReviews.forEach(r => {
        totalRating += r.rating;
      });
      const averageRating = allReviews.length > 0 ? (totalRating / allReviews.length).toFixed(2) : 0;

      await supplier.update({
        rating: averageRating,
        review_count: allReviews.length
      }, { transaction });
    }

    await transaction.commit();

    res.status(200).json({ message: 'Supplier review deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting supplier review:', error);
    res.status(500).json({ message: 'Failed to delete supplier review', error: error.message });
  }
};

// ===== Supplier API Integration =====

// Check if a supplier's API integration is working
export const getSupplierApiStatus = async (req, res) => {
  try {
    const { supplierId } = req.params;

    const supplier = await Supplier.findByPk(supplierId);
    if (!supplier) {
      return res.status(404).json({ message: 'Supplier not found' });
    }

    // Check if supplier has API integration configured
    if (!supplier.api_endpoint || !supplier.api_key) {
      return res.status(400).json({ 
        message: 'Supplier does not have API integration configured',
        status: 'not_configured'
      });
    }

    try {
      // Test the API connection
      const response = await axios.get(`${supplier.api_endpoint}/status`, {
        headers: {
          'Authorization': `Bearer ${supplier.api_key}`,
          'Content-Type': 'application/json'
        },
        timeout: 5000 // 5 second timeout
      });

      // If we get here, the API is working
      res.status(200).json({
        message: 'API connection successful',
        status: 'connected',
        api_response: response.data
      });
    } catch (apiError) {
      console.error('API connection error:', apiError);

      // Return a more detailed error response
      res.status(200).json({
        message: 'API connection failed',
        status: 'error',
        error: {
          message: apiError.message,
          code: apiError.response?.status || 'unknown',
          details: apiError.response?.data || {}
        }
      });
    }
  } catch (error) {
    console.error('Error checking supplier API status:', error);
    res.status(500).json({ message: 'Failed to check supplier API status', error: error.message });
  }
};

// Sync products from a supplier's API
export const syncSupplierProducts = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { supplierId } = req.params;
    const { syncOptions } = req.body;

    const supplier = await Supplier.findByPk(supplierId);
    if (!supplier) {
      await transaction.rollback();
      return res.status(404).json({ message: 'Supplier not found' });
    }

    // Check if supplier has API integration configured
    if (!supplier.api_endpoint || !supplier.api_key) {
      await transaction.rollback();
      return res.status(400).json({ message: 'Supplier does not have API integration configured' });
    }

    // Get API configuration from supplier or use defaults
    const apiConfig = supplier.api_integration || {};

    // Determine API endpoint path based on supplier configuration or default to /products
    const productsEndpoint = apiConfig.products_endpoint || '/products';

    // Determine if we should update inventory levels only or full product details
    const updateInventoryOnly = syncOptions?.updateInventoryOnly || false;

    // Determine if we should auto-link products to inventory items
    const autoLinkToInventory = syncOptions?.autoLinkToInventory || false;

    // Determine if we should create new products if they don't exist
    const createNewProducts = syncOptions?.createNewProducts !== false; // Default to true

    try {
      // Fetch products from the supplier's API
      const response = await axios.get(`${supplier.api_endpoint}${productsEndpoint}`, {
        headers: {
          'Authorization': `Bearer ${supplier.api_key}`,
          'Content-Type': 'application/json',
          ...(apiConfig.custom_headers || {}) // Add any custom headers from the supplier's configuration
        },
        params: {
          ...(apiConfig.default_params || {}), // Add any default parameters from the supplier's configuration
          ...(syncOptions?.apiParams || {}) // Add any parameters from the sync options
        },
        timeout: apiConfig.timeout || 10000 // Use timeout from config or default to 10 seconds
      });

      // Extract products based on the supplier's API response format
      const responseFormat = apiConfig.response_format || { products_path: 'products' };
      const productsPath = responseFormat.products_path.split('.');

      let apiProducts = response.data;
      for (const path of productsPath) {
        if (apiProducts && typeof apiProducts === 'object') {
          apiProducts = apiProducts[path];
        } else {
          apiProducts = [];
          break;
        }
      }

      apiProducts = Array.isArray(apiProducts) ? apiProducts : [];

      if (apiProducts.length === 0) {
        await transaction.rollback();
        return res.status(200).json({ message: 'No products found in supplier API', products: [] });
      }

      // Get existing supplier products
      const existingProducts = await SupplierProduct.findAll({
        where: { 
          supplier_id: supplierId,
          external_id: { 
            [sequelize.Op.ne]: null 
          }
        }
      });

      // Create a map of existing products by external_id for quick lookup
      const existingProductMap = {};
      existingProducts.forEach(product => {
        if (product.external_id) {
          existingProductMap[product.external_id] = product;
        }
      });

      const created = [];
      const updated = [];
      const unchanged = [];
      const inventoryUpdated = [];

      // Define field mapping from API response to our database fields
      const fieldMapping = apiConfig.field_mapping || {
        id: 'id',
        name: 'name',
        description: 'description',
        sku: 'sku',
        price: 'price',
        unit: 'unit',
        category: 'category',
        type: 'type',
        availability: 'availability',
        lead_time: 'lead_time',
        minimum_order_quantity: 'minimum_order_quantity',
        stock_level: 'stock_level' // For inventory updates
      };

      // Process each product from the API
      for (const apiProduct of apiProducts) {
        // Extract the external ID using the configured field mapping
        const externalIdField = fieldMapping.id || 'id';
        const externalId = apiProduct[externalIdField]?.toString();

        // Skip products without an ID
        if (!externalId) continue;

        const existingProduct = existingProductMap[externalId];

        if (existingProduct) {
          if (updateInventoryOnly) {
            // Only update inventory/availability information
            const stockLevelField = fieldMapping.stock_level || 'stock_level';
            const availabilityField = fieldMapping.availability || 'availability';

            const stockLevel = apiProduct[stockLevelField];
            const availability = apiProduct[availabilityField];

            if (stockLevel !== undefined || availability !== undefined) {
              const updates = {};

              if (availability !== undefined) {
                updates.availability = availability;
              }

              // Update the external_data field with the latest data
              updates.external_data = {
                ...existingProduct.external_data,
                ...apiProduct,
                last_inventory_update: new Date().toISOString()
              };

              await existingProduct.update(updates, { transaction });

              // If auto-linking to inventory is enabled and we have a stock level
              if (autoLinkToInventory && stockLevel !== undefined) {
                try {
                  // Find linked inventory items
                  const ProductInventory = require('../models/ProductInventory.js').default;
                  const inventoryLinks = await ProductInventory.findAll({
                    where: {
                      product_id: existingProduct.product_id,
                      is_active: true
                    }
                  });

                  // Update inventory items if found
                  if (inventoryLinks.length > 0) {
                    for (const link of inventoryLinks) {
                      const InventoryItem = require('../models/InventoryItem.js').default;
                      const inventoryItem = await InventoryItem.findByPk(link.inventory_item_id);

                      if (inventoryItem) {
                        await inventoryItem.update({
                          quantity: stockLevel
                        }, { transaction });
                      }
                    }
                  }
                } catch (inventoryError) {
                  console.error('Error updating inventory:', inventoryError);
                  // Continue with the sync even if inventory update fails
                }
              }

              inventoryUpdated.push(existingProduct);
            } else {
              unchanged.push(existingProduct);
            }
          } else {
            // Update all product details
            // Map API fields to our database fields
            const updates = {};
            let hasChanges = false;

            for (const [dbField, apiField] of Object.entries(fieldMapping)) {
              if (dbField === 'id') continue; // Skip ID field

              const apiValue = apiProduct[apiField];
              if (apiValue !== undefined) {
                // For JSON fields, compare stringified values
                if (typeof apiValue === 'object' && apiValue !== null) {
                  if (JSON.stringify(existingProduct[dbField]) !== JSON.stringify(apiValue)) {
                    updates[dbField] = apiValue;
                    hasChanges = true;
                  }
                } else if (existingProduct[dbField] !== apiValue) {
                  updates[dbField] = apiValue;
                  hasChanges = true;
                }
              }
            }

            // Always update external_data with the latest data
            updates.external_data = apiProduct;

            if (hasChanges || Object.keys(updates).length > 1) { // > 1 because external_data is always included
              await existingProduct.update(updates, { transaction });
              updated.push(existingProduct);
            } else {
              unchanged.push(existingProduct);
            }
          }
        } else if (createNewProducts && !updateInventoryOnly) {
          // Create new product
          const newProductData = {
            supplier_id: supplierId,
            external_id: externalId,
            external_data: apiProduct,
            is_active: true
          };

          // Map API fields to our database fields
          for (const [dbField, apiField] of Object.entries(fieldMapping)) {
            if (dbField === 'id') continue; // Skip ID field

            const apiValue = apiProduct[apiField];
            if (apiValue !== undefined) {
              newProductData[dbField] = apiValue;
            }
          }

          // Set defaults for required fields if not provided
          newProductData.name = newProductData.name || 'Unknown Product';
          newProductData.price = newProductData.price || 0;

          const newProduct = await SupplierProduct.create(newProductData, { transaction });
          created.push(newProduct);
        }
      }

      await transaction.commit();

      res.status(200).json({
        message: 'Products synced successfully',
        summary: {
          total: apiProducts.length,
          created: created.length,
          updated: updated.length,
          unchanged: unchanged.length,
          inventoryUpdated: inventoryUpdated.length
        },
        created,
        updated,
        inventoryUpdated
      });
    } catch (apiError) {
      await transaction.rollback();
      console.error('API error during product sync:', apiError);

      res.status(500).json({
        message: 'Failed to sync products from supplier API',
        error: {
          message: apiError.message,
          code: apiError.response?.status || 'unknown',
          details: apiError.response?.data || {}
        }
      });
    }
  } catch (error) {
    await transaction.rollback();
    console.error('Error syncing supplier products:', error);
    res.status(500).json({ message: 'Failed to sync supplier products', error: error.message });
  }
};

// Check the status of an order through a supplier's API
export const checkOrderStatus = async (req, res) => {
  try {
    const { supplierId, orderId } = req.params;

    const supplier = await Supplier.findByPk(supplierId);
    if (!supplier) {
      return res.status(404).json({ message: 'Supplier not found' });
    }

    // Check if supplier has API integration configured
    if (!supplier.api_endpoint || !supplier.api_key) {
      return res.status(400).json({ message: 'Supplier does not have API integration configured' });
    }

    if (!orderId) {
      return res.status(400).json({ message: 'Order ID is required' });
    }

    try {
      // Fetch order status from the supplier's API
      const response = await axios.get(`${supplier.api_endpoint}/orders/${orderId}`, {
        headers: {
          'Authorization': `Bearer ${supplier.api_key}`,
          'Content-Type': 'application/json'
        },
        timeout: 5000 // 5 second timeout
      });

      // Return the order status
      res.status(200).json({
        message: 'Order status retrieved successfully',
        order_id: orderId,
        status: response.data.status || 'unknown',
        details: response.data
      });
    } catch (apiError) {
      console.error('API error checking order status:', apiError);

      res.status(500).json({
        message: 'Failed to check order status from supplier API',
        error: {
          message: apiError.message,
          code: apiError.response?.status || 'unknown',
          details: apiError.response?.data || {}
        }
      });
    }
  } catch (error) {
    console.error('Error checking order status:', error);
    res.status(500).json({ message: 'Failed to check order status', error: error.message });
  }
};
