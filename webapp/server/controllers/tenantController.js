// This file is deprecated. Use farmController.js instead.
import { sequelize } from '../config/database.js';

// Stub functions to avoid breaking existing imports
export const getAllTenants = async (req, res) => {
  console.warn('getAllTenants is deprecated. Use getAllFarms from farmController.js instead.');
  return res.status(410).json({ error: 'This endpoint is deprecated. Use /api/farms instead.' });
};

// Get a single tenant by ID
export const getTenantById = async (req, res) => {
  console.warn('getTenantById is deprecated. Use getFarmById from farmController.js instead.');
  return res.status(410).json({ error: 'This endpoint is deprecated. Use /api/farms/:farmId instead.' });
};

// Create a new tenant
export const createTenant = async (req, res) => {
  console.warn('createTenant is deprecated. Use createFarm from farmController.js instead.');
  return res.status(410).json({ error: 'This endpoint is deprecated. Use /api/farms instead.' });
};

// Update a tenant
export const updateTenant = async (req, res) => {
  console.warn('updateTenant is deprecated. Use updateFarm from farmController.js instead.');
  return res.status(410).json({ error: 'This endpoint is deprecated. Use /api/farms/:farmId instead.' });
};

// Delete a tenant (admin only)
export const deleteTenant = async (req, res) => {
  console.warn('deleteTenant is deprecated. Use deleteFarm from farmController.js instead.');
  return res.status(410).json({ error: 'This endpoint is deprecated. Use /api/farms/:farmId instead.' });
};

// Add a user to a tenant as admin
export const addFarmAdmin = async (req, res) => {
  console.warn('addFarmAdmin from tenantController is deprecated. Use addFarmAdmin from farmController.js instead.');
  return res.status(410).json({ error: 'This endpoint is deprecated. Use /api/farms/:farmId/admins instead.' });
};

// Remove a user from tenant admins
export const removeFarmAdmin = async (req, res) => {
  console.warn('removeFarmAdmin from tenantController is deprecated. Use removeFarmAdmin from farmController.js instead.');
  return res.status(410).json({ error: 'This endpoint is deprecated. Use /api/farms/:farmId/admins/:userId instead.' });
};

// Get all users for a tenant
export const getTenantUsers = async (req, res) => {
  console.warn('getTenantUsers is deprecated. Use getFarmUsers from farmController.js instead.');
  return res.status(410).json({ error: 'This endpoint is deprecated. Use /api/farms/:farmId/users instead.' });
};
