import { Op } from 'sequelize';
import Farm from '../models/Farm.js';
import Crop from '../models/Crop.js';
import Field from '../models/Field.js';
import Equipment from '../models/Equipment.js';
import Transaction from '../models/Transaction.js';

/**
 * Get ROI data for crops
 */
export const getCropROI = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { year = new Date().getFullYear() } = req.query;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get crops for the farm
    const crops = await Crop.findAll({
      where: {
        farm_id: farmId,
        harvest_date: {
          [Op.between]: [
            new Date(`${year}-01-01`),
            new Date(`${year}-12-31`)
          ]
        }
      },
      include: [
        {
          model: Transaction,
          as: 'transactions',
          required: false,
          where: {
            date: {
              [Op.between]: [
                new Date(`${year}-01-01`),
                new Date(`${year}-12-31`)
              ]
            }
          }
        }
      ]
    });

    // Calculate ROI for each crop
    const cropROIData = crops.map(crop => {
      // Calculate revenue (positive transactions)
      const revenue = crop.transactions
        ? crop.transactions
            .filter(t => t.amount > 0)
            .reduce((sum, t) => sum + t.amount, 0)
        : 0;

      // Calculate expenses (negative transactions)
      const expenses = crop.transactions
        ? crop.transactions
            .filter(t => t.amount < 0)
            .reduce((sum, t) => sum + Math.abs(t.amount), 0)
        : 0;

      // Calculate investment (seed cost, labor, etc.)
      const investment = crop.seed_cost + crop.labor_cost + crop.fertilizer_cost + crop.pesticide_cost;

      // Calculate profit
      const profit = revenue - expenses;

      // Calculate ROI
      const roi = investment > 0 ? (profit / investment) * 100 : 0;

      // Calculate payback period (in years)
      const paybackPeriod = profit > 0 ? investment / profit : 0;

      return {
        id: crop.id,
        name: crop.name,
        type: 'crop',
        investment,
        revenue,
        expenses,
        profit,
        roi,
        paybackPeriod,
        year: parseInt(year)
      };
    });

    return res.status(200).json({
      roiData: cropROIData
    });
  } catch (error) {
    console.error('Error getting crop ROI:', error);
    return res.status(500).json({ error: 'Failed to get crop ROI' });
  }
};

/**
 * Get ROI data for fields
 */
export const getFieldROI = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { year = new Date().getFullYear() } = req.query;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get fields for the farm
    const fields = await Field.findAll({
      where: {
        farm_id: farmId
      },
      include: [
        {
          model: Crop,
          as: 'crops',
          required: false,
          where: {
            harvest_date: {
              [Op.between]: [
                new Date(`${year}-01-01`),
                new Date(`${year}-12-31`)
              ]
            }
          },
          include: [
            {
              model: Transaction,
              as: 'transactions',
              required: false,
              where: {
                date: {
                  [Op.between]: [
                    new Date(`${year}-01-01`),
                    new Date(`${year}-12-31`)
                  ]
                }
              }
            }
          ]
        }
      ]
    });

    // Calculate ROI for each field
    const fieldROIData = fields.map(field => {
      // Calculate revenue from all crops in the field
      let revenue = 0;
      let expenses = 0;

      if (field.crops) {
        field.crops.forEach(crop => {
          if (crop.transactions) {
            // Add positive transactions to revenue
            revenue += crop.transactions
              .filter(t => t.amount > 0)
              .reduce((sum, t) => sum + t.amount, 0);

            // Add negative transactions to expenses
            expenses += crop.transactions
              .filter(t => t.amount < 0)
              .reduce((sum, t) => sum + Math.abs(t.amount), 0);
          }
        });
      }

      // Calculate investment (field purchase price or value)
      const investment = field.purchase_price || field.estimated_value || 0;

      // Calculate profit
      const profit = revenue - expenses;

      // Calculate ROI
      const roi = investment > 0 ? (profit / investment) * 100 : 0;

      // Calculate payback period (in years)
      const paybackPeriod = profit > 0 ? investment / profit : 0;

      return {
        id: field.id,
        name: field.name,
        type: 'field',
        investment,
        revenue,
        expenses,
        profit,
        roi,
        paybackPeriod,
        year: parseInt(year)
      };
    });

    return res.status(200).json({
      roiData: fieldROIData
    });
  } catch (error) {
    console.error('Error getting field ROI:', error);
    return res.status(500).json({ error: 'Failed to get field ROI' });
  }
};

/**
 * Get ROI data for equipment
 */
export const getEquipmentROI = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { year = new Date().getFullYear() } = req.query;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get equipment for the farm
    const equipment = await Equipment.findAll({
      where: {
        farm_id: farmId
      },
      include: [
        {
          model: Transaction,
          as: 'transactions',
          required: false,
          where: {
            date: {
              [Op.between]: [
                new Date(`${year}-01-01`),
                new Date(`${year}-12-31`)
              ]
            }
          }
        }
      ]
    });

    // Calculate ROI for each equipment
    const equipmentROIData = equipment.map(equip => {
      // For equipment, we don't have direct revenue, but we have expenses
      const expenses = equip.transactions
        ? equip.transactions
            .filter(t => t.amount < 0)
            .reduce((sum, t) => sum + Math.abs(t.amount), 0)
        : 0;

      // Calculate investment (purchase price)
      const investment = equip.purchase_price || 0;

      // For equipment, profit is estimated based on productivity gains
      // This is a simplified calculation - in a real app, this would be more complex
      const estimatedProductivityGain = investment * 0.3; // Assume 30% productivity gain on investment

      // Calculate profit (estimated)
      const profit = estimatedProductivityGain - expenses;

      // Calculate ROI
      const roi = investment > 0 ? (profit / investment) * 100 : 0;

      // Calculate payback period (in years)
      const paybackPeriod = profit > 0 ? investment / profit : 0;

      return {
        id: equip.id,
        name: `${equip.make} ${equip.model}`,
        type: 'equipment',
        investment,
        revenue: 0, // Equipment doesn't directly generate revenue
        expenses,
        profit,
        roi,
        paybackPeriod,
        year: parseInt(year)
      };
    });

    return res.status(200).json({
      roiData: equipmentROIData
    });
  } catch (error) {
    console.error('Error getting equipment ROI:', error);
    return res.status(500).json({ error: 'Failed to get equipment ROI' });
  }
};

/**
 * Get ROI history data
 */
export const getROIHistory = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { startYear = new Date().getFullYear() - 5, endYear = new Date().getFullYear() } = req.query;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get transactions for the farm for the specified years
    const transactions = await Transaction.findAll({
      where: {
        farm_id: farmId,
        transaction_date: {
          [Op.between]: [
            new Date(`${startYear}-01-01`),
            new Date(`${endYear}-12-31`)
          ]
        }
      }
    });

    // Group transactions by year
    const transactionsByYear = {};

    for (let year = parseInt(startYear); year <= parseInt(endYear); year++) {
      transactionsByYear[year] = {
        revenue: 0,
        expenses: 0
      };
    }

    // Calculate revenue and expenses for each year
    transactions.forEach(transaction => {
      const year = new Date(transaction.transaction_date).getFullYear();

      if (transaction.amount > 0) {
        transactionsByYear[year].revenue += transaction.amount;
      } else {
        transactionsByYear[year].expenses += Math.abs(transaction.amount);
      }
    });

    // Calculate ROI for each year
    const roiHistory = Object.keys(transactionsByYear).map(year => {
      const { revenue, expenses } = transactionsByYear[year];
      const profit = revenue - expenses;

      // For simplicity, we'll use the farm's total assets as investment
      // In a real app, this would be calculated more precisely
      const investment = farm.total_assets || 1000000; // Default to 1M if not available

      const roi = investment > 0 ? (profit / investment) * 100 : 0;

      return {
        year: parseInt(year),
        roi
      };
    });

    return res.status(200).json({
      roiHistory
    });
  } catch (error) {
    console.error('Error getting ROI history:', error);
    return res.status(500).json({ error: 'Failed to get ROI history' });
  }
};

/**
 * Get combined ROI data for all types
 */
export const getCombinedROI = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { year = new Date().getFullYear() } = req.query;

    // Get ROI data for crops
    const cropResponse = await getCropROI({ params: { farmId }, query: { year } }, { 
      status: () => ({ json: data => data }),
      json: data => data
    });

    // Get ROI data for fields
    const fieldResponse = await getFieldROI({ params: { farmId }, query: { year } }, {
      status: () => ({ json: data => data }),
      json: data => data
    });

    // Get ROI data for equipment
    const equipmentResponse = await getEquipmentROI({ params: { farmId }, query: { year } }, {
      status: () => ({ json: data => data }),
      json: data => data
    });

    // Combine all ROI data
    const combinedROIData = [
      ...(cropResponse.roiData || []),
      ...(fieldResponse.roiData || []),
      ...(equipmentResponse.roiData || [])
    ];

    return res.status(200).json({
      roiData: combinedROIData
    });
  } catch (error) {
    console.error('Error getting combined ROI:', error);
    return res.status(500).json({ error: 'Failed to get combined ROI' });
  }
};
