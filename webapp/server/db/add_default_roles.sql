-- Add default roles to the database based on current usage
-- This script adds system-wide default role permissions (farm_id is null)

-- Set the search path to the appropriate schema
-- This will be replaced with the actual schema name by the script
SET search_path TO site;

-- Delete existing default role permissions to avoid duplicates
DELETE FROM role_permissions WHERE farm_id IS NULL;

-- Farm Owner role permissions
-- Farm owners have full access to everything
INSERT INTO role_permissions (id, farm_id, role_name, feature, can_view, can_create, can_edit, can_delete, created_at, updated_at)
VALUES 
  (uuid_generate_v4(), NULL, 'farm_owner', 'farms', TRUE, TRUE, TRUE, TRUE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_owner', 'fields', TRUE, TRUE, TRUE, TRUE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_owner', 'crops', TRUE, TRUE, TRUE, TRUE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_owner', 'livestock', TRUE, TRUE, TRUE, TRUE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_owner', 'equipment', TRUE, TRUE, TRUE, TRUE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_owner', 'inventory', TRUE, TRUE, TRUE, TRUE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_owner', 'employees', TRUE, TRUE, TRUE, TRUE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_owner', 'finances', TRUE, TRUE, TRUE, TRUE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_owner', 'reports', TRUE, TRUE, TRUE, TRUE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_owner', 'settings', TRUE, TRUE, TRUE, TRUE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_owner', 'roles', TRUE, TRUE, TRUE, TRUE, NOW(), NOW());

-- Farm Admin role permissions
-- Farm admins have full access to most things, but can't delete farms or change settings
INSERT INTO role_permissions (id, farm_id, role_name, feature, can_view, can_create, can_edit, can_delete, created_at, updated_at)
VALUES 
  (uuid_generate_v4(), NULL, 'farm_admin', 'farms', TRUE, FALSE, TRUE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_admin', 'fields', TRUE, TRUE, TRUE, TRUE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_admin', 'crops', TRUE, TRUE, TRUE, TRUE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_admin', 'livestock', TRUE, TRUE, TRUE, TRUE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_admin', 'equipment', TRUE, TRUE, TRUE, TRUE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_admin', 'inventory', TRUE, TRUE, TRUE, TRUE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_admin', 'employees', TRUE, TRUE, TRUE, TRUE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_admin', 'finances', TRUE, TRUE, TRUE, TRUE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_admin', 'reports', TRUE, TRUE, TRUE, TRUE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_admin', 'settings', TRUE, FALSE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_admin', 'roles', TRUE, FALSE, FALSE, FALSE, NOW(), NOW());

-- Farm Manager role permissions
-- Farm managers can view and edit most things, but can't delete or create new farms
INSERT INTO role_permissions (id, farm_id, role_name, feature, can_view, can_create, can_edit, can_delete, created_at, updated_at)
VALUES 
  (uuid_generate_v4(), NULL, 'farm_manager', 'farms', TRUE, FALSE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_manager', 'fields', TRUE, TRUE, TRUE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_manager', 'crops', TRUE, TRUE, TRUE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_manager', 'livestock', TRUE, TRUE, TRUE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_manager', 'equipment', TRUE, TRUE, TRUE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_manager', 'inventory', TRUE, TRUE, TRUE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_manager', 'employees', TRUE, FALSE, TRUE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_manager', 'finances', TRUE, TRUE, TRUE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_manager', 'reports', TRUE, TRUE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_manager', 'settings', FALSE, FALSE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_manager', 'roles', FALSE, FALSE, FALSE, FALSE, NOW(), NOW());

-- Farm Employee role permissions
-- Farm employees can view most things, but can only edit their own data
INSERT INTO role_permissions (id, farm_id, role_name, feature, can_view, can_create, can_edit, can_delete, created_at, updated_at)
VALUES 
  (uuid_generate_v4(), NULL, 'farm_employee', 'farms', TRUE, FALSE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_employee', 'fields', TRUE, FALSE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_employee', 'crops', TRUE, FALSE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_employee', 'livestock', TRUE, FALSE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_employee', 'equipment', TRUE, FALSE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_employee', 'inventory', TRUE, FALSE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_employee', 'employees', FALSE, FALSE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_employee', 'finances', FALSE, FALSE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_employee', 'reports', FALSE, FALSE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_employee', 'settings', FALSE, FALSE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'farm_employee', 'roles', FALSE, FALSE, FALSE, FALSE, NOW(), NOW());

-- Accountant role permissions
-- Accountants can view and edit financial data
INSERT INTO role_permissions (id, farm_id, role_name, feature, can_view, can_create, can_edit, can_delete, created_at, updated_at)
VALUES 
  (uuid_generate_v4(), NULL, 'accountant', 'farms', TRUE, FALSE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'accountant', 'fields', FALSE, FALSE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'accountant', 'crops', FALSE, FALSE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'accountant', 'livestock', FALSE, FALSE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'accountant', 'equipment', FALSE, FALSE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'accountant', 'inventory', TRUE, FALSE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'accountant', 'employees', TRUE, FALSE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'accountant', 'finances', TRUE, TRUE, TRUE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'accountant', 'reports', TRUE, TRUE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'accountant', 'settings', FALSE, FALSE, FALSE, FALSE, NOW(), NOW()),
  (uuid_generate_v4(), NULL, 'accountant', 'roles', FALSE, FALSE, FALSE, FALSE, NOW(), NOW());
