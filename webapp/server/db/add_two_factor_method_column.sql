-- Add two_factor_method column to users table
-- Set the search path to the site schema
SET search_path TO site;

-- First, create the ENUM type if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'two_factor_method_enum') THEN
        CREATE TYPE two_factor_method_enum AS ENUM ('app', 'sms');
    END IF;
END$$;

-- Check if the column exists with a different type
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_schema = 'site'
               AND table_name = 'users' 
               AND column_name = 'two_factor_method' 
               AND data_type <> 'USER-DEFINED') THEN
        
        -- Drop the existing column
        ALTER TABLE site.users DROP COLUMN two_factor_method;
    END IF;
END$$;

-- Then add the column with the ENUM type
ALTER TABLE site.users 
ADD COLUMN IF NOT EXISTS two_factor_method two_factor_method_enum;

-- Add a comment to the column
COMMENT ON COLUMN site.users.two_factor_method IS 'Preferred 2FA method: authenticator app or SMS';