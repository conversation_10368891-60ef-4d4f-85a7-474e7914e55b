-- Add is_billing_contact column to user_farms table

-- Set the search path to the site schema
SET search_path TO site;

-- Add is_billing_contact column if it doesn't exist
ALTER TABLE site.user_farms ADD COLUMN IF NOT EXISTS is_billing_contact BOOLEAN DEFAULT FALSE;
COMMENT ON COLUMN site.user_farms.is_billing_contact IS 'Whether this user is responsible for billing for this farm';

-- Verify the column was added
DO $$
DECLARE
    column_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'site' 
        AND table_name = 'user_farms' 
        AND column_name = 'is_billing_contact'
    ) INTO column_exists;
    
    IF column_exists THEN
        RAISE NOTICE 'The is_billing_contact column was successfully added to the user_farms table.';
    ELSE
        RAISE EXCEPTION 'Failed to add the is_billing_contact column to the user_farms table.';
    END IF;
END $$;

-- Create index for better query performance if it doesn't exist
CREATE INDEX IF NOT EXISTS idx_user_farms_is_billing_contact ON site.user_farms(is_billing_contact);