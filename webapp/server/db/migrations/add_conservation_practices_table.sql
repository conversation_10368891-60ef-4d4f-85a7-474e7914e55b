-- Migration to add conservation_practices table
-- This table stores conservation practices for fields

-- Set the search path to the appropriate schema
SET search_path TO site;

CREATE TABLE IF NOT EXISTS site.conservation_practices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID NOT NULL REFERENCES site.farms(id) ON DELETE CASCADE,
    field_id UUID NOT NULL REFERENCES site.fields(id) ON DELETE CASCADE,
    practice_name VARCHAR(100) NOT NULL,
    practice_type VARCHAR(50),
    description TEXT,
    implementation_date DATE,
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    benefits JSONB,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE site.conservation_practices IS 'Stores conservation practices for fields';
COMMENT ON COLUMN site.conservation_practices.id IS 'Unique identifier for the conservation practice';
COMMENT ON COLUMN site.conservation_practices.farm_id IS 'ID of the farm this conservation practice belongs to';
COMMENT ON COLUMN site.conservation_practices.field_id IS 'ID of the field this conservation practice is for';
COMMENT ON COLUMN site.conservation_practices.practice_name IS 'Name of the conservation practice';
COMMENT ON COLUMN site.conservation_practices.practice_type IS 'Type of conservation practice (e.g., soil, water, habitat)';
COMMENT ON COLUMN site.conservation_practices.description IS 'Description of the conservation practice';
COMMENT ON COLUMN site.conservation_practices.implementation_date IS 'Date when the practice was implemented';
COMMENT ON COLUMN site.conservation_practices.status IS 'Status of the practice (active, planned, completed)';
COMMENT ON COLUMN site.conservation_practices.benefits IS 'Benefits of the conservation practice';

-- Step 2: Create indexes for better performance

CREATE INDEX IF NOT EXISTS idx_conservation_practices_farm_id ON site.conservation_practices(farm_id);
CREATE INDEX IF NOT EXISTS idx_conservation_practices_field_id ON site.conservation_practices(field_id);
CREATE INDEX IF NOT EXISTS idx_conservation_practices_practice_name ON site.conservation_practices(practice_name);
CREATE INDEX IF NOT EXISTS idx_conservation_practices_status ON site.conservation_practices(status);

-- Step 3: Create trigger for updated_at timestamp

CREATE TRIGGER update_conservation_practices_timestamp
BEFORE UPDATE ON site.conservation_practices
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

