# Grants Implementation

This document describes the implementation of the grants feature in the NxtAcre Farm Management Platform.

## Overview

The grants feature consists of two main components:

1. **External Grants**: Grants fetched from external sources like grants.gov, USDA, etc. These are stored in the `grants` table.
2. **Farm-Specific Grants**: Grants that are specific to a farm, stored in the `farm_grants` table.

## Database Tables

### Grants Table

The `grants` table stores grants fetched from external sources. It has the following structure:

```sql
CREATE TABLE IF NOT EXISTS site.grants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    external_id VARCHAR(255) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    agency VARCHAR(255),
    opportunity_number VARCHAR(255),
    category VARCHAR(255),
    eligibility TEXT,
    funding_amount VARCHAR(255),
    close_date TIMESTAMP,
    url VARCHAR(255),
    source VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Farm Grants Table

The `farm_grants` table stores farm-specific grants. It has the following structure:

```sql
CREATE TABLE IF NOT EXISTS site.farm_grants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    provider VARCHAR(255) NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    status VARCHAR(50) DEFAULT 'applied' NOT NULL,
    deadline TIMESTAMP NOT NULL,
    description TEXT,
    requirements JSONB DEFAULT '[]',
    application_date TIMESTAMP,
    approval_date TIMESTAMP,
    disbursement_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (farm_id) REFERENCES site.farms(id) ON DELETE CASCADE
);
```

## Models

### Grant Model

The `Grant` model represents grants from external sources. It maps to the `grants` table.

### FarmGrant Model

The `FarmGrant` model represents farm-specific grants. It maps to the `farm_grants` table.

## API Routes

### External Grants

- `GET /api/grants`: Get all grants from the database
- `GET /api/grants/grants-gov`: Get grants from grants.gov
- `GET /api/grants/usda`: Get grants from USDA
- `GET /api/grants/farmers-gov`: Get grants from farmers.gov
- `GET /api/grants/fsa`: Get grants from Farm Service Agency
- `GET /api/grants/rural-development`: Get grants from USDA Rural Development
- `GET /api/grants/nrcs`: Get grants from Natural Resources Conservation Service
- `GET /api/grants/nifa`: Get grants from National Institute of Food and Agriculture
- `GET /api/grants/rma`: Get grants from Risk Management Agency
- `GET /api/grants/ams`: Get grants from Agricultural Marketing Service
- `GET /api/grants/data-gov`: Get grants from data.gov
- `GET /api/grants/search`: Search for grants
- `GET /api/grants/:source/:id`: Get grant details by ID and source

### Farm-Specific Grants

- `GET /api/farms/:farmId/grants`: Get grants for a farm
- `GET /api/grants/:grantId`: Get a specific grant
- `POST /api/farms/:farmId/grants`: Create a new grant
- `PUT /api/grants/:grantId`: Update a grant
- `DELETE /api/grants/:grantId`: Delete a grant

## Scripts

### Run Farm Grants Migration

This script creates the `farm_grants` table in the database.

```bash
npm run migrate:farm-grants
```

### Fetch and Store Grants

This script fetches grants from external sources and stores them in the `grants` table.

```bash
npm run fetch:grants
```

## Frontend

The frontend displays grants fetched from the database. If there are no grants in the database, it falls back to fetching from external sources.

## Implementation Steps

1. Create the `grants` table using the `add_grants_table.sql` migration
2. Create the `farm_grants` table using the `add_farm_grants_table.sql` migration
3. Create the `Grant` model to map to the `grants` table
4. Create the `FarmGrant` model to map to the `farm_grants` table
5. Update the `grantSubsidyController.js` to use the `FarmGrant` model
6. Update the `grantsRoutes.js` to add a route for fetching all grants from the database
7. Update the `grantsService.ts` to fetch grants from the database
8. Create scripts for running migrations and fetching grants

## Usage

1. Run the farm grants migration:
   ```bash
   npm run migrate:farm-grants
   ```

2. Fetch and store grants:
   ```bash
   npm run fetch:grants
   ```

3. Start the application:
   ```bash
   npm run start
   ```

4. Navigate to the grants page to view the grants