-- Migration: Fix association definitions in models
-- This migration documents the code changes needed to fix duplicate associations in models

SET search_path TO site;

-- Create a function to log migration progress
CREATE OR REPLACE FUNCTION log_migration_step(step_name TEXT)
RETURNS VOID AS $$
BEGIN
  RAISE NOTICE 'Migration step: %', step_name;
  RETURN;
END;
$$ LANGUAGE plpgsql;

-- Log the start of the migration
SELECT log_migration_step('Starting fix_association_definitions migration');

-- This migration doesn't make any schema changes, it only documents code-level issues
-- with association definitions in models.

-- The issue is that some models have associations defined both in the individual model files
-- and in the central associations.js file, which can lead to duplicate associations at runtime.

-- Other models have associations defined only in the individual model files, which is inconsistent
-- with the project's pattern of centralizing associations in associations.js.

-- The recommended approach is to:
-- 1. Move all associations from individual model files to associations.js
-- 2. Remove duplicate associations from individual model files
-- 3. Add comments in individual model files indicating that associations are defined in associations.js

-- Record the migration in the database_migrations table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'database_migrations') THEN
    INSERT INTO site.database_migrations (id, name, file_path, "order", applied_at, created_at, updated_at, status, dependencies)
    VALUES (
      uuid_generate_v4(),
      'Fix association definitions in models',
      'webapp/server/db/migrations/fix_association_definitions.sql',
      (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
      NOW(),
      NOW(),
      NOW(),
      'completed',
      NULL
    );
    
    SELECT log_migration_step('Migration recorded in database_migrations table');
  ELSE
    SELECT log_migration_step('database_migrations table does not exist, skipping recording');
  END IF;
END $$;

-- Log the completion of the migration
SELECT log_migration_step('Completed fix_association_definitions migration');

-- Drop the logging function
DROP FUNCTION IF EXISTS log_migration_step;