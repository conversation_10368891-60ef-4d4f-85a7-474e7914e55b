-- Create drivers table for transport management

-- Set the search path to the site schema
SET search_path TO site;

-- Create drivers table if it doesn't exist
CREATE TABLE IF NOT EXISTS site.drivers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID NOT NULL REFERENCES site.farms(id),
  user_id UUID REFERENCES site.users(id),
  first_name VA<PERSON>HAR(100) NOT NULL,
  last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
  email VARCHAR(255),
  phone_number VARCHAR(20),
  license_number VARCHAR(50),
  license_expiry TIMESTAMP WITH TIME ZONE,
  vehicle_type VARCHAR(100),
  vehicle_plate VARCHAR(20),
  status VARCHAR(20) NOT NULL DEFAULT 'active',
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add enum constraint for status
ALTER TABLE site.drivers DROP CONSTRAINT IF EXISTS drivers_status_check;
ALTER TABLE site.drivers ADD CONSTRAINT drivers_status_check 
  CHECK (status IN ('active', 'inactive', 'on_leave'));

-- Add comments to columns
COMMENT ON COLUMN site.drivers.id IS 'Unique identifier for the driver';
COMMENT ON COLUMN site.drivers.farm_id IS 'ID of the farm this driver belongs to';
COMMENT ON COLUMN site.drivers.user_id IS 'ID of the user account if the driver is a user in the system';
COMMENT ON COLUMN site.drivers.first_name IS 'First name of the driver';
COMMENT ON COLUMN site.drivers.last_name IS 'Last name of the driver';
COMMENT ON COLUMN site.drivers.email IS 'Email address of the driver';
COMMENT ON COLUMN site.drivers.phone_number IS 'Phone number of the driver';
COMMENT ON COLUMN site.drivers.license_number IS 'Driver license number';
COMMENT ON COLUMN site.drivers.license_expiry IS 'Expiration date of the driver license';
COMMENT ON COLUMN site.drivers.vehicle_type IS 'Type of vehicle the driver operates';
COMMENT ON COLUMN site.drivers.vehicle_plate IS 'License plate of the vehicle';
COMMENT ON COLUMN site.drivers.status IS 'Status of the driver: active, inactive, or on_leave';
COMMENT ON COLUMN site.drivers.notes IS 'Additional notes about the driver';
COMMENT ON COLUMN site.drivers.created_at IS 'Timestamp when the driver record was created';
COMMENT ON COLUMN site.drivers.updated_at IS 'Timestamp when the driver record was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS drivers_farm_id_idx ON site.drivers(farm_id);
CREATE INDEX IF NOT EXISTS drivers_user_id_idx ON site.drivers(user_id);
CREATE INDEX IF NOT EXISTS drivers_status_idx ON site.drivers(status);
CREATE INDEX IF NOT EXISTS drivers_name_idx ON site.drivers(last_name, first_name);

-- Verify the table was created
DO $$
DECLARE
    table_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_schema = 'site' 
        AND table_name = 'drivers'
    ) INTO table_exists;

    IF table_exists THEN
        RAISE NOTICE 'The drivers table was successfully created.';
    ELSE
        RAISE EXCEPTION 'Failed to create the drivers table.';
    END IF;
END $$;

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_drivers_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_drivers_timestamp
BEFORE UPDATE ON site.drivers
FOR EACH ROW EXECUTE FUNCTION update_drivers_updated_at();