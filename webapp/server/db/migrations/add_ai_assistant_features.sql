-- Add tables for AI Assistant Decision Support System and Predictive Maintenance
-- Set the search path to the appropriate schema
SET search_path TO site;

-- Create table for AI decision support
CREATE TABLE IF NOT EXISTS site.ai_decision_support (
  id SERIAL PRIMARY KEY,
  farm_id UUID NOT NULL,
  operation_type VARCHAR(100) NOT NULL,
  recommendation TEXT NOT NULL,
  confidence_score DECIMAL(5,2) NOT NULL,
  factors_considered TEXT NOT NULL,
  is_implemented BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (farm_id) REFERENCES site.farms(id) ON DELETE CASCADE
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_ai_decision_support_farm_id ON site.ai_decision_support(farm_id);
CREATE INDEX IF NOT EXISTS idx_ai_decision_support_operation_type ON site.ai_decision_support(operation_type);
CREATE INDEX IF NOT EXISTS idx_ai_decision_support_created_at ON site.ai_decision_support(created_at);

-- Create table for AI predictive maintenance
CREATE TABLE IF NOT EXISTS site.ai_predictive_maintenance (
  id SERIAL PRIMARY KEY,
  farm_id UUID NOT NULL,
  equipment_id UUID NOT NULL,
  maintenance_type VARCHAR(100) NOT NULL,
  prediction TEXT NOT NULL,
  urgency_level VARCHAR(20) NOT NULL,
  predicted_failure_date DATE,
  confidence_score DECIMAL(5,2) NOT NULL,
  is_addressed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (farm_id) REFERENCES site.farms(id) ON DELETE CASCADE,
  FOREIGN KEY (equipment_id) REFERENCES site.equipment(id) ON DELETE CASCADE
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_ai_predictive_maintenance_farm_id ON site.ai_predictive_maintenance(farm_id);
CREATE INDEX IF NOT EXISTS idx_ai_predictive_maintenance_equipment_id ON site.ai_predictive_maintenance(equipment_id);
CREATE INDEX IF NOT EXISTS idx_ai_predictive_maintenance_urgency_level ON site.ai_predictive_maintenance(urgency_level);
CREATE INDEX IF NOT EXISTS idx_ai_predictive_maintenance_predicted_failure_date ON site.ai_predictive_maintenance(predicted_failure_date);