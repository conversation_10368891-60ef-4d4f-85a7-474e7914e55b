# Users Column Types Fix

## Overview

This migration fixes data type inconsistencies in the `users` table. The issue was that several columns were defined with incorrect data types in the database schema, which did not match the model definition.

## Issues Fixed

1. `created_at` and `updated_at` columns were defined as `varchar` in the database schema, but as `DataTypes.DATE` in the model definition.
2. `reset_password_expires` column was defined as `varchar` in the database schema, but as `DataTypes.DATE` in the model definition.
3. `email_2fa_expires` column was defined as `varchar` in the database schema, but as `DataTypes.DATE` in the model definition.

These inconsistencies could lead to issues with data storage and retrieval, as the model expects dates but the database stores strings.

## Migration Details

The migration performs the following actions:

1. Creates a backup of the `users` table
2. Alters the `created_at` and `updated_at` columns to be `timestamp with time zone`
3. Alters the `reset_password_expires` column to be `timestamp with time zone`
4. Alters the `email_2fa_expires` column to be `timestamp with time zone`
5. Records the migration in the `database_migrations` table if it exists

## How to Apply the Migration

To apply this migration, run the following command:

```bash
psql -U your_username -d your_database -f server/db/migrations/fix_users_column_types.sql
```

Or you can use the database migration script:

```bash
node server/scripts/run-migration.js fix_users_column_types.sql
```

## Dependencies

This migration depends on:
- `fix_user_farms_column_types.sql` - The migration that fixes column types in the `user_farms` table

## Verification

After running the migration, you can verify that the changes were applied correctly by:

1. Checking the column types in the database:

```sql
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'users' 
AND column_name IN ('created_at', 'updated_at', 'reset_password_expires', 'email_2fa_expires');
```

2. Verifying that the application still works correctly with the `users` table.

## Related Files

- `webapp/server/models/User.js` - The model definition that expects `created_at`, `updated_at`, `reset_password_expires`, and `email_2fa_expires` to be dates.
- `webapp/server/db/migrations/fix_users_column_types.sql` - The migration file that fixes the data type inconsistencies.