-- Migration to add tables for storing NCEI climate data

-- Table for storing historical temperature data
CREATE TABLE IF NOT EXISTS site.historical_temperature (
    id SERIAL PRIMARY KEY,
    farm_id UUID REFERENCES site.farms(id) ON DELETE CASCADE,
    field_id UUID REFERENCES site.fields(id) ON DELETE CASCADE,
    station_id VARCHAR(255) NOT NULL,
    station_name VARCHAR(255),
    latitude NUMERIC(10, 6) NOT NULL,
    longitude NUMERIC(10, 6) NOT NULL,
    date DATE NOT NULL,
    max_temp NUMERIC(5, 2),
    min_temp NUMERIC(5, 2),
    avg_temp NUMERIC(5, 2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(station_id, date)
);

-- Table for storing historical precipitation data
CREATE TABLE IF NOT EXISTS site.historical_precipitation (
    id SERIAL PRIMARY KEY,
    farm_id UUID REFERENCES site.farms(id) ON DELETE CASCADE,
    field_id UUID REFERENCES site.fields(id) ON DELETE CASCADE,
    station_id VARCHAR(255) NOT NULL,
    station_name VARCHAR(255),
    latitude NUMERIC(10, 6) NOT NULL,
    longitude NUMERIC(10, 6) NOT NULL,
    date DATE NOT NULL,
    precipitation NUMERIC(6, 3), -- in inches
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(station_id, date)
);

-- Table for storing climate normals
CREATE TABLE IF NOT EXISTS site.climate_normals (
    id SERIAL PRIMARY KEY,
    station_id VARCHAR(255) NOT NULL,
    station_name VARCHAR(255),
    latitude NUMERIC(10, 6) NOT NULL,
    longitude NUMERIC(10, 6) NOT NULL,
    month INTEGER NOT NULL CHECK (month BETWEEN 1 AND 12),
    day INTEGER NOT NULL CHECK (day BETWEEN 1 AND 31),
    max_temp NUMERIC(5, 2),
    min_temp NUMERIC(5, 2),
    avg_temp NUMERIC(5, 2),
    precipitation NUMERIC(6, 3), -- in inches
    heating_degree_days NUMERIC(6, 2),
    cooling_degree_days NUMERIC(6, 2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(station_id, month, day)
);

-- Table for storing monthly climate normals (averages)
CREATE TABLE IF NOT EXISTS site.monthly_climate_normals (
    id SERIAL PRIMARY KEY,
    station_id VARCHAR(255) NOT NULL,
    station_name VARCHAR(255),
    latitude NUMERIC(10, 6) NOT NULL,
    longitude NUMERIC(10, 6) NOT NULL,
    month INTEGER NOT NULL CHECK (month BETWEEN 1 AND 12),
    max_temp NUMERIC(5, 2),
    min_temp NUMERIC(5, 2),
    avg_temp NUMERIC(5, 2),
    total_precipitation NUMERIC(6, 3), -- in inches
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(station_id, month)
);

-- Table for storing growing degree days
CREATE TABLE IF NOT EXISTS site.growing_degree_days (
    id SERIAL PRIMARY KEY,
    farm_id UUID REFERENCES site.farms(id) ON DELETE CASCADE,
    field_id UUID REFERENCES site.fields(id) ON DELETE CASCADE,
    station_id VARCHAR(255) NOT NULL,
    station_name VARCHAR(255),
    latitude NUMERIC(10, 6) NOT NULL,
    longitude NUMERIC(10, 6) NOT NULL,
    date DATE NOT NULL,
    base_temperature INTEGER NOT NULL DEFAULT 50, -- in Fahrenheit
    gdd NUMERIC(6, 2) NOT NULL,
    cumulative_gdd NUMERIC(8, 2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(station_id, date, base_temperature)
);

-- Table for storing frost/freeze dates
CREATE TABLE IF NOT EXISTS site.frost_freeze_dates (
    id SERIAL PRIMARY KEY,
    farm_id UUID REFERENCES site.farms(id) ON DELETE CASCADE,
    field_id UUID REFERENCES site.fields(id) ON DELETE CASCADE,
    station_id VARCHAR(255) NOT NULL,
    station_name VARCHAR(255),
    latitude NUMERIC(10, 6) NOT NULL,
    longitude NUMERIC(10, 6) NOT NULL,
    year INTEGER NOT NULL,
    last_spring_frost DATE,
    first_fall_frost DATE,
    growing_season_length INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(station_id, year)
);

-- Table for storing extreme weather events
CREATE TABLE IF NOT EXISTS site.extreme_weather_events (
    id SERIAL PRIMARY KEY,
    event_id VARCHAR(255) NOT NULL,
    event_type VARCHAR(255) NOT NULL,
    state VARCHAR(255),
    county VARCHAR(255),
    begin_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    injuries INTEGER,
    deaths INTEGER,
    property_damage NUMERIC(12, 2),
    crop_damage NUMERIC(12, 2),
    event_narrative TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(event_id)
);

-- Table for tracking which farms/fields are affected by extreme weather events
CREATE TABLE IF NOT EXISTS site.farm_weather_events (
    id SERIAL PRIMARY KEY,
    farm_id UUID REFERENCES site.farms(id) ON DELETE CASCADE,
    field_id UUID REFERENCES site.fields(id) ON DELETE CASCADE,
    event_id VARCHAR(255) REFERENCES site.extreme_weather_events(event_id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(farm_id, field_id, event_id)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_historical_temperature_farm_id ON site.historical_temperature(farm_id);
CREATE INDEX IF NOT EXISTS idx_historical_temperature_field_id ON site.historical_temperature(field_id);
CREATE INDEX IF NOT EXISTS idx_historical_temperature_date ON site.historical_temperature(date);

CREATE INDEX IF NOT EXISTS idx_historical_precipitation_farm_id ON site.historical_precipitation(farm_id);
CREATE INDEX IF NOT EXISTS idx_historical_precipitation_field_id ON site.historical_precipitation(field_id);
CREATE INDEX IF NOT EXISTS idx_historical_precipitation_date ON site.historical_precipitation(date);

CREATE INDEX IF NOT EXISTS idx_climate_normals_station_id ON site.climate_normals(station_id);
CREATE INDEX IF NOT EXISTS idx_climate_normals_month_day ON site.climate_normals(month, day);

CREATE INDEX IF NOT EXISTS idx_monthly_climate_normals_station_id ON site.monthly_climate_normals(station_id);
CREATE INDEX IF NOT EXISTS idx_monthly_climate_normals_month ON site.monthly_climate_normals(month);

CREATE INDEX IF NOT EXISTS idx_growing_degree_days_farm_id ON site.growing_degree_days(farm_id);
CREATE INDEX IF NOT EXISTS idx_growing_degree_days_field_id ON site.growing_degree_days(field_id);
CREATE INDEX IF NOT EXISTS idx_growing_degree_days_date ON site.growing_degree_days(date);

CREATE INDEX IF NOT EXISTS idx_frost_freeze_dates_farm_id ON site.frost_freeze_dates(farm_id);
CREATE INDEX IF NOT EXISTS idx_frost_freeze_dates_field_id ON site.frost_freeze_dates(field_id);
CREATE INDEX IF NOT EXISTS idx_frost_freeze_dates_year ON site.frost_freeze_dates(year);

CREATE INDEX IF NOT EXISTS idx_extreme_weather_events_event_type ON site.extreme_weather_events(event_type);
CREATE INDEX IF NOT EXISTS idx_extreme_weather_events_county ON site.extreme_weather_events(county);
CREATE INDEX IF NOT EXISTS idx_extreme_weather_events_begin_date ON site.extreme_weather_events(begin_date);

CREATE INDEX IF NOT EXISTS idx_farm_weather_events_farm_id ON site.farm_weather_events(farm_id);
CREATE INDEX IF NOT EXISTS idx_farm_weather_events_field_id ON site.farm_weather_events(field_id);
CREATE INDEX IF NOT EXISTS idx_farm_weather_events_event_id ON site.farm_weather_events(event_id);
