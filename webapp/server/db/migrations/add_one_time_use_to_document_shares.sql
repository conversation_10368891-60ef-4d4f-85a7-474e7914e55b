-- Migration: Add one_time_use and used fields to document_shares table
-- Depends on:

SET search_path TO site;

-- Add one_time_use and used columns to document_shares table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'site'
        AND table_name = 'document_shares'
        AND column_name = 'one_time_use'
    ) THEN
        ALTER TABLE site.document_shares
        ADD COLUMN one_time_use BOOLEAN NOT NULL DEFAULT FALSE;
        
        COMMENT ON COLUMN site.document_shares.one_time_use IS 'Whether the share link can only be used once';
        
        RAISE NOTICE 'Successfully added one_time_use column to document_shares table';
    ELSE
        RAISE NOTICE 'one_time_use column already exists in document_shares table';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'site'
        AND table_name = 'document_shares'
        AND column_name = 'used'
    ) THEN
        ALTER TABLE site.document_shares
        ADD COLUMN used BOOLEAN NOT NULL DEFAULT FALSE;
        
        COMMENT ON COLUMN site.document_shares.used IS 'Whether a one-time share link has been used';
        
        RAISE NOTICE 'Successfully added used column to document_shares table';
    ELSE
        RAISE NOTICE 'used column already exists in document_shares table';
    END IF;
END $$;

-- Record the migration in the database_migrations table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'database_migrations') THEN
        INSERT INTO site.database_migrations (id, name, file_path, "order", applied_at, created_at, updated_at, status, dependencies)
        VALUES (
            gen_random_uuid(),
            'Add one_time_use and used fields to document_shares table',
            'webapp/server/db/migrations/add_one_time_use_to_document_shares.sql',
            (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
            NOW(),
            NOW(),
            NOW(),
            'applied',
            ARRAY['add_document_shares_table.sql']
        );
    END IF;
END $$;