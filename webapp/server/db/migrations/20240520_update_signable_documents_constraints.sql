-- Migration: Update signable_documents constraints to use farm_id and user_id instead of tenant_id
-- Depends on:

-- Set the search path to the site schema
SET search_path TO site;

-- Begin transaction
BEGIN;

-- Log migration step
DO $$
BEGIN
    RAISE NOTICE 'Updating signable_documents constraints';
END $$;

-- Make tenant_id nullable
ALTER TABLE signable_documents ALTER COLUMN tenant_id DROP NOT NULL;

-- Make farm_id NOT NULL
-- First, check if there are any records with NULL farm_id
DO $$
DECLARE
    null_farm_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO null_farm_count FROM signable_documents WHERE farm_id IS NULL;

    IF null_farm_count > 0 THEN
        RAISE EXCEPTION 'Cannot make farm_id NOT NULL because there are % records with NULL farm_id', null_farm_count;
    END IF;
END $$;

-- If no exceptions were raised, make farm_id NOT NULL
ALTER TABLE signable_documents ALTER COLUMN farm_id SET NOT NULL;

-- Make created_by NOT NULL
-- First, check if there are any records with NULL created_by
DO $$
DECLARE
    null_user_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO null_user_count FROM signable_documents WHERE created_by IS NULL;

    IF null_user_count > 0 THEN
        RAISE EXCEPTION 'Cannot make created_by NOT NULL because there are % records with NULL created_by', null_user_count;
    END IF;
END $$;

-- If no exceptions were raised, make created_by NOT NULL
ALTER TABLE signable_documents ALTER COLUMN created_by SET NOT NULL;

-- Update comments to reflect the changes
COMMENT ON COLUMN site.signable_documents.tenant_id IS 'ID of the tenant this document belongs to (deprecated, use farm_id and created_by instead)';
COMMENT ON COLUMN site.signable_documents.farm_id IS 'ID of the farm this document is associated with';
COMMENT ON COLUMN site.signable_documents.created_by IS 'ID of the user who created the document';

-- Commit transaction
COMMIT;

-- Verify changes
DO $$
DECLARE
    tenant_id_nullable BOOLEAN;
    farm_id_not_null BOOLEAN;
    created_by_not_null BOOLEAN;
BEGIN
    SELECT is_nullable = 'YES' INTO tenant_id_nullable
    FROM information_schema.columns
    WHERE table_schema = 'site' AND table_name = 'signable_documents' AND column_name = 'tenant_id';

    SELECT is_nullable = 'NO' INTO farm_id_not_null
    FROM information_schema.columns
    WHERE table_schema = 'site' AND table_name = 'signable_documents' AND column_name = 'farm_id';

    SELECT is_nullable = 'NO' INTO created_by_not_null
    FROM information_schema.columns
    WHERE table_schema = 'site' AND table_name = 'signable_documents' AND column_name = 'created_by';

    IF NOT tenant_id_nullable THEN
        RAISE EXCEPTION 'Migration failed: tenant_id is still NOT NULL';
    END IF;

    IF NOT farm_id_not_null THEN
        RAISE EXCEPTION 'Migration failed: farm_id is not set to NOT NULL';
    END IF;

    IF NOT created_by_not_null THEN
        RAISE EXCEPTION 'Migration failed: created_by is not set to NOT NULL';
    END IF;

    RAISE NOTICE 'Migration successful: signable_documents constraints updated';
END $$;
