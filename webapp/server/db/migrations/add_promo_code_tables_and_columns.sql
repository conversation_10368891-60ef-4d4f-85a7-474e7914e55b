-- Add promo code tables and columns to the database
-- This migration adds a promo_codes table and updates existing tables to support promo codes

-- Set the search path to the site schema
SET search_path TO site;

-- Create promo_codes table
CREATE TABLE IF NOT EXISTS promo_codes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  code VARCHAR(50) NOT NULL UNIQUE,
  description TEXT,
  subscription_plan_id UUID REFERENCES subscription_plans(id) ON DELETE SET NULL,
  valid_from TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  valid_to TIMESTAMP WITH TIME ZONE,
  max_uses INTEGER,
  current_uses INTEGER NOT NULL DEFAULT 0,
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  farm_id UUID REFERENCES farms(id) ON DELETE SET NULL,
  discount_percent DECIMAL(5, 2) CHECK (discount_percent >= 0 AND discount_percent <= 100),
  discount_amount DECIMAL(10, 2) CHECK (discount_amount >= 0),
  discount_months INTEGER,
  applies_to_monthly BOOLEAN NOT NULL DEFAULT TRUE,
  applies_to_yearly BOOLEAN NOT NULL DEFAULT TRUE,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  CONSTRAINT either_discount_percent_or_amount CHECK (
    (discount_percent IS NULL AND discount_amount IS NOT NULL) OR
    (discount_percent IS NOT NULL AND discount_amount IS NULL)
  )
);

-- Create indexes for promo_codes
CREATE INDEX IF NOT EXISTS promo_codes_code_idx ON promo_codes(code);
CREATE INDEX IF NOT EXISTS promo_codes_subscription_plan_id_idx ON promo_codes(subscription_plan_id);
CREATE INDEX IF NOT EXISTS promo_codes_user_id_idx ON promo_codes(user_id);
CREATE INDEX IF NOT EXISTS promo_codes_farm_id_idx ON promo_codes(farm_id);
CREATE INDEX IF NOT EXISTS promo_codes_valid_from_valid_to_idx ON promo_codes(valid_from, valid_to);

-- Add trigger for updated_at
CREATE OR REPLACE FUNCTION update_promo_codes_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_promo_codes_timestamp
BEFORE UPDATE ON promo_codes
FOR EACH ROW
EXECUTE FUNCTION update_promo_codes_updated_at();

-- Add columns to subscription_transactions table
ALTER TABLE subscription_transactions
ADD COLUMN IF NOT EXISTS original_amount DECIMAL(10, 2),
ADD COLUMN IF NOT EXISTS discount_amount DECIMAL(10, 2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS promo_code VARCHAR(50),
ADD COLUMN IF NOT EXISTS promo_code_id UUID REFERENCES promo_codes(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS billing_cycle VARCHAR(10);

-- Add columns to farms table
ALTER TABLE farms
ADD COLUMN IF NOT EXISTS promo_code VARCHAR(50),
ADD COLUMN IF NOT EXISTS promo_code_id UUID REFERENCES promo_codes(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS promo_discount_months INTEGER,
ADD COLUMN IF NOT EXISTS billing_cycle VARCHAR(10);

-- Add comments to tables and columns
COMMENT ON TABLE promo_codes IS 'Stores promo codes for subscription discounts';
COMMENT ON COLUMN promo_codes.code IS 'Unique promo code string';
COMMENT ON COLUMN promo_codes.description IS 'Description of the promo code';
COMMENT ON COLUMN promo_codes.subscription_plan_id IS 'If set, promo code only applies to this subscription plan';
COMMENT ON COLUMN promo_codes.valid_from IS 'Date from which the promo code is valid';
COMMENT ON COLUMN promo_codes.valid_to IS 'Date until which the promo code is valid, null means no expiration';
COMMENT ON COLUMN promo_codes.max_uses IS 'Maximum number of times the promo code can be used, null means unlimited';
COMMENT ON COLUMN promo_codes.current_uses IS 'Current number of times the promo code has been used';
COMMENT ON COLUMN promo_codes.user_id IS 'If set, promo code is only valid for this user';
COMMENT ON COLUMN promo_codes.farm_id IS 'If set, promo code is only valid for this farm';
COMMENT ON COLUMN promo_codes.discount_percent IS 'Percentage discount (0-100)';
COMMENT ON COLUMN promo_codes.discount_amount IS 'Fixed amount discount';
COMMENT ON COLUMN promo_codes.discount_months IS 'Number of months the discount applies for, null means perpetual';
COMMENT ON COLUMN promo_codes.applies_to_monthly IS 'Whether the promo code applies to monthly billing plans';
COMMENT ON COLUMN promo_codes.applies_to_yearly IS 'Whether the promo code applies to yearly billing plans';
COMMENT ON COLUMN promo_codes.is_active IS 'Whether the promo code is active';

COMMENT ON COLUMN subscription_transactions.original_amount IS 'Original amount before any discount';
COMMENT ON COLUMN subscription_transactions.discount_amount IS 'Amount of discount applied';
COMMENT ON COLUMN subscription_transactions.promo_code IS 'Promo code that was applied';
COMMENT ON COLUMN subscription_transactions.promo_code_id IS 'ID of the promo code that was applied';
COMMENT ON COLUMN subscription_transactions.billing_cycle IS 'Billing cycle (monthly or yearly)';

COMMENT ON COLUMN farms.promo_code IS 'Promo code that was applied to the current subscription';
COMMENT ON COLUMN farms.promo_code_id IS 'ID of the promo code that was applied to the current subscription';
COMMENT ON COLUMN farms.promo_discount_months IS 'Number of months the discount applies for';
COMMENT ON COLUMN farms.billing_cycle IS 'Billing cycle (monthly or yearly)';

-- Record the migration in the database_migrations table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'database_migrations') THEN
    INSERT INTO site.database_migrations (id, name, file_path, "order", created_at, updated_at, status)
    VALUES (
      gen_random_uuid(),
      'Add Promo Code Tables and Columns',
      'migrations/add_promo_code_tables_and_columns.sql',
      (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
      NOW(),
      NOW(),
      'pending'
    );
  END IF;
END$$;