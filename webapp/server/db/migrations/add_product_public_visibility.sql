-- Migration: Add is_public field to products table for customer portal
-- Depends on: add_transport_and_receipt_tables.sql

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Get the schema name from environment variable or use 'site' as default
DO $$
DECLARE
    schema_name TEXT;
BEGIN
    -- Get the schema name from environment variable
    schema_name := current_setting('app.db_schema', true);

    -- If the environment variable is not set, use 'site' as default
    IF schema_name IS NULL THEN
        schema_name := 'site';
    END IF;

    -- Add is_public column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = schema_name
        AND table_name = 'products'
        AND column_name = 'is_public'
    ) THEN
        EXECUTE format('ALTER TABLE %I.products ADD COLUMN is_public BOOLEAN NOT NULL DEFAULT FALSE', schema_name);
    END IF;

    -- Record the migration in the database_migrations table if it exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = schema_name AND table_name = 'database_migrations') THEN
        INSERT INTO site.database_migrations (id, name, file_path, "order", applied_at, created_at, updated_at, status)
        VALUES (
            gen_random_uuid(),
            'Add product public visibility field',
            'webapp/server/db/migrations/add_product_public_visibility.sql',
            (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
            NOW(),
            NOW(),
            NOW(),
            'applied'
        );
    END IF;
END $$;