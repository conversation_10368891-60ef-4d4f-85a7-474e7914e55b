-- Migration: Add AI document generations table for tracking AI-generated documents
-- Depends on: add_document_signing_tables.sql

SET search_path TO site;

-- Create ai_document_generations table
CREATE TABLE IF NOT EXISTS ai_document_generations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
  document_id UUID NOT NULL REFERENCES signable_documents(id) ON DELETE CASCADE,
  prompt TEXT NOT NULL,
  document_type VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS idx_ai_document_generations_user_id ON ai_document_generations(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_document_generations_farm_id ON ai_document_generations(farm_id);
CREATE INDEX IF NOT EXISTS idx_ai_document_generations_document_id ON ai_document_generations(document_id);

-- Add is_ai_generated and ai_prompt columns to signable_documents table
ALTER TABLE signable_documents 
ADD COLUMN IF NOT EXISTS is_ai_generated BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS ai_prompt TEXT;

-- Add comments
COMMENT ON TABLE ai_document_generations IS 'Stores history of AI-generated documents';
COMMENT ON COLUMN ai_document_generations.id IS 'Unique identifier for the AI document generation record';
COMMENT ON COLUMN ai_document_generations.user_id IS 'Reference to the user who generated the document';
COMMENT ON COLUMN ai_document_generations.farm_id IS 'Reference to the farm the document belongs to';
COMMENT ON COLUMN ai_document_generations.document_id IS 'Reference to the generated document';
COMMENT ON COLUMN ai_document_generations.prompt IS 'The prompt used to generate the document';
COMMENT ON COLUMN ai_document_generations.document_type IS 'The type of document that was generated';
COMMENT ON COLUMN ai_document_generations.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN ai_document_generations.updated_at IS 'Timestamp when the record was last updated';

COMMENT ON COLUMN signable_documents.is_ai_generated IS 'Indicates whether the document was generated by AI';
COMMENT ON COLUMN signable_documents.ai_prompt IS 'The prompt used to generate the document, if AI-generated';