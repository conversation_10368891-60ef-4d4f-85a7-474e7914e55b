-- Migration: Add schema migrations table
-- Creates a table to track applied schema migrations and their dependencies
-- Set the search path to the appropriate schema
SET search_path TO site;

-- Create the schema_migrations table
CREATE TABLE IF NOT EXISTS schema_migrations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  filename VARCHAR(255) NOT NULL UNIQUE,
  description TEXT,
  applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  applied_by VARCHAR(255),
  status VARCHAR(20) DEFAULT 'success',
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create the schema_migration_dependencies table to track dependencies between migrations
CREATE TABLE IF NOT EXISTS schema_migration_dependencies (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  migration_filename VARCHAR(255) NOT NULL,
  depends_on_filename VARCHAR(255) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  FOREIGN KEY (migration_filename) REFERENCES schema_migrations(filename) ON DELETE CASCADE,
  UNIQUE(migration_filename, depends_on_filename)
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_schema_migrations_filename ON schema_migrations(filename);
CREATE INDEX IF NOT EXISTS idx_schema_migrations_status ON schema_migrations(status);
CREATE INDEX IF NOT EXISTS idx_schema_migration_dependencies_migration_filename ON schema_migration_dependencies(migration_filename);
CREATE INDEX IF NOT EXISTS idx_schema_migration_dependencies_depends_on_filename ON schema_migration_dependencies(depends_on_filename);

-- Add comments to tables and columns for documentation
COMMENT ON TABLE schema_migrations IS 'Tracks applied schema migrations';
COMMENT ON TABLE schema_migration_dependencies IS 'Tracks dependencies between schema migrations';