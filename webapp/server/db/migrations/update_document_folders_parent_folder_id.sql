-- Migration: Update document_folders table to use uuid_nil() instead of NULL for parent_folder_id
-- Depends on: add_document_folders_table.sql

SET search_path TO site;

-- First, check if a root folder with the UUID nil value already exists
DO $$
DECLARE
    root_folder_exists BOOLEAN;
    has_existing_folders BOOLEAN;
    v_farm_id UUID;
    v_created_by UUID;
BEGIN
    -- Check if the root folder already exists
    SELECT EXISTS (
        SELECT 1 FROM document_folders 
        WHERE id = '00000000-0000-0000-0000-000000000000'
    ) INTO root_folder_exists;

    -- Check if there are any existing folders
    SELECT EXISTS (
        SELECT 1 FROM document_folders
    ) INTO has_existing_folders;

    -- If the root folder doesn't exist, create it
    IF NOT root_folder_exists THEN
        -- If there are existing folders, use values from them
        IF has_existing_folders THEN
            SELECT farm_id, created_by INTO v_farm_id, v_created_by
            FROM document_folders
            LIMIT 1;
        ELSE
            -- If no existing folders, we need to get valid IDs from other tables
            -- First try to get a farm_id
            BEGIN
                SELECT id INTO v_farm_id FROM farms LIMIT 1;
            EXCEPTION WHEN OTHERS THEN
                -- If farms table doesn't exist or is empty, use a nil UUID
                v_farm_id := '00000000-0000-0000-0000-000000000000';
            END;

            -- Then try to get a user_id for created_by
            BEGIN
                SELECT id INTO v_created_by FROM users LIMIT 1;
            EXCEPTION WHEN OTHERS THEN
                -- If users table doesn't exist or is empty, use a nil UUID
                v_created_by := '00000000-0000-0000-0000-000000000000';
            END;
        END IF;

        -- Now insert the root folder with the values we determined
        INSERT INTO document_folders (
            id, 
            name, 
            description, 
            parent_folder_id, 
            farm_id, 
            created_by, 
            created_at, 
            updated_at
        ) VALUES (
            '00000000-0000-0000-0000-000000000000', 
            'Root Folder', 
            'System root folder for document hierarchy', 
            '00000000-0000-0000-0000-000000000000',  -- Self-referential for the root
            v_farm_id, 
            v_created_by, 
            NOW(), 
            NOW()
        );
    END IF;
END $$;

-- Now update existing records with NULL parent_folder_id to use uuid_nil()
UPDATE document_folders
SET parent_folder_id = '00000000-0000-0000-0000-000000000000'
WHERE parent_folder_id IS NULL;

-- Add a NOT NULL constraint to parent_folder_id column
ALTER TABLE document_folders
ALTER COLUMN parent_folder_id SET NOT NULL;
