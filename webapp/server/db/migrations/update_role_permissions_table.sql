-- Update role_permissions table to work with the new roles table

-- Set the search path to the site schema
SET search_path TO site;

-- Add role_id column to role_permissions table if it doesn't exist
ALTER TABLE site.role_permissions ADD COLUMN IF NOT EXISTS role_id UUID REFERENCES site.roles(id);
COMMENT ON COLUMN site.role_permissions.role_id IS 'Reference to the role';

-- Modify role_name column to be nullable and mark as deprecated
ALTER TABLE site.role_permissions ALTER COLUMN role_name DROP NOT NULL;
COMMENT ON COLUMN site.role_permissions.role_name IS 'Name of the role (deprecated, use role_id instead)';

-- Update the unique index to use role_id instead of role_name
DROP INDEX IF EXISTS role_permissions_farm_role_feature_idx;
CREATE UNIQUE INDEX IF NOT EXISTS role_permissions_farm_role_feature_idx ON site.role_permissions(farm_id, role_id, feature);

-- Verify the column was added
DO $$
DECLARE
    column_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'site' 
        AND table_name = 'role_permissions' 
        AND column_name = 'role_id'
    ) INTO column_exists;

    IF column_exists THEN
        RAISE NOTICE 'The role_id column was successfully added to the role_permissions table.';
    ELSE
        RAISE EXCEPTION 'Failed to add the role_id column to the role_permissions table.';
    END IF;
END $$;

-- Migrate existing role_permissions to use role_id
DO $$
DECLARE
    permission_record RECORD;
    found_role_id UUID;
BEGIN
    FOR permission_record IN SELECT * FROM site.role_permissions WHERE role_id IS NULL AND role_name IS NOT NULL LOOP
        -- Find the corresponding role
        SELECT id INTO found_role_id FROM site.roles 
        WHERE name = permission_record.role_name 
        AND (roles.farm_id = permission_record.farm_id OR (roles.farm_id IS NULL AND permission_record.farm_id IS NULL));

        IF found_role_id IS NOT NULL THEN
            -- Update the permission with the role_id
            UPDATE site.role_permissions 
            SET role_id = found_role_id 
            WHERE id = permission_record.id;

            RAISE NOTICE 'Updated role_permission % with role_id %', permission_record.id, found_role_id;
        ELSE
            -- If no matching role found, create one
            INSERT INTO site.roles (id, farm_id, name, description, is_system_role)
            VALUES (gen_random_uuid(), permission_record.farm_id, permission_record.role_name, 'Migrated from legacy role', TRUE)
            RETURNING id INTO found_role_id;

            -- Update the permission with the new role_id
            UPDATE site.role_permissions 
            SET role_id = found_role_id 
            WHERE id = permission_record.id;

            RAISE NOTICE 'Created new role % for role_permission %', found_role_id, permission_record.id;
        END IF;
    END LOOP;
END $$;
