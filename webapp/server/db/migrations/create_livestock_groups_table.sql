-- Create livestock_groups table for farm management
-- This migration adds the livestock_groups table which is referenced by the AI analysis tables

-- Set the search path to the site schema
SET search_path TO site;

-- Create a function to log migration progress
CREATE OR REPLACE FUNCTION log_migration_step(step_name TEXT)
RETURNS VOID AS $$
BEGIN
    RAISE NOTICE 'Migration step: %', step_name;
END;
$$ LANGUAGE plpgsql;

-- Begin transaction
BEGIN;

-- Step 1: Create livestock_groups table
SELECT log_migration_step('Creating livestock_groups table');

CREATE TABLE IF NOT EXISTS site.livestock_groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID NOT NULL REFERENCES site.farms(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  type VARCHAR(100),
  breed VARCHAR(100),
  quantity INTEGER,
  status VARCHAR(50),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to columns
COMMENT ON COLUMN site.livestock_groups.id IS 'Unique identifier for the livestock group';
COMMENT ON COLUMN site.livestock_groups.farm_id IS 'ID of the farm this livestock group belongs to';
COMMENT ON COLUMN site.livestock_groups.name IS 'Name of the livestock group';
COMMENT ON COLUMN site.livestock_groups.description IS 'Description of the livestock group';
COMMENT ON COLUMN site.livestock_groups.type IS 'Type of livestock (e.g., cattle, sheep, poultry)';
COMMENT ON COLUMN site.livestock_groups.breed IS 'Breed of the livestock';
COMMENT ON COLUMN site.livestock_groups.quantity IS 'Number of animals in the group';
COMMENT ON COLUMN site.livestock_groups.status IS 'Status of the livestock group';
COMMENT ON COLUMN site.livestock_groups.notes IS 'Additional notes about the livestock group';
COMMENT ON COLUMN site.livestock_groups.created_at IS 'Timestamp when the livestock group was created';
COMMENT ON COLUMN site.livestock_groups.updated_at IS 'Timestamp when the livestock group was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS livestock_groups_farm_id_idx ON site.livestock_groups(farm_id);
CREATE INDEX IF NOT EXISTS livestock_groups_type_idx ON site.livestock_groups(type);
CREATE INDEX IF NOT EXISTS livestock_groups_status_idx ON site.livestock_groups(status);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_livestock_groups_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_livestock_groups_timestamp
BEFORE UPDATE ON site.livestock_groups
FOR EACH ROW EXECUTE FUNCTION update_livestock_groups_updated_at();

-- Record the migration in the database_migrations table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'database_migrations') THEN
        INSERT INTO site.database_migrations (id, name, file_path, "order", applied_at, created_at, updated_at)
        VALUES (
            gen_random_uuid(), 
            'create_livestock_groups_table', 
            'webapp/server/db/migrations/create_livestock_groups_table.sql',
            (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        );
    END IF;
END $$;

-- Commit transaction
COMMIT;

-- Verify table was created
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'livestock_groups') THEN
        RAISE EXCEPTION 'Migration failed. The livestock_groups table was not created.';
    ELSE
        RAISE NOTICE 'Migration successful. The livestock_groups table was created.';
    END IF;
END $$;