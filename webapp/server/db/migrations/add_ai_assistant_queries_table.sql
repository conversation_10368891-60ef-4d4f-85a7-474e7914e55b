-- Migration: Add AI assistant queries table
-- Depends on: 

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Create table for AI assistant queries
CREATE TABLE IF NOT EXISTS site.ai_assistant_queries (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL,
  farm_id UUID NOT NULL,
  query TEXT NOT NULL,
  response TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES site.users(id) ON DELETE CASCADE,
  FOREIGN KEY (farm_id) REFERENCES site.farms(id) ON DELETE CASCADE
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_ai_assistant_queries_user_id ON site.ai_assistant_queries(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_assistant_queries_farm_id ON site.ai_assistant_queries(farm_id);
CREATE INDEX IF NOT EXISTS idx_ai_assistant_queries_created_at ON site.ai_assistant_queries(created_at);

-- Create table for AI assistant suggestions
CREATE TABLE IF NOT EXISTS site.ai_assistant_suggestions (
  id SERIAL PRIMARY KEY,
  farm_id UUID NOT NULL,
  suggestion TEXT NOT NULL,
  category VARCHAR(50) NOT NULL,
  is_read BOOLEAN DEFAULT FALSE,
  is_implemented BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (farm_id) REFERENCES site.farms(id) ON DELETE CASCADE
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_ai_assistant_suggestions_farm_id ON site.ai_assistant_suggestions(farm_id);
CREATE INDEX IF NOT EXISTS idx_ai_assistant_suggestions_category ON site.ai_assistant_suggestions(category);
CREATE INDEX IF NOT EXISTS idx_ai_assistant_suggestions_created_at ON site.ai_assistant_suggestions(created_at);