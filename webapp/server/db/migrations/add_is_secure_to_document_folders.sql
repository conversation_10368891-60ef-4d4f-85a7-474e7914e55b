-- Migration: Add is_secure column to document_folders table
-- Depends on:

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Function to log migration progress
CREATE OR REPLACE FUNCTION log_migration_step(step_name TEXT)
RETURNS VOID AS $$
BEGIN
  RAISE NOTICE 'Migration step: %', step_name;
  RETURN;
END;
$$ LANGUAGE plpgsql;

BEGIN;

-- Add is_secure column to document_folders table
SELECT log_migration_step('Adding is_secure column to document_folders table');

ALTER TABLE document_folders
ADD COLUMN is_secure BOOLEAN NOT NULL DEFAULT FALSE;

-- Add index for better query performance
SELECT log_migration_step('Adding index for is_secure column');

CREATE INDEX idx_document_folders_is_secure ON document_folders(is_secure);

COMMIT;

-- Add comment to explain the purpose of the new column
COMMENT ON COLUMN document_folders.is_secure IS 'Indicates whether the folder contains secure/sensitive documents that cannot be shared with public links';

-- Clean up the temporary function
DROP FUNCTION log_migration_step(TEXT);
