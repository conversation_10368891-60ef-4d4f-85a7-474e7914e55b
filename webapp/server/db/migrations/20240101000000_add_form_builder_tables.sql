-- Migration: Add form builder tables for document signing system
-- Depends on: 

SET search_path TO site;

-- Add new column to signable_documents table to indicate if it's a form-built document
ALTER TABLE signable_documents ADD COLUMN IF NOT EXISTS is_form_built BOOLEAN DEFAULT FALSE;

-- Create table for document elements (text, images, etc.)
CREATE TABLE IF NOT EXISTS document_elements (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  document_id UUID NOT NULL REFERENCES signable_documents(id) ON DELETE CASCADE,
  element_type VARCHAR(50) NOT NULL, -- 'text', 'image', 'field', 'signature', 'date', etc.
  content TEXT, -- For text elements
  image_path VARCHAR(1024), -- For image elements
  field_id UUID, -- Reference to document_fields for field elements
  page_number INTEGER NOT NULL DEFAULT 1,
  x_position FLOAT NOT NULL,
  y_position FLOAT NOT NULL,
  width FLOAT NOT NULL,
  height FLOAT NOT NULL,
  z_index INTEGER NOT NULL DEFAULT 0,
  font_family VARCHAR(100), -- For text elements
  font_size INTEGER, -- For text elements
  font_color VARCHAR(20), -- For text elements
  background_color VARCHAR(20),
  border_style VARCHAR(20),
  border_width INTEGER,
  border_color VARCHAR(20),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for document_elements
CREATE INDEX IF NOT EXISTS document_elements_document_id_idx ON document_elements(document_id);
CREATE INDEX IF NOT EXISTS document_elements_element_type_idx ON document_elements(element_type);

-- Update field_type to include new field types
DO $$
DECLARE
    column_type TEXT;
BEGIN
    -- Check if field_type is an enum type
    SELECT data_type INTO column_type 
    FROM information_schema.columns 
    WHERE table_schema = 'site' 
    AND table_name = 'document_fields' 
    AND column_name = 'field_type';

    IF column_type = 'USER-DEFINED' THEN
        -- It's an enum type, so we need to alter the enum
        -- First, check if the enum already has the values we want to add
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum 
            WHERE enumtypid = (
                SELECT oid FROM pg_type 
                WHERE typname = 'enum_document_fields_field_type'
            ) 
            AND enumlabel = 'image'
        ) THEN
            -- Add new values to the enum
            ALTER TYPE enum_document_fields_field_type ADD VALUE IF NOT EXISTS 'image';
            ALTER TYPE enum_document_fields_field_type ADD VALUE IF NOT EXISTS 'heading';
            ALTER TYPE enum_document_fields_field_type ADD VALUE IF NOT EXISTS 'paragraph';
            ALTER TYPE enum_document_fields_field_type ADD VALUE IF NOT EXISTS 'divider';
            ALTER TYPE enum_document_fields_field_type ADD VALUE IF NOT EXISTS 'spacer';
        END IF;
    ELSE
        -- It's a VARCHAR with a CHECK constraint, so update the constraint
        ALTER TABLE document_fields DROP CONSTRAINT IF EXISTS document_fields_field_type_check;
        ALTER TABLE document_fields ADD CONSTRAINT document_fields_field_type_check 
          CHECK (field_type IN ('signature', 'initial', 'date', 'text', 'checkbox', 'radio', 'dropdown', 'attachment', 
                                'image', 'heading', 'paragraph', 'divider', 'spacer'));
    END IF;
END
$$;

-- Add new columns to document_fields for form builder
ALTER TABLE document_fields ADD COLUMN IF NOT EXISTS placeholder TEXT;
ALTER TABLE document_fields ADD COLUMN IF NOT EXISTS options JSONB; -- For dropdown, radio, etc.
ALTER TABLE document_fields ADD COLUMN IF NOT EXISTS validation_rules JSONB; -- For validation
ALTER TABLE document_fields ADD COLUMN IF NOT EXISTS style_properties JSONB; -- For styling
