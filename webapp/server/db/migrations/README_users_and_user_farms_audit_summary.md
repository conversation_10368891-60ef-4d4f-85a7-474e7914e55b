# Users and User Farms Schema Audit Summary

## Overview

This document summarizes the findings and fixes from the schema audit of the users and user farms areas. The audit identified several issues with the database schema and model definitions, which have been addressed with migration files and documentation.

## Issues Identified

### 1. Data Type Inconsistencies in the Users Table

Several columns in the `users` table have inconsistent data types between the database schema and the model definition:

- `created_at` and `updated_at` are defined as `varchar` in the database schema, but as `DataTypes.DATE` in the model definition.
- `reset_password_expires` is defined as `varchar` in the database schema, but as `DataTypes.DATE` in the model definition.
- `email_2fa_expires` is defined as `varchar` in the database schema, but as `DataTypes.DATE` in the model definition.

### 2. Data Type Inconsistencies in the User Farms Table

Several columns in the `user_farms` table have inconsistent data types between the database schema and the model definition:

- `created_at` and `updated_at` are defined as `varchar` in the database schema, but as `DataTypes.DATE` in the model definition.
- `permissions` is defined as `varchar` in the database schema, but as `DataTypes.JSON` in the model definition.

### 3. Enum Type Usage

Models are defining enum types inline rather than using the enum types defined in the database schema. This could lead to inconsistencies if the enum values change in the database but not in the model.

Examples:
- In `User.js`, `two_factor_method` is defined as `DataTypes.ENUM('app', 'sms', 'email')` instead of using the `enum_users_two_factor_method` type.
- In `User.js`, `user_type` is defined as `DataTypes.ENUM('farmer', 'supplier', 'vet', 'admin', 'accountant')` instead of using the `user_type_enum` type.
- In `UserFarm.js`, `role` is defined as `DataTypes.ENUM('farm_owner', 'farm_admin', 'farm_manager', 'farm_employee', 'accountant')` instead of using the `enum_user_farms_role` type.

### 4. Duplicate Enum Types

There are duplicate enum types defined in the database schema:

- `enum_users_user_type` and `user_type_enum` have the same values.
- `enum_user_farms_role` and `user_farm_role_enum` have the same values.

This duplication could lead to confusion and maintenance issues.

## Fixes Implemented

### 1. Data Type Inconsistencies in the Users Table

Created a migration file to fix the data type inconsistencies in the `users` table:
- `fix_users_column_types.sql`: Changes the data types of `created_at`, `updated_at`, `reset_password_expires`, and `email_2fa_expires` columns in the `users` table.

### 2. Data Type Inconsistencies in the User Farms Table

Created a migration file to fix the data type inconsistencies in the `user_farms` table:
- `fix_user_farms_column_types.sql`: Changes the data types of `created_at`, `updated_at`, and `permissions` columns in the `user_farms` table.

## Recommended Next Steps

1. **Use Enum Types from Database Schema**: Update models to use the enum types defined in the database schema instead of defining them inline.

2. **Consolidate Duplicate Enum Types**: Create a migration to consolidate the duplicate enum types and update all references to use a single enum type.

3. **Regular Schema Audits**: Conduct regular schema audits to identify and fix any new inconsistencies that may arise.

## Documentation

Detailed documentation for each fix has been provided in the following files:
- `README_users_column_types_fix.md`: Documents the fix for data type inconsistencies in the `users` table.
- `README_user_farms_column_types_fix.md`: Documents the fix for data type inconsistencies in the `user_farms` table.
- `README_users_and_user_farms_audit_summary.md` (this file): Summarizes all findings and fixes from the schema audit of the users and user farms areas.