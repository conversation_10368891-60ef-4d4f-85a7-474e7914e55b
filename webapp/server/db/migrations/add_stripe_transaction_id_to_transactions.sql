-- Migration: Add stripe_transaction_id column to transactions table
-- Depends on:

SET search_path TO site;

-- Add stripe_transaction_id column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'site'
        AND table_name = 'transactions'
        AND column_name = 'stripe_transaction_id'
    ) THEN
        ALTER TABLE transactions ADD COLUMN stripe_transaction_id VARCHAR(255);
        COMMENT ON COLUMN transactions.stripe_transaction_id IS 'Stripe transaction ID for transactions imported from Stripe Financial Connections';
    END IF;
END $$;

-- Create index on stripe_transaction_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'site'
        AND tablename = 'transactions'
        AND indexname = 'idx_transactions_stripe_transaction_id'
    ) THEN
        CREATE INDEX idx_transactions_stripe_transaction_id ON transactions(stripe_transaction_id);
    END IF;
END $$;