-- Add missing columns to user_farms table

-- Set the search path to the site schema
SET search_path TO site;

-- Add permissions column
ALTER TABLE site.user_farms ADD COLUMN IF NOT EXISTS permissions JSONB;
COMMENT ON COLUMN site.user_farms.permissions IS 'Custom permissions for this role, overrides default role permissions';

-- Add is_billing_contact column
ALTER TABLE site.user_farms ADD COLUMN IF NOT EXISTS is_billing_contact BOOLEAN DEFAULT FALSE;
COMMENT ON COLUMN site.user_farms.is_billing_contact IS 'Whether this user is responsible for billing for this farm';

-- Fix role column to match the model definition
-- First, create the enum type if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_farm_role_enum') THEN
        CREATE TYPE user_farm_role_enum AS ENUM ('farm_owner', 'farm_admin', 'farm_manager', 'farm_employee', 'accountant');
    END IF;
END$$;

-- Then update the column to use the enum type
-- Note: This is a complex operation that might require data migration
-- For now, we'll keep the VARCHAR type but add a check constraint to enforce valid values
ALTER TABLE site.user_farms DROP CONSTRAINT IF EXISTS check_user_farm_role;
ALTER TABLE site.user_farms ADD CONSTRAINT check_user_farm_role 
    CHECK (role IN ('farm_owner', 'farm_admin', 'farm_manager', 'farm_employee', 'accountant'));

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_user_farms_is_billing_contact ON site.user_farms(is_billing_contact);
