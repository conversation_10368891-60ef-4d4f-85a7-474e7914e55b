SET search_path TO site;

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Add roles table
CREATE TABLE IF NOT EXISTS roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID REFERENCES farms(id) ON DELETE CASCADE,
    name VARCHAR(50) NOT NULL,
    description VARCHAR(255),
    is_system_role BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add unique index on farm_id and name
CREATE UNIQUE INDEX IF NOT EXISTS roles_farm_name_idx ON roles(farm_id, name);

-- Create default global roles
INSERT INTO roles (id, farm_id, name, description, is_system_role, created_at, updated_at)
VALUES 
    (uuid_generate_v4(), NULL, 'farm_owner', 'Farm Owner', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    (uuid_generate_v4(), NULL, 'farm_admin', 'Farm Administrator', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    (uuid_generate_v4(), NULL, 'farm_manager', 'Farm Manager', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    (uuid_generate_v4(), NULL, 'farm_employee', 'Farm Employee', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    (uuid_generate_v4(), NULL, 'accountant', 'Accountant', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (farm_id, name) DO NOTHING;
