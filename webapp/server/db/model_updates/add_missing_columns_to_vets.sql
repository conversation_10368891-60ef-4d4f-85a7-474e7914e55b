-- Add missing columns to vets table
-- Generated on 2025-04-30T19:58:20.424Z
-- Set the search path to the appropriate schema
SET search_path TO site;

-- Get the schema from environment variable or use 'site' as default
DO $$
DECLARE
    schema_name TEXT;
BEGIN
    -- Get the current schema or use 'site' as default
    BEGIN
        -- Check if DB_SCHEMA is set as a PostgreSQL variable with proper namespace
        SELECT current_setting('app.db_schema') INTO schema_name;
        EXCEPTION WHEN OTHERS THEN
            -- If not set, use current schema or 'site' as default
            SELECT current_schema() INTO schema_name;
    END;

    -- Add user_id column if it doesn't exist
    EXECUTE format('
        ALTER TABLE %I.vets 
        ADD COLUMN IF NOT EXISTS user_id UUID;
    ', schema_name);
    -- Add comment to the column
    EXECUTE format('
        COMMENT ON COLUMN %I.vets.user_id IS ''User account associated with this vet'';
    ', schema_name);

    -- Add is_active column if it doesn't exist
    EXECUTE format('
        ALTER TABLE %I.vets 
        ADD COLUMN IF NOT EXISTS is_active BOOLEAN NOT NULL DEFAULT true;
    ', schema_name);
    -- Add comment to the column
    EXECUTE format('
        COMMENT ON COLUMN %I.vets.is_active IS ''Whether this vet listing is active'';
    ', schema_name);

    RAISE NOTICE 'Successfully added missing columns to vets table in schema %', schema_name;
END $$;
