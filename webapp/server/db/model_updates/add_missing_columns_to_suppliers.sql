-- Add missing columns to suppliers table
-- Generated on 2025-04-30T19:58:20.408Z
-- Set the search path to the appropriate schema
SET search_path TO site;

-- Get the schema from environment variable or use 'site' as default
DO $$
DECLARE
    schema_name TEXT;
BEGIN
    -- Try to get schema from DB_SCHEMA environment variable, fall back to current_schema() if not available
    BEGIN
        -- Check if DB_SCHEMA is set as a PostgreSQL variable with proper namespace
        SELECT current_setting('app.db_schema') INTO schema_name;
        EXCEPTION WHEN OTHERS THEN
            -- If not set, use current schema or 'site' as default
            SELECT current_schema() INTO schema_name;
    END;

    -- Add user_id column if it doesn't exist
    EXECUTE format('
        ALTER TABLE %I.suppliers 
        ADD COLUMN IF NOT EXISTS user_id UUID;
    ', schema_name);
    -- Add comment to the column
    EXECUTE format('
        COMMENT ON COLUMN %I.suppliers.user_id IS ''User account associated with this supplier'';
    ', schema_name);

    -- Add latitude column if it doesn't exist
    EXECUTE format('
        ALTER TABLE %I.suppliers 
        ADD COLUMN IF NOT EXISTS latitude DECIMAL(10, 6);
    ', schema_name);
    -- Add comment to the column
    EXECUTE format('
        COMMENT ON COLUMN %I.suppliers.latitude IS ''Latitude for location-aware supplier search'';
    ', schema_name);

    -- Add longitude column if it doesn't exist
    EXECUTE format('
        ALTER TABLE %I.suppliers 
        ADD COLUMN IF NOT EXISTS longitude DECIMAL(10, 6);
    ', schema_name);
    -- Add comment to the column
    EXECUTE format('
        COMMENT ON COLUMN %I.suppliers.longitude IS ''Longitude for location-aware supplier search'';
    ', schema_name);

    -- Add product_types column if it doesn't exist
    EXECUTE format('
        ALTER TABLE %I.suppliers 
        ADD COLUMN IF NOT EXISTS product_types VARCHAR(255)[] DEFAULT ARRAY[]::VARCHAR(255)[];
    ', schema_name);
    -- Add comment to the column
    EXECUTE format('
        COMMENT ON COLUMN %I.suppliers.product_types IS ''Types of products offered by this supplier'';
    ', schema_name);

    -- Add availability column if it doesn't exist
    EXECUTE format('
        ALTER TABLE %I.suppliers 
        ADD COLUMN IF NOT EXISTS availability JSONB;
    ', schema_name);
    -- Add comment to the column
    EXECUTE format('
        COMMENT ON COLUMN %I.suppliers.availability IS ''Availability information in JSON format'';
    ', schema_name);

    -- Add is_preferred column if it doesn't exist
    EXECUTE format('
        ALTER TABLE %I.suppliers 
        ADD COLUMN IF NOT EXISTS is_preferred BOOLEAN NOT NULL DEFAULT false;
    ', schema_name);
    -- Add comment to the column
    EXECUTE format('
        COMMENT ON COLUMN %I.suppliers.is_preferred IS ''Whether this supplier is preferred by the farmer'';
    ', schema_name);

    -- Add api_integration column if it doesn't exist
    EXECUTE format('
        ALTER TABLE %I.suppliers 
        ADD COLUMN IF NOT EXISTS api_integration JSONB;
    ', schema_name);
    -- Add comment to the column
    EXECUTE format('
        COMMENT ON COLUMN %I.suppliers.api_integration IS ''External API integration information in JSON format'';
    ', schema_name);

    -- Add api_key column if it doesn't exist
    EXECUTE format('
        ALTER TABLE %I.suppliers 
        ADD COLUMN IF NOT EXISTS api_key VARCHAR(255);
    ', schema_name);
    -- Add comment to the column
    EXECUTE format('
        COMMENT ON COLUMN %I.suppliers.api_key IS ''API key for external integrations'';
    ', schema_name);

    -- Add api_endpoint column if it doesn't exist
    EXECUTE format('
        ALTER TABLE %I.suppliers 
        ADD COLUMN IF NOT EXISTS api_endpoint VARCHAR(255);
    ', schema_name);
    -- Add comment to the column
    EXECUTE format('
        COMMENT ON COLUMN %I.suppliers.api_endpoint IS ''API endpoint for external integrations'';
    ', schema_name);

    -- Add business_hours column if it doesn't exist
    EXECUTE format('
        ALTER TABLE %I.suppliers 
        ADD COLUMN IF NOT EXISTS business_hours JSONB;
    ', schema_name);
    -- Add comment to the column
    EXECUTE format('
        COMMENT ON COLUMN %I.suppliers.business_hours IS ''Business hours in JSON format'';
    ', schema_name);

    -- Add rating column if it doesn't exist
    EXECUTE format('
        ALTER TABLE %I.suppliers 
        ADD COLUMN IF NOT EXISTS rating DECIMAL(3, 2);
    ', schema_name);
    -- Add comment to the column
    EXECUTE format('
        COMMENT ON COLUMN %I.suppliers.rating IS ''Average rating from reviews'';
    ', schema_name);

    -- Add review_count column if it doesn't exist
    EXECUTE format('
        ALTER TABLE %I.suppliers 
        ADD COLUMN IF NOT EXISTS review_count INTEGER NOT NULL DEFAULT 0;
    ', schema_name);
    -- Add comment to the column
    EXECUTE format('
        COMMENT ON COLUMN %I.suppliers.review_count IS ''Number of reviews'';
    ', schema_name);

    RAISE NOTICE 'Successfully added missing columns to suppliers table in schema %', schema_name;
END $$;
