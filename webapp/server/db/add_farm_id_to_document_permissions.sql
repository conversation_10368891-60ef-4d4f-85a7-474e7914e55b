-- Add farm_id column to document_permissions table if it doesn't exist

-- Set the search path to the appropriate schema
SET search_path TO site;

ALTER TABLE site.document_permissions ADD COLUMN IF NOT EXISTS farm_id UUID REFERENCES site.farms(id);

-- Copy data from tenant_id to farm_id if both columns exist
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_schema = 'site'
               AND table_name = 'document_permissions' 
               AND column_name = 'tenant_id') THEN

        -- Update farm_id with values from tenant_id
        -- This assumes there's a relationship between tenants and farms
        -- Adjust the join condition as needed for your data model
        UPDATE site.document_permissions dp
        SET farm_id = f.id
        FROM site.farms f
        WHERE dp.tenant_id = f.tenant_id;

        -- Drop the tenant_id column
        ALTER TABLE site.document_permissions DROP COLUMN IF EXISTS tenant_id;
    END IF;
END
$$;

-- Add NOT NULL constraint to farm_id if needed
-- Only do this after data migration to avoid constraint violations
ALTER TABLE site.document_permissions ALTER COLUMN farm_id SET NOT NULL;

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_document_permissions_farm_id ON site.document_permissions(farm_id);

-- Add comment explaining the purpose of this column
COMMENT ON COLUMN site.document_permissions.farm_id IS 'Reference to the farm this document permission belongs to';
