-- Move is_trial column from users table to subscription_plans table

-- Set the search path to the site schema
SET search_path TO site;

-- Add is_trial column to subscription_plans table
ALTER TABLE site.subscription_plans ADD COLUMN IF NOT EXISTS is_trial BOOLEAN DEFAULT FALSE;
COMMENT ON COLUMN site.subscription_plans.is_trial IS 'Whether this plan is a trial subscription plan';

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_subscription_plans_is_trial ON site.subscription_plans(is_trial);

-- Update existing subscription plans based on user data
-- This assumes there's at least one subscription plan that should be marked as a trial plan
-- You may need to adjust this logic based on your specific requirements
UPDATE site.subscription_plans 
SET is_trial = TRUE 
WHERE id = (
    SELECT id FROM site.subscription_plans ORDER BY created_at ASC LIMIT 1
);

-- Note: We're not removing the is_trial column from users table yet
-- This should be done in a separate migration after all code has been updated
