-- Create support_tickets table

-- Set the search path to the site schema
SET search_path TO site;

-- Enable the uuid-ossp extension if it's not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE TABLE IF NOT EXISTS support_tickets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  subject VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'open',
  priority VARCHAR(20) NOT NULL DEFAULT 'medium',
  category VARCHAR(100),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  farm_id UUID REFERENCES farms(id) ON DELETE SET NULL,
  assigned_to UUID REFERENCES users(id) ON DELETE SET NULL,
  resolved_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create index on user_id
CREATE INDEX IF NOT EXISTS idx_support_tickets_user_id ON support_tickets(user_id);

-- Create index on farm_id
CREATE INDEX IF NOT EXISTS idx_support_tickets_farm_id ON support_tickets(farm_id);

-- Create index on assigned_to
CREATE INDEX IF NOT EXISTS idx_support_tickets_assigned_to ON support_tickets(assigned_to);

-- Create index on status
CREATE INDEX IF NOT EXISTS idx_support_tickets_status ON support_tickets(status);

-- Add comment to table
COMMENT ON TABLE support_tickets IS 'Stores support tickets submitted by users';

-- Create support_ticket_comments table
CREATE TABLE IF NOT EXISTS support_ticket_comments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  content TEXT NOT NULL,
  ticket_id UUID NOT NULL REFERENCES support_tickets(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  is_internal BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create index on ticket_id
CREATE INDEX IF NOT EXISTS idx_support_ticket_comments_ticket_id ON support_ticket_comments(ticket_id);

-- Create index on user_id
CREATE INDEX IF NOT EXISTS idx_support_ticket_comments_user_id ON support_ticket_comments(user_id);

-- Add comment to table
COMMENT ON TABLE support_ticket_comments IS 'Stores comments on support tickets';

-- Create help_guides table
CREATE TABLE IF NOT EXISTS help_guides (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) NOT NULL UNIQUE,
  content TEXT NOT NULL,
  category VARCHAR(100) NOT NULL,
  subcategory VARCHAR(100),
  tags VARCHAR[] DEFAULT '{}',
  is_published BOOLEAN DEFAULT TRUE,
  "order" INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create index on category
CREATE INDEX IF NOT EXISTS idx_help_guides_category ON help_guides(category);

-- Create index on slug
CREATE INDEX IF NOT EXISTS idx_help_guides_slug ON help_guides(slug);

-- Add comment to table
COMMENT ON TABLE help_guides IS 'Stores help guides and documentation';

-- Create help_tips table
CREATE TABLE IF NOT EXISTS help_tips (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  page_path VARCHAR(255) NOT NULL,
  element_selector VARCHAR(255),
  position VARCHAR(20) DEFAULT 'right',
  "order" INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create index on page_path
CREATE INDEX IF NOT EXISTS idx_help_tips_page_path ON help_tips(page_path);

-- Add comment to table
COMMENT ON TABLE help_tips IS 'Stores help tips that appear as pop-ups on specific pages';

-- Create user_help_tip_dismissals table
CREATE TABLE IF NOT EXISTS user_help_tip_dismissals (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  help_tip_id UUID NOT NULL REFERENCES help_tips(id) ON DELETE CASCADE,
  dismissed_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id, help_tip_id)
);

-- Create index on user_id
CREATE INDEX IF NOT EXISTS idx_user_help_tip_dismissals_user_id ON user_help_tip_dismissals(user_id);

-- Create index on help_tip_id
CREATE INDEX IF NOT EXISTS idx_user_help_tip_dismissals_help_tip_id ON user_help_tip_dismissals(help_tip_id);

-- Add comment to table
COMMENT ON TABLE user_help_tip_dismissals IS 'Tracks which help tips have been dismissed by users';


