import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function applyMatrixForeignKey() {
  try {
    console.log('Adding foreign key constraint to MatrixRoomMapping...');
    
    // Set the schema
    const schema = process.env.DB_SCHEMA || 'site';
    console.log(`Using schema: ${schema}`);
    
    // Read the migration SQL file
    const sqlPath = path.join(__dirname, '../db/migrations/add_matrix_room_mapping_foreign_key.sql');
    
    if (!fs.existsSync(sqlPath)) {
      throw new Error(`Migration file not found: ${sqlPath}`);
    }
    
    const sql = fs.readFileSync(sqlPath, 'utf8');
    console.log('Migration file loaded successfully');
    
    // Check if the foreign key constraint already exists
    const checkConstraintQuery = `
      SELECT constraint_name 
      FROM information_schema.table_constraints 
      WHERE table_schema = '${schema}' 
      AND table_name = 'matrix_room_mappings' 
      AND constraint_name = 'fk_matrix_room_mapping_conversation_id';
    `;
    
    const [results] = await sequelize.query(checkConstraintQuery);
    
    if (results.length > 0) {
      console.log('Foreign key constraint already exists. Skipping migration.');
      return;
    }
    
    // Check if both tables exist
    const checkTablesQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = '${schema}' 
      AND table_name IN ('matrix_room_mappings', 'chat_conversations');
    `;
    
    const [tables] = await sequelize.query(checkTablesQuery);
    
    if (tables.length !== 2) {
      throw new Error('Required tables (matrix_room_mappings, chat_conversations) do not exist');
    }
    
    console.log('Both required tables exist. Proceeding with migration...');
    
    // Execute the migration in a transaction
    const transaction = await sequelize.transaction();
    
    try {
      // Set search path
      await sequelize.query(`SET search_path TO ${schema};`, { transaction });
      
      // Execute the SQL migration
      await sequelize.query(sql, { transaction });
      
      await transaction.commit();
      console.log('Foreign key constraint added successfully!');
      
      // Verify the constraint was created
      const [verifyResults] = await sequelize.query(checkConstraintQuery);
      if (verifyResults.length > 0) {
        console.log('✅ Foreign key constraint verified: fk_matrix_room_mapping_conversation_id');
      }
      
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
    
  } catch (error) {
    console.error('Error applying foreign key migration:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

applyMatrixForeignKey();
