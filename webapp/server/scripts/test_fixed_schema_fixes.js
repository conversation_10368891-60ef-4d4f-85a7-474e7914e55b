import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function testFixedSchemaMigrations() {
  try {
    console.log('Testing fixed schema migrations...');
    
    // Define the migration files to run in order
    const migrationFiles = [
      'add_farm_billing_columns.sql',
      'add_user_columns.sql',
      'add_user_farm_columns.sql'
    ];
    
    // Run each migration file
    for (const file of migrationFiles) {
      console.log(`Testing migration: ${file}`);
      
      // Read the SQL file
      const sqlFilePath = path.join(__dirname, '../db', file);
      const sql = fs.readFileSync(sqlFilePath, 'utf8');
      
      try {
        // Execute the SQL
        await sequelize.query(sql);
        console.log(`Migration ${file} executed successfully`);
      } catch (error) {
        console.error(`Error executing migration ${file}:`, error.message);
        // Continue with next file even if this one fails
      }
    }
    
    console.log('All schema migrations tested');
    
    // Now check if the columns were added correctly
    const schema = process.env.DB_SCHEMA || 'site';
    console.log(`\nVerifying columns in schema: ${schema}`);
    
    // Check farms table columns
    console.log('\nChecking farms table columns:');
    const farmColumns = [
      'subscription_plan_id', 'subscription_status', 'subscription_start_date', 
      'subscription_end_date', 'billing_email', 'billing_address', 'billing_city', 
      'billing_state', 'billing_zip_code', 'billing_country', 'payment_method_id', 
      'stripe_customer_id'
    ];
    
    for (const column of farmColumns) {
      try {
        const result = await sequelize.query(
          `SELECT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_schema = '${schema}' 
            AND table_name = 'farms' 
            AND column_name = '${column}'
          );`,
          { type: sequelize.QueryTypes.SELECT }
        );
        
        const columnExists = result[0].exists;
        console.log(`  - Column farms.${column}: ${columnExists ? 'EXISTS' : 'MISSING'}`);
      } catch (error) {
        console.error(`  Error checking column farms.${column}:`, error.message);
      }
    }
    
    // Check users table columns
    console.log('\nChecking users table columns:');
    const userColumns = [
      'is_business_owner', 'is_approved', 'subscription_plan_id', 'user_type'
    ];
    
    for (const column of userColumns) {
      try {
        const result = await sequelize.query(
          `SELECT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_schema = '${schema}' 
            AND table_name = 'users' 
            AND column_name = '${column}'
          );`,
          { type: sequelize.QueryTypes.SELECT }
        );
        
        const columnExists = result[0].exists;
        console.log(`  - Column users.${column}: ${columnExists ? 'EXISTS' : 'MISSING'}`);
      } catch (error) {
        console.error(`  Error checking column users.${column}:`, error.message);
      }
    }
    
    // Check user_farms table columns
    console.log('\nChecking user_farms table columns:');
    const userFarmColumns = [
      'permissions', 'is_billing_contact', 'role'
    ];
    
    for (const column of userFarmColumns) {
      try {
        const result = await sequelize.query(
          `SELECT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_schema = '${schema}' 
            AND table_name = 'user_farms' 
            AND column_name = '${column}'
          );`,
          { type: sequelize.QueryTypes.SELECT }
        );
        
        const columnExists = result[0].exists;
        console.log(`  - Column user_farms.${column}: ${columnExists ? 'EXISTS' : 'MISSING'}`);
      } catch (error) {
        console.error(`  Error checking column user_farms.${column}:`, error.message);
      }
    }
    
    console.log('\nSchema migration test completed.');
  } catch (error) {
    console.error('Error testing schema migrations:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

// Run the test
testFixedSchemaMigrations()
  .then(() => {
    console.log('Test completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });