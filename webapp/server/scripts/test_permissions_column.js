import { sequelize } from '../config/database.js';

async function testPermissionsColumn() {
  try {
    console.log('Testing if permissions column exists in user_farms table...');
    
    // Get the current schema
    const currentSchema = process.env.DB_SCHEMA || 'site';
    console.log(`Current schema from environment: ${currentSchema}`);
    
    // Check if the permissions column exists in the user_farms table
    const columnCheck = await sequelize.query(
      `SELECT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = '${currentSchema}' 
        AND table_name = 'user_farms' 
        AND column_name = 'permissions'
      );`,
      { type: sequelize.QueryTypes.SELECT }
    );
    
    const columnExists = columnCheck[0].exists;
    console.log(`permissions column exists in user_farms table: ${columnExists}`);
    
    if (columnExists) {
      // Check the data type of the column
      const dataTypeCheck = await sequelize.query(
        `SELECT data_type, udt_name 
         FROM information_schema.columns 
         WHERE table_schema = '${currentSchema}' 
         AND table_name = 'user_farms' 
         AND column_name = 'permissions';`,
        { type: sequelize.QueryTypes.SELECT }
      );
      
      console.log(`Column data type: ${dataTypeCheck[0].data_type}`);
      console.log(`UDT name: ${dataTypeCheck[0].udt_name}`);
      
      console.log('Migration was successful!');
    } else {
      console.error('permissions column does not exist. Migration may not have been run.');
    }
    
  } catch (error) {
    console.error('Error testing permissions column:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

// Run the test
testPermissionsColumn()
  .then(() => {
    console.log('Test completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });