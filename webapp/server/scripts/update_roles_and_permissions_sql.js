import { sequelize } from '../config/database.js';
import dotenv from 'dotenv';
import crypto from 'crypto';

// Load environment variables
dotenv.config();

async function updateRolesAndPermissions() {
  const transaction = await sequelize.transaction();

  try {
    console.log('Starting update of roles and permissions...');

    // 1. Create the roles table if it doesn't exist
    console.log('Creating roles table if it doesn\'t exist...');
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS site.roles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        farm_id UUID,
        name VARCHAR(50) NOT NULL,
        description VARCHAR(255),
        is_system_role BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `, { transaction });

    // 2. Update role names to be more user-friendly
    console.log('Updating role names to be more user-friendly...');

    // Define the mapping of old role names to new user-friendly names
    const roleNameMapping = {
      'farm_owner': 'Farm Owner',
      'farm_admin': 'Farm Administrator',
      'farm_manager': 'Farm Manager',
      'farm_employee': 'Farm Worker',
      'accountant': 'Financial Manager'
    };

    // For each role mapping, insert the new role if it doesn't exist
    for (const [oldName, newName] of Object.entries(roleNameMapping)) {
      console.log(`Ensuring role "${newName}" exists...`);

      // Check if the role already exists
      const [existingRoles] = await sequelize.query(`
        SELECT id FROM site.roles WHERE name = $1;
      `, {
        bind: [newName],
        transaction
      });

      if (existingRoles.length === 0) {
        // Create the role with a generated UUID
        console.log(`Creating role "${newName}"...`);
        const uuid = crypto.randomUUID();
        await sequelize.query(`
          INSERT INTO site.roles (id, name, description, is_system_role, created_at, updated_at)
          VALUES ($1, $2, $3, TRUE, NOW(), NOW());
        `, {
          bind: [uuid, newName, `${newName} role with appropriate permissions`],
          transaction
        });
      } else {
        console.log(`Role "${newName}" already exists.`);
      }
    }

    // 3. Update default permissions for global roles
    console.log('Updating default permissions for global roles...');

    // Define the default permissions for each role and feature
    const defaultPermissions = {
      'Farm Owner': {
        'dashboard': { view: true, create: true, edit: true, delete: true },
        'farms': { view: true, create: true, edit: true, delete: true },
        'fields': { view: true, create: true, edit: true, delete: true },
        'crops': { view: true, create: true, edit: true, delete: true },
        'livestock': { view: true, create: true, edit: true, delete: true },
        'equipment': { view: true, create: true, edit: true, delete: true },
        'inventory': { view: true, create: true, edit: true, delete: true },
        'employees': { view: true, create: true, edit: true, delete: true },
        'finances': { view: true, create: true, edit: true, delete: true },
        'reports': { view: true, create: true, edit: true, delete: true },
        'settings': { view: true, create: true, edit: true, delete: true },
        'roles': { view: true, create: true, edit: true, delete: true },
        'transport': { view: true, create: true, edit: true, delete: true },
        'receipts': { view: true, create: true, edit: true, delete: true }
      },
      'Farm Administrator': {
        'dashboard': { view: true, create: true, edit: true, delete: false },
        'farms': { view: true, create: false, edit: true, delete: false },
        'fields': { view: true, create: true, edit: true, delete: true },
        'crops': { view: true, create: true, edit: true, delete: true },
        'livestock': { view: true, create: true, edit: true, delete: true },
        'equipment': { view: true, create: true, edit: true, delete: true },
        'inventory': { view: true, create: true, edit: true, delete: true },
        'employees': { view: true, create: true, edit: true, delete: true },
        'finances': { view: true, create: true, edit: true, delete: true },
        'reports': { view: true, create: true, edit: true, delete: true },
        'settings': { view: true, create: false, edit: false, delete: false },
        'roles': { view: true, create: false, edit: false, delete: false },
        'transport': { view: true, create: true, edit: true, delete: true },
        'receipts': { view: true, create: true, edit: true, delete: true }
      },
      'Farm Manager': {
        'dashboard': { view: true, create: true, edit: true, delete: false },
        'farms': { view: true, create: false, edit: false, delete: false },
        'fields': { view: true, create: true, edit: true, delete: false },
        'crops': { view: true, create: true, edit: true, delete: false },
        'livestock': { view: true, create: true, edit: true, delete: false },
        'equipment': { view: true, create: true, edit: true, delete: false },
        'inventory': { view: true, create: true, edit: true, delete: false },
        'employees': { view: true, create: false, edit: true, delete: false },
        'finances': { view: true, create: true, edit: true, delete: false },
        'reports': { view: true, create: true, edit: false, delete: false },
        'settings': { view: false, create: false, edit: false, delete: false },
        'roles': { view: false, create: false, edit: false, delete: false },
        'transport': { view: true, create: true, edit: true, delete: false },
        'receipts': { view: true, create: true, edit: true, delete: false }
      },
      'Farm Worker': {
        'dashboard': { view: true, create: false, edit: false, delete: false },
        'farms': { view: true, create: false, edit: false, delete: false },
        'fields': { view: true, create: false, edit: false, delete: false },
        'crops': { view: true, create: false, edit: false, delete: false },
        'livestock': { view: true, create: false, edit: false, delete: false },
        'equipment': { view: true, create: false, edit: false, delete: false },
        'inventory': { view: true, create: false, edit: false, delete: false },
        'employees': { view: false, create: false, edit: false, delete: false },
        'finances': { view: false, create: false, edit: false, delete: false },
        'reports': { view: false, create: false, edit: false, delete: false },
        'settings': { view: false, create: false, edit: false, delete: false },
        'roles': { view: false, create: false, edit: false, delete: false },
        'transport': { view: true, create: false, edit: false, delete: false },
        'receipts': { view: true, create: true, edit: false, delete: false }
      },
      'Financial Manager': {
        'dashboard': { view: true, create: false, edit: false, delete: false },
        'farms': { view: true, create: false, edit: false, delete: false },
        'fields': { view: false, create: false, edit: false, delete: false },
        'crops': { view: false, create: false, edit: false, delete: false },
        'livestock': { view: false, create: false, edit: false, delete: false },
        'equipment': { view: false, create: false, edit: false, delete: false },
        'inventory': { view: true, create: false, edit: false, delete: false },
        'employees': { view: true, create: false, edit: false, delete: false },
        'finances': { view: true, create: true, edit: true, delete: false },
        'reports': { view: true, create: true, edit: false, delete: false },
        'settings': { view: false, create: false, edit: false, delete: false },
        'roles': { view: false, create: false, edit: false, delete: false },
        'transport': { view: true, create: false, edit: false, delete: false },
        'receipts': { view: true, create: false, edit: false, delete: false }
      }
    };

    // Get the reverse mapping from new names to old names
    const reverseRoleMapping = {};
    for (const [oldName, newName] of Object.entries(roleNameMapping)) {
      reverseRoleMapping[newName] = oldName;
    }

    // For each role, update or create the permissions for each feature
    for (const [roleName, features] of Object.entries(defaultPermissions)) {
      const oldRoleName = reverseRoleMapping[roleName];

      console.log(`Updating permissions for role "${roleName}" (${oldRoleName})...`);

      // For each feature, update or create the permission
      for (const [featureName, permissions] of Object.entries(features)) {
        // Check if permission exists
        const [existingPermissions] = await sequelize.query(`
          SELECT id FROM site.role_permissions 
          WHERE role_name = $1 AND farm_id IS NULL AND feature = $2;
        `, {
          bind: [oldRoleName, featureName],
          transaction
        });

        if (existingPermissions.length > 0) {
          // Update existing permission
          console.log(`Updating permission for role "${roleName}", feature "${featureName}"`);
          await sequelize.query(`
            UPDATE site.role_permissions
            SET can_view = $1, can_create = $2, can_edit = $3, can_delete = $4, updated_at = NOW()
            WHERE role_name = $5 AND farm_id IS NULL AND feature = $6;
          `, {
            bind: [
              permissions.view, 
              permissions.create, 
              permissions.edit, 
              permissions.delete, 
              oldRoleName, 
              featureName
            ],
            transaction
          });
        } else {
          // Create new permission with a generated UUID
          console.log(`Creating permission for role "${roleName}", feature "${featureName}"`);
          const uuid = crypto.randomUUID();
          await sequelize.query(`
            INSERT INTO site.role_permissions 
            (id, role_name, farm_id, feature, can_view, can_create, can_edit, can_delete, created_at, updated_at)
            VALUES ($1, $2, NULL, $3, $4, $5, $6, $7, NOW(), NOW());
          `, {
            bind: [
              uuid,
              oldRoleName, 
              featureName, 
              permissions.view, 
              permissions.create, 
              permissions.edit, 
              permissions.delete
            ],
            transaction
          });
        }
      }
    }

    // Commit the transaction
    await transaction.commit();
    console.log('Successfully updated roles and permissions');

  } catch (error) {
    // Rollback the transaction in case of error
    await transaction.rollback();
    console.error('Error updating roles and permissions:', error);
    process.exit(1);
  }
}

// Run the function
updateRolesAndPermissions()
  .then(() => {
    console.log('Update completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });
