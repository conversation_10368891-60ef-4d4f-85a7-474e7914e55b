import { sequelize } from '../config/database.js';

async function testIsApprovedColumn() {
  try {
    console.log('Testing if is_approved column exists in user_farms table...');
    
    // Get the current schema
    const currentSchema = process.env.DB_SCHEMA || 'site';
    console.log(`Current schema from environment: ${currentSchema}`);
    
    // Check if the is_approved column exists in the user_farms table
    const columnCheck = await sequelize.query(
      `SELECT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = '${currentSchema}' 
        AND table_name = 'user_farms' 
        AND column_name = 'is_approved'
      );`,
      { type: sequelize.QueryTypes.SELECT }
    );
    
    const columnExists = columnCheck[0].exists;
    console.log(`is_approved column exists in user_farms table: ${columnExists}`);
    
    if (columnExists) {
      // Check the data type of the column
      const dataTypeCheck = await sequelize.query(
        `SELECT data_type 
         FROM information_schema.columns 
         WHERE table_schema = '${currentSchema}' 
         AND table_name = 'user_farms' 
         AND column_name = 'is_approved';`,
        { type: sequelize.QueryTypes.SELECT }
      );
      
      console.log(`Column data type: ${dataTypeCheck[0].data_type}`);
      
      // Check if the index exists
      const indexCheck = await sequelize.query(
        `SELECT EXISTS (
          SELECT 1 
          FROM pg_indexes 
          WHERE schemaname = '${currentSchema}' 
          AND tablename = 'user_farms' 
          AND indexname = 'idx_user_farms_is_approved'
        );`,
        { type: sequelize.QueryTypes.SELECT }
      );
      
      const indexExists = indexCheck[0].exists;
      console.log(`Index idx_user_farms_is_approved exists: ${indexExists}`);
      
      console.log('Migration was successful!');
    } else {
      console.error('is_approved column does not exist. Migration may not have been run.');
    }
  } catch (error) {
    console.error('Error testing is_approved column:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

// Run the test
testIsApprovedColumn();
