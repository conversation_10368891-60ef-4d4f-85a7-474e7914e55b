import User from '../models/User.js';
import { registerMatrixUser, getMatrixUser, getNxtAcreUserIdToMatrixUserId, setDisplayName, setAvatarUrl } from '../utils/matrixUtils.js';
import { sequelize } from '../config/database.js';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Synchronize NxtAcre users with Matrix Synapse
 * This script creates Matrix user accounts for all NxtAcre users
 */
async function syncUsersToMatrix() {
  console.log('Starting user synchronization with Matrix Synapse...');

  try {
    // Get all active users from NxtAcre
    const users = await User.findAll({
      where: {
        is_active: true
      },
      attributes: ['id', 'email', 'first_name', 'last_name']
    });

    console.log(`Found ${users.length} active users to synchronize`);

    // Track statistics
    let created = 0;
    let skipped = 0;
    let errors = 0;

    // Process each user
    for (const user of users) {
      try {
        // Generate Matrix user ID
        const matrixUserId = getNxtAcreUserIdToMatrixUserId(user.id);

        // Check if user already exists in Matrix
        try {
          await getMatrixUser(matrixUserId);
          console.log(`User ${user.email} (${matrixUserId}) already exists in Matrix, skipping`);
          skipped++;
          continue;
        } catch (error) {
          // User doesn't exist, continue with creation
          if (error.response && error.response.status === 404) {
            console.log(`User ${user.email} (${matrixUserId}) doesn't exist in Matrix, creating...`);
          } else {
            // Unexpected error
            console.error(`Error checking if user ${user.email} exists in Matrix:`, error.message);
            errors++;
            continue;
          }
        }

        // Create user in Matrix
        const username = `nxtacre_${user.id}`;
        const displayName = `${user.first_name} ${user.last_name}`;

        // Register user without password (will use JWT for authentication)
        const result = await registerMatrixUser(username, null, false);

        // Store the access token in the user record
        if (result.access_token) {
          await user.update({ matrix_token: result.access_token });
          console.log(`Updated matrix_token for user ${user.email}`);
        }

        console.log(`Created Matrix user for ${user.email}: ${result.user_id}`);

        // Set display name for the user
        try {
          await setDisplayName(result.user_id, displayName);
          console.log(`Set display name for ${result.user_id} to "${displayName}"`);
        } catch (displayNameError) {
          console.error(`Error setting display name for ${result.user_id}:`, displayNameError.message);
        }

        // Set avatar URL if available (using Gravatar as an example)
        try {
          const emailHash = require('crypto').createHash('md5').update(user.email.toLowerCase().trim()).digest('hex');
          const avatarUrl = `https://www.gravatar.com/avatar/${emailHash}?d=identicon`;
          await setAvatarUrl(result.user_id, avatarUrl);
          console.log(`Set avatar URL for ${result.user_id}`);
        } catch (avatarError) {
          console.error(`Error setting avatar URL for ${result.user_id}:`, avatarError.message);
        }

        created++;

      } catch (error) {
        console.error(`Error creating Matrix user for ${user.email}:`, error.message);
        errors++;
      }
    }

    console.log('User synchronization completed:');
    console.log(`- Created: ${created}`);
    console.log(`- Skipped (already exist): ${skipped}`);
    console.log(`- Errors: ${errors}`);

  } catch (error) {
    console.error('Error synchronizing users with Matrix:', error);
  }
}

// Run the script if called directly
if (process.argv[1].endsWith('syncUsersToMatrix.js')) {
  syncUsersToMatrix()
    .then(() => {
      console.log('Synchronization script completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('Error running synchronization script:', error);
      process.exit(1);
    });
}

export default syncUsersToMatrix;
