/**
 * Migration script to replace tenant structures with farms
 * 
 * This script:
 * 1. Renames the tenants table to farms (if it doesn't already exist)
 * 2. Renames the tenant_admins table to farm_admins
 * 3. Renames tenant_id columns to farm_id in various tables
 * 4. Renames tenant_storage_usage table to farm_storage_usage
 */

import { sequelize } from '../config/database.js';
import dotenv from 'dotenv';

dotenv.config();

const migrateTenantsToFarms = async () => {
  const transaction = await sequelize.transaction();

  try {
    console.log('Starting migration: replacing tenant structures with farms');

    // Check if farms table already exists
    const [farmsExist] = await sequelize.query(
      `SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = '${process.env.DB_SCHEMA || 'site'}' 
        AND table_name = 'farms'
      );`,
      { transaction }
    );
    
    // Check if tenants table exists
    const [tenantsExist] = await sequelize.query(
      `SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = '${process.env.DB_SCHEMA || 'site'}' 
        AND table_name = 'tenants'
      );`,
      { transaction }
    );

    // If tenants table exists and farms table doesn't exist, rename tenants to farms
    if (tenantsExist[0].exists && !farmsExist[0].exists) {
      console.log('Renaming tenants table to farms...');
      await sequelize.query(
        `ALTER TABLE ${process.env.DB_SCHEMA || 'site'}.tenants RENAME TO farms;`,
        { transaction }
      );
      console.log('Renamed tenants table to farms');
    } else if (tenantsExist[0].exists && farmsExist[0].exists) {
      console.log('Both tenants and farms tables exist. Merging data...');
      
      // Copy data from tenants to farms
      await sequelize.query(
        `INSERT INTO ${process.env.DB_SCHEMA || 'site'}.farms (
          id, name, subscription_plan_id, subscription_status, subscription_start_date, 
          subscription_end_date, billing_email, billing_address, billing_city, 
          billing_state, billing_zip_code, billing_country, payment_method_id, 
          stripe_customer_id, created_at, updated_at
        )
        SELECT 
          id, name, subscription_plan_id, subscription_status, subscription_start_date, 
          subscription_end_date, billing_email, billing_address, billing_city, 
          billing_state, billing_zip_code, billing_country, payment_method_id, 
          stripe_customer_id, created_at, updated_at
        FROM ${process.env.DB_SCHEMA || 'site'}.tenants
        ON CONFLICT (id) DO NOTHING;`,
        { transaction }
      );
      
      console.log('Data copied from tenants to farms');
    } else {
      console.log('Tenants table does not exist, skipping rename');
    }

    // Check if tenant_admins table exists
    const [tenantAdminsExist] = await sequelize.query(
      `SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = '${process.env.DB_SCHEMA || 'site'}' 
        AND table_name = 'tenant_admins'
      );`,
      { transaction }
    );

    // Rename tenant_admins table to farm_admins if it exists
    if (tenantAdminsExist[0].exists) {
      console.log('Renaming tenant_admins table to farm_admins...');
      
      // Check if farm_admins table already exists
      const [farmAdminsExist] = await sequelize.query(
        `SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = '${process.env.DB_SCHEMA || 'site'}' 
          AND table_name = 'farm_admins'
        );`,
        { transaction }
      );
      
      if (!farmAdminsExist[0].exists) {
        // Rename the table
        await sequelize.query(
          `ALTER TABLE ${process.env.DB_SCHEMA || 'site'}.tenant_admins RENAME TO farm_admins;`,
          { transaction }
        );
        
        // Rename the tenant_id column to farm_id
        await sequelize.query(
          `ALTER TABLE ${process.env.DB_SCHEMA || 'site'}.farm_admins RENAME COLUMN tenant_id TO farm_id;`,
          { transaction }
        );
        
        console.log('Renamed tenant_admins table to farm_admins and tenant_id column to farm_id');
      } else {
        console.log('farm_admins table already exists, copying data...');
        
        // Copy data from tenant_admins to farm_admins
        await sequelize.query(
          `INSERT INTO ${process.env.DB_SCHEMA || 'site'}.farm_admins (
            farm_id, user_id, role, created_at, updated_at
          )
          SELECT 
            tenant_id, user_id, role, created_at, updated_at
          FROM ${process.env.DB_SCHEMA || 'site'}.tenant_admins
          ON CONFLICT (farm_id, user_id) DO NOTHING;`,
          { transaction }
        );
        
        console.log('Data copied from tenant_admins to farm_admins');
      }
    } else {
      console.log('tenant_admins table does not exist, skipping rename');
    }

    // Check if tenant_storage_usage table exists
    const [tenantStorageUsageExist] = await sequelize.query(
      `SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = '${process.env.DB_SCHEMA || 'site'}' 
        AND table_name = 'tenant_storage_usage'
      );`,
      { transaction }
    );

    // Rename tenant_storage_usage table to farm_storage_usage if it exists
    if (tenantStorageUsageExist[0].exists) {
      console.log('Renaming tenant_storage_usage table to farm_storage_usage...');
      
      // Check if farm_storage_usage table already exists
      const [farmStorageUsageExist] = await sequelize.query(
        `SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = '${process.env.DB_SCHEMA || 'site'}' 
          AND table_name = 'farm_storage_usage'
        );`,
        { transaction }
      );
      
      if (!farmStorageUsageExist[0].exists) {
        // Rename the table
        await sequelize.query(
          `ALTER TABLE ${process.env.DB_SCHEMA || 'site'}.tenant_storage_usage RENAME TO farm_storage_usage;`,
          { transaction }
        );
        
        // Rename the tenant_id column to farm_id
        await sequelize.query(
          `ALTER TABLE ${process.env.DB_SCHEMA || 'site'}.farm_storage_usage RENAME COLUMN tenant_id TO farm_id;`,
          { transaction }
        );
        
        console.log('Renamed tenant_storage_usage table to farm_storage_usage and tenant_id column to farm_id');
      } else {
        console.log('farm_storage_usage table already exists, copying data...');
        
        // Copy data from tenant_storage_usage to farm_storage_usage
        await sequelize.query(
          `INSERT INTO ${process.env.DB_SCHEMA || 'site'}.farm_storage_usage (
            id, farm_id, total_bytes_used, document_count, external_document_count, 
            last_calculated_at, created_at, updated_at
          )
          SELECT 
            id, tenant_id, total_bytes_used, document_count, external_document_count, 
            last_calculated_at, created_at, updated_at
          FROM ${process.env.DB_SCHEMA || 'site'}.tenant_storage_usage
          ON CONFLICT (id) DO NOTHING;`,
          { transaction }
        );
        
        console.log('Data copied from tenant_storage_usage to farm_storage_usage');
      }
    } else {
      console.log('tenant_storage_usage table does not exist, skipping rename');
    }

    // Update tenant_id to farm_id in users table
    console.log('Updating tenant_id to farm_id in users table...');
    
    // Check if users table has tenant_id column
    const [usersTenantIdExists] = await sequelize.query(
      `SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = '${process.env.DB_SCHEMA || 'site'}' 
        AND table_name = 'users' 
        AND column_name = 'tenant_id'
      );`,
      { transaction }
    );
    
    // Check if users table has farm_id column
    const [usersFarmIdExists] = await sequelize.query(
      `SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = '${process.env.DB_SCHEMA || 'site'}' 
        AND table_name = 'users' 
        AND column_name = 'farm_id'
      );`,
      { transaction }
    );
    
    if (usersTenantIdExists[0].exists) {
      if (!usersFarmIdExists[0].exists) {
        // Rename tenant_id to farm_id in users table
        await sequelize.query(
          `ALTER TABLE ${process.env.DB_SCHEMA || 'site'}.users RENAME COLUMN tenant_id TO farm_id;`,
          { transaction }
        );
        console.log('Renamed tenant_id to farm_id in users table');
      } else {
        // Copy tenant_id values to farm_id
        await sequelize.query(
          `UPDATE ${process.env.DB_SCHEMA || 'site'}.users 
           SET farm_id = tenant_id 
           WHERE tenant_id IS NOT NULL AND farm_id IS NULL;`,
          { transaction }
        );
        console.log('Copied tenant_id values to farm_id in users table');
      }
    } else {
      console.log('users table does not have tenant_id column, skipping update');
    }

    // Update tenant_id to farm_id in subscription_transactions table
    console.log('Updating tenant_id to farm_id in subscription_transactions table...');
    
    // Check if subscription_transactions table exists
    const [subscriptionTransactionsExist] = await sequelize.query(
      `SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = '${process.env.DB_SCHEMA || 'site'}' 
        AND table_name = 'subscription_transactions'
      );`,
      { transaction }
    );
    
    if (subscriptionTransactionsExist[0].exists) {
      // Check if subscription_transactions table has tenant_id column
      const [subscriptionTransactionsTenantIdExists] = await sequelize.query(
        `SELECT EXISTS (
          SELECT FROM information_schema.columns 
          WHERE table_schema = '${process.env.DB_SCHEMA || 'site'}' 
          AND table_name = 'subscription_transactions' 
          AND column_name = 'tenant_id'
        );`,
        { transaction }
      );
      
      if (subscriptionTransactionsTenantIdExists[0].exists) {
        // Rename tenant_id to farm_id in subscription_transactions table
        await sequelize.query(
          `ALTER TABLE ${process.env.DB_SCHEMA || 'site'}.subscription_transactions RENAME COLUMN tenant_id TO farm_id;`,
          { transaction }
        );
        console.log('Renamed tenant_id to farm_id in subscription_transactions table');
      } else {
        console.log('subscription_transactions table does not have tenant_id column, skipping update');
      }
    } else {
      console.log('subscription_transactions table does not exist, skipping update');
    }

    // Update tenant_id to farm_id in feature_usage table
    console.log('Updating tenant_id to farm_id in feature_usage table...');
    
    // Check if feature_usage table exists
    const [featureUsageExist] = await sequelize.query(
      `SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = '${process.env.DB_SCHEMA || 'site'}' 
        AND table_name = 'feature_usage'
      );`,
      { transaction }
    );
    
    if (featureUsageExist[0].exists) {
      // Check if feature_usage table has tenant_id column
      const [featureUsageTenantIdExists] = await sequelize.query(
        `SELECT EXISTS (
          SELECT FROM information_schema.columns 
          WHERE table_schema = '${process.env.DB_SCHEMA || 'site'}' 
          AND table_name = 'feature_usage' 
          AND column_name = 'tenant_id'
        );`,
        { transaction }
      );
      
      if (featureUsageTenantIdExists[0].exists) {
        // Rename tenant_id to farm_id in feature_usage table
        await sequelize.query(
          `ALTER TABLE ${process.env.DB_SCHEMA || 'site'}.feature_usage RENAME COLUMN tenant_id TO farm_id;`,
          { transaction }
        );
        console.log('Renamed tenant_id to farm_id in feature_usage table');
      } else {
        console.log('feature_usage table does not have tenant_id column, skipping update');
      }
    } else {
      console.log('feature_usage table does not exist, skipping update');
    }

    // Commit the transaction
    await transaction.commit();
    console.log('Migration completed successfully');
    
    return { success: true, message: 'Migration completed successfully' };
  } catch (error) {
    // Rollback the transaction in case of error
    await transaction.rollback();
    console.error('Migration failed:', error);
    
    return { success: false, message: `Migration failed: ${error.message}` };
  }
};

// Run the migration
migrateTenantsToFarms()
  .then(result => {
    console.log(result.message);
    process.exit(result.success ? 0 : 1);
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });