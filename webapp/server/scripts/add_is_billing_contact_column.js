import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function addIsBillingContactColumn() {
  try {
    console.log('Starting to add is_billing_contact column to user_farms table...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../db/add_is_billing_contact_column.sql');
    console.log(`Reading SQL file from: ${sqlFilePath}`);
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    console.log('Executing SQL...');
    await sequelize.query(sql);
    
    console.log('is_billing_contact column added successfully to user_farms table');
  } catch (error) {
    console.error('Error adding is_billing_contact column:', error);
    process.exit(1);
  }
}

// Run the function
addIsBillingContactColumn()
  .then(() => {
    console.log('Migration completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });