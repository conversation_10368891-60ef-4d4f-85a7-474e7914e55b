import { sequelize } from '../config/database.js';

async function testStorageQuotaColumn() {
  try {
    console.log('Testing storage quota column migration...');

    // Get the current schema
    const currentSchema = process.env.DB_SCHEMA || 'site';
    console.log(`Current schema from environment: ${currentSchema}`);

    // Define the columns to check
    const columnsToCheck = [
      'storage_quota_gb',
      'max_file_size_mb'
    ];

    // Check each column
    console.log(`\nChecking columns for table: subscription_plans`);

    for (const column of columnsToCheck) {
      try {
        const result = await sequelize.query(
          `SELECT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_schema = '${currentSchema}' 
            AND table_name = 'subscription_plans' 
            AND column_name = '${column}'
          );`,
          { type: sequelize.QueryTypes.SELECT }
        );

        const columnExists = result[0].exists;
        console.log(`  - Column ${column}: ${columnExists ? 'EXISTS' : 'MISSING'}`);

        if (!columnExists) {
          console.error(`    WARNING: Column ${column} is missing from table subscription_plans`);
        }
      } catch (error) {
        console.error(`  Error checking column ${column} in table subscription_plans:`, error.message);
      }
    }

    // Check if the columns have the correct values
    try {
      const plans = await sequelize.query(
        `SELECT name, storage_quota_gb, max_file_size_mb 
         FROM ${currentSchema}.subscription_plans;`,
        { type: sequelize.QueryTypes.SELECT }
      );

      console.log('\nSubscription plans with storage quotas:');
      plans.forEach(plan => {
        console.log(`  - ${plan.name}: ${plan.storage_quota_gb} GB storage, ${plan.max_file_size_mb} MB max file size`);
      });

      // Check if all plans have the expected values
      const expectedValues = {
        'Basic': { storage_quota_gb: 5, max_file_size_mb: 25 },
        'Standard': { storage_quota_gb: 20, max_file_size_mb: 50 },
        'Premium': { storage_quota_gb: 100, max_file_size_mb: 100 },
        'Enterprise': { storage_quota_gb: 1000, max_file_size_mb: 500 }
      };

      let allValuesCorrect = true;
      plans.forEach(plan => {
        const expected = expectedValues[plan.name];
        if (expected) {
          if (plan.storage_quota_gb != expected.storage_quota_gb || 
              plan.max_file_size_mb != expected.max_file_size_mb) {
            console.error(`    WARNING: Plan ${plan.name} has incorrect values. Expected: ${expected.storage_quota_gb} GB storage, ${expected.max_file_size_mb} MB max file size`);
            allValuesCorrect = false;
          }
        }
      });

      if (allValuesCorrect) {
        console.log('\n✅ All subscription plans have the correct storage quota values!');
      }
    } catch (error) {
      console.error('  Error checking subscription plan values:', error.message);
    }

    console.log('\nStorage quota column migration test completed.');
  } catch (error) {
    console.error('Error testing storage quota column migration:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

// Run the test
testStorageQuotaColumn()
  .then(() => {
    console.log('Test completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });
