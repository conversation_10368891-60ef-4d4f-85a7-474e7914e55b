import { sequelize } from '../config/database.js';

async function testUserIdColumns() {
  try {
    console.log('Testing if user_id columns exist in vendors, suppliers, and vets tables...');
    
    // Get the current schema
    const currentSchema = process.env.DB_SCHEMA || 'site';
    console.log(`Current schema from environment: ${currentSchema}`);
    
    // Define the tables to check
    const tables = ['vendors', 'suppliers', 'vets'];
    
    // Check each table
    for (const table of tables) {
      // Check if the user_id column exists
      const columnCheck = await sequelize.query(
        `SELECT EXISTS (
          SELECT 1 
          FROM information_schema.columns 
          WHERE table_schema = '${currentSchema}' 
          AND table_name = '${table}' 
          AND column_name = 'user_id'
        );`,
        { type: sequelize.QueryTypes.SELECT }
      );
      
      const columnExists = columnCheck[0].exists;
      console.log(`user_id column exists in ${table} table: ${columnExists}`);
      
      if (columnExists) {
        // Check the data type of the column
        const dataTypeCheck = await sequelize.query(
          `SELECT data_type, udt_name 
           FROM information_schema.columns 
           WHERE table_schema = '${currentSchema}' 
           AND table_name = '${table}' 
           AND column_name = 'user_id';`,
          { type: sequelize.QueryTypes.SELECT }
        );
        
        console.log(`Column data type in ${table}: ${dataTypeCheck[0].data_type}`);
        console.log(`UDT name in ${table}: ${dataTypeCheck[0].udt_name}`);
        
        // Check if the index exists
        const indexCheck = await sequelize.query(
          `SELECT EXISTS (
            SELECT 1 
            FROM pg_indexes 
            WHERE schemaname = '${currentSchema}' 
            AND tablename = '${table}' 
            AND indexname = 'idx_${table}_user_id'
          );`,
          { type: sequelize.QueryTypes.SELECT }
        );
        
        const indexExists = indexCheck[0].exists;
        console.log(`Index idx_${table}_user_id exists in ${table} table: ${indexExists}`);
        
        console.log(`Migration for ${table} was successful!`);
      } else {
        console.error(`user_id column does not exist in ${table} table. Migration may not have been run.`);
      }
      
      console.log('-----------------------------------');
    }
  } catch (error) {
    console.error('Error testing user_id columns:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

// Run the test
testUserIdColumns();
