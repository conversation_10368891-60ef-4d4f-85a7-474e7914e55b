#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to create Stripe customers for existing farms that don't have a Stripe customer ID yet
 * 
 * Usage: node create_stripe_customers_for_existing_farms.js [--dry-run] [--limit=<number>]
 * 
 * Options:
 *   --dry-run    Run the script without making any changes to the database or Stripe
 *   --limit=<n>  Process only the first n farms (useful for testing)
 */

import { sequelize } from '../config/database.js';
import Farm from '../models/Farm.js';
import User from '../models/User.js';
import UserFarm from '../models/UserFarm.js';
import { stripe } from '../config/stripe.js';
import dotenv from 'dotenv';

dotenv.config();

// Parse command line arguments
const args = process.argv.slice(2);
const dryRun = args.includes('--dry-run');
const limitArg = args.find(arg => arg.startsWith('--limit='));
const limit = limitArg ? parseInt(limitArg.split('=')[1], 10) : null;

console.log(`Starting script to create Stripe customers for existing farms`);
if (dryRun) {
  console.log('DRY RUN MODE: No changes will be made to the database or Stripe');
}
if (limit) {
  console.log(`Processing only the first ${limit} farms`);
}

async function createStripeCustomersForExistingFarms() {
  try {
    // Find all farms without a stripe_customer_id
    const farms = await Farm.findAll({
      where: {
        stripe_customer_id: null
      },
      ...(limit ? { limit } : {})
    });

    console.log(`Found ${farms.length} farms without a Stripe customer ID`);

    if (farms.length === 0) {
      console.log('No farms to process. Exiting.');
      return;
    }

    let successCount = 0;
    let errorCount = 0;

    // Process each farm
    for (const [index, farm] of farms.entries()) {
      try {
        console.log(`Processing farm ${index + 1}/${farms.length}: ${farm.name} (ID: ${farm.id})`);

        // Find the farm owner
        const userFarm = await UserFarm.findOne({
          where: {
            farm_id: farm.id,
            role: 'farm_owner'
          },
          include: [
            {
              model: User,
              attributes: ['id', 'email', 'first_name', 'last_name', 'phone_number']
            }
          ]
        });

        if (!userFarm || !userFarm.User) {
          console.error(`No owner found for farm ${farm.name} (ID: ${farm.id})`);
          errorCount++;
          continue;
        }

        const owner = userFarm.User;
        console.log(`Found owner: ${owner.first_name} ${owner.last_name} (ID: ${owner.id})`);

        if (!dryRun) {
          // Create a Stripe customer for the farm
          const customer = await stripe.customers.create({
            email: farm.billing_email || owner.email,
            name: farm.name,
            phone: owner.phone_number || undefined,
            metadata: {
              farm_id: farm.id,
              owner_id: owner.id,
              owner_name: `${owner.first_name} ${owner.last_name}`
            }
          });

          console.log(`Created Stripe customer with ID: ${customer.id}`);

          // Update the farm record with the Stripe customer ID
          await farm.update({ stripe_customer_id: customer.id });
          console.log(`Updated farm record with Stripe customer ID`);
          
          successCount++;
        } else {
          console.log(`[DRY RUN] Would create Stripe customer for farm ${farm.name}`);
          successCount++;
        }
      } catch (error) {
        console.error(`Error processing farm ${farm.name} (ID: ${farm.id}):`, error.message);
        errorCount++;
      }
    }

    console.log('\nSummary:');
    console.log(`Total farms processed: ${farms.length}`);
    console.log(`Successful: ${successCount}`);
    console.log(`Failed: ${errorCount}`);

  } catch (error) {
    console.error('Error querying farms:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
    console.log('Database connection closed');
  }
}

// Run the script
createStripeCustomersForExistingFarms()
  .then(() => {
    console.log('Script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });