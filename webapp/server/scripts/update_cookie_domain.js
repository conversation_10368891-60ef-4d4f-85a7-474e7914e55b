import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the authController.js file
const authControllerPath = path.join(__dirname, '..', 'controllers', 'authController.js');

// Read the file
let content = fs.readFileSync(authControllerPath, 'utf8');

// Replace all instances of hardcoded domain with the getCookieDomain() function
content = content.replace(/domain: `\.nxtacre\.com`/g, 'domain: getCookieDomain()');

// Also handle the clearCookie cases
content = content.replace(/clearCookie\('.*?', \{ domain: `\.nxtacre\.com` \}\)/g, match => {
  return match.replace('`.nxtacre.com`', 'getCookieDomain()');
});

// Write the updated content back to the file
fs.writeFileSync(authControllerPath, content);

console.log('Updated all cookie domain settings in authController.js');
