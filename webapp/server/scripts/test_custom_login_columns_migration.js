import { sequelize } from '../config/database.js';

async function testCustomLoginColumnsMigration() {
  try {
    console.log('Testing if custom_login_text and custom_login_logo columns exist in farms table...');

    // Get the current schema
    const currentSchema = process.env.DB_SCHEMA || 'site';
    console.log(`Current schema from environment: ${currentSchema}`);

    // Define the required columns
    const requiredColumns = [
      'custom_login_text', 'custom_login_logo'
    ];

    // Check if each required column exists
    console.log('\nChecking required columns:');
    let missingColumns = [];

    for (const column of requiredColumns) {
      try {
        const result = await sequelize.query(
          `SELECT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_schema = '${currentSchema}' 
            AND table_name = 'farms' 
            AND column_name = '${column}'
          );`,
          { type: sequelize.QueryTypes.SELECT }
        );

        const columnExists = result[0].exists;
        console.log(`  - Column farms.${column}: ${columnExists ? 'EXISTS' : 'MISSING'}`);

        if (!columnExists) {
          missingColumns.push(column);
        }
      } catch (error) {
        console.error(`  Error checking column farms.${column}:`, error.message);
        missingColumns.push(`${column} (error checking)`);
      }
    }

    // Report results
    console.log('\nTest results:');
    if (missingColumns.length === 0) {
      console.log('✅ All required columns exist in the farms table.');
      console.log('\n🎉 Migration was successful! All required columns exist.');
    } else {
      console.error(`❌ The following columns are missing: ${missingColumns.join(', ')}`);
      console.error('\n⚠️ Migration was not completely successful. Some columns are missing.');
    }

  } catch (error) {
    console.error('Error testing required columns:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

// Run the test
testCustomLoginColumnsMigration()
  .then(() => {
    console.log('Test completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });
