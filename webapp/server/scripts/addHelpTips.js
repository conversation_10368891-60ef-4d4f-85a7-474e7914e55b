import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const API_URL = process.env.API_URL || 'http://localhost:3000/api';

// Help tips for registration and onboarding
const registrationTips = [
  {
    title: 'Welcome to NxtAcre',
    content: 'Welcome to NxtAcre! This registration form will help you set up your account. Fill in your details to get started.',
    pagePath: '/register',
    elementSelector: 'h2',
    position: 'bottom',
    order: 1,
    isActive: true
  },
  {
    title: 'Farm Name',
    content: 'Enter the name of your farm or agricultural business here. This will be displayed throughout the application.',
    pagePath: '/register',
    elementSelector: '#farm-name',
    position: 'right',
    order: 2,
    isActive: true
  },
  {
    title: 'Farm Subdomain',
    content: 'Your subdomain is a unique web address for your farm. It will be used to access your farm\'s dashboard and can be shared with employees.',
    pagePath: '/register',
    elementSelector: '#subdomain',
    position: 'right',
    order: 3,
    isActive: true
  },
  {
    title: 'Password Requirements',
    content: 'Create a strong password that\'s at least 8 characters long. Consider using a mix of letters, numbers, and special characters for better security.',
    pagePath: '/register',
    elementSelector: '#password',
    position: 'right',
    order: 4,
    isActive: true
  },
  {
    title: 'Business Account Option',
    content: 'If you\'re a supplier, vendor, or veterinarian who works with farms, you should register for a business account instead.',
    pagePath: '/register',
    elementSelector: 'form div:last-child p:last-child',
    position: 'top',
    order: 5,
    isActive: true
  }
];

// Help tips for dashboard
const dashboardTips = [
  {
    title: 'Welcome to Your Dashboard',
    content: 'This is your personalized dashboard where you can see an overview of your farm operations. You can customize it to show the information most important to you.',
    pagePath: '/dashboard',
    elementSelector: '.dashboard-page',
    position: 'top',
    order: 1,
    isActive: true
  },
  {
    title: 'Customize Your Dashboard',
    content: 'Click the "Edit" button in the top-right corner to customize your dashboard. You can add, remove, and rearrange widgets to suit your needs.',
    pagePath: '/dashboard',
    elementSelector: '.dashboard-grid-controls',
    position: 'left',
    order: 2,
    isActive: true
  },
  {
    title: 'Add Widgets',
    content: 'In edit mode, click "Add Widget" to add new information panels to your dashboard. Choose from weather, tasks, inventory, and more.',
    pagePath: '/dashboard',
    elementSelector: '.btn.btn-primary',
    position: 'bottom',
    order: 3,
    isActive: true
  }
];

// Help tips for farms management
const farmsTips = [
  {
    title: 'Farm Management',
    content: 'Here you can manage all your farms. Each farm can have its own set of fields, equipment, inventory, and employees.',
    pagePath: '/farms',
    elementSelector: 'h1',
    position: 'bottom',
    order: 1,
    isActive: true
  },
  {
    title: 'Add a New Farm',
    content: 'Click here to add a new farm to your account. You can manage multiple farms from a single account.',
    pagePath: '/farms',
    elementSelector: 'a[href="/farms/new"]',
    position: 'left',
    order: 2,
    isActive: true
  },
  {
    title: 'Farm Details',
    content: 'Click "View Details" to see more information about a farm, including its fields, equipment, and employees.',
    pagePath: '/farms',
    elementSelector: 'a[href^="/farms/"]',
    position: 'top',
    order: 3,
    isActive: true
  }
];

// Help tips for equipment management
const equipmentTips = [
  {
    title: 'Equipment Management',
    content: 'Here you can manage all your farm equipment. Keep track of tractors, implements, tools, and other machinery.',
    pagePath: '/equipment',
    elementSelector: 'h1',
    position: 'bottom',
    order: 1,
    isActive: true
  },
  {
    title: 'Add New Equipment',
    content: 'Click here to add a new piece of equipment to your inventory. You can track details like purchase date, value, and maintenance schedule.',
    pagePath: '/equipment',
    elementSelector: 'a[href="/equipment/new"]',
    position: 'left',
    order: 2,
    isActive: true
  }
];

// Help tips for inventory management
const inventoryTips = [
  {
    title: 'Inventory Management',
    content: 'Here you can manage your farm inventory, including seeds, fertilizers, chemicals, and other supplies.',
    pagePath: '/inventory',
    elementSelector: 'h1',
    position: 'bottom',
    order: 1,
    isActive: true
  },
  {
    title: 'Add New Inventory Item',
    content: 'Click here to add a new item to your inventory. You can track quantities, costs, and usage over time.',
    pagePath: '/inventory',
    elementSelector: 'a[href="/inventory/new"]',
    position: 'left',
    order: 2,
    isActive: true
  }
];

// Help tips for field management
const fieldsTips = [
  {
    title: 'Field Management',
    content: 'Here you can manage all your fields. Track planting, harvesting, and other activities for each field.',
    pagePath: '/fields',
    elementSelector: 'h1',
    position: 'bottom',
    order: 1,
    isActive: true
  },
  {
    title: 'Add a New Field',
    content: 'Click here to add a new field to your farm. You can define its boundaries, soil type, and other characteristics.',
    pagePath: '/fields',
    elementSelector: 'a[href="/fields/new"]',
    position: 'left',
    order: 2,
    isActive: true
  }
];

// Help tips for task management
const tasksTips = [
  {
    title: 'Task Management',
    content: 'Here you can manage all your farm tasks. Assign tasks to employees, set due dates, and track completion.',
    pagePath: '/tasks',
    elementSelector: 'h1',
    position: 'bottom',
    order: 1,
    isActive: true
  },
  {
    title: 'Add a New Task',
    content: 'Click here to create a new task. You can assign it to yourself or another team member and set a due date.',
    pagePath: '/tasks',
    elementSelector: 'a[href="/tasks/new"]',
    position: 'left',
    order: 2,
    isActive: true
  }
];

// Combine all tips
const allTips = [
  ...registrationTips,
  ...dashboardTips,
  ...farmsTips,
  ...equipmentTips,
  ...inventoryTips,
  ...fieldsTips,
  ...tasksTips
];

// Function to add help tips
const addHelpTips = async () => {
  try {
    console.log(`Adding ${allTips.length} help tips...`);
    
    // Get authentication token (you'll need to implement this based on your auth system)
    // For example, you might need to log in as an admin user
    const authResponse = await axios.post(`${API_URL}/auth/login`, {
      email: process.env.ADMIN_EMAIL,
      password: process.env.ADMIN_PASSWORD
    });
    
    const token = authResponse.data.token;
    
    // Add each tip
    for (const tip of allTips) {
      try {
        const response = await axios.post(
          `${API_URL}/help/tips`,
          tip,
          {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        );
        
        console.log(`Added tip: ${tip.title}`);
      } catch (error) {
        console.error(`Error adding tip "${tip.title}":`, error.response?.data || error.message);
      }
    }
    
    console.log('Finished adding help tips');
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
};

// Run the function
addHelpTips();