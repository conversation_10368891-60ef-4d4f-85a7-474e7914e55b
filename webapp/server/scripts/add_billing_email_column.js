import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function addBillingEmailColumn() {
  try {
    console.log('Starting to add billing_email column to farms table...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../db/add_billing_email_column.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    await sequelize.query(sql);
    
    console.log('billing_email column added successfully to farms table');
  } catch (error) {
    console.error('Error adding billing_email column:', error);
    process.exit(1);
  }
}

// Run the function
addBillingEmailColumn()
  .then(() => {
    console.log('Migration completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });