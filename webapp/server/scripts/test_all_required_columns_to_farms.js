import { sequelize } from '../config/database.js';

async function testAllRequiredColumnsToFarms() {
  try {
    console.log('Testing if all required columns exist in farms table...');
    
    // Get the current schema
    const currentSchema = process.env.DB_SCHEMA || 'site';
    console.log(`Current schema from environment: ${currentSchema}`);
    
    // Define the required columns
    const requiredColumns = [
      'id', 'name', 'address', 'city', 'state', 'zip_code', 'country', 'tax_id',
      'subscription_plan_id', 'subscription_status', 'subscription_start_date', 'subscription_end_date',
      'billing_email', 'billing_address', 'billing_city', 'billing_state', 'billing_zip_code', 'billing_country',
      'payment_method_id', 'stripe_customer_id', 'subdomain', 'created_at', 'updated_at'
    ];
    
    // Check if each required column exists
    console.log('\nChecking required columns:');
    let missingColumns = [];
    
    for (const column of requiredColumns) {
      try {
        const result = await sequelize.query(
          `SELECT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_schema = '${currentSchema}' 
            AND table_name = 'farms' 
            AND column_name = '${column}'
          );`,
          { type: sequelize.QueryTypes.SELECT }
        );
        
        const columnExists = result[0].exists;
        console.log(`  - Column farms.${column}: ${columnExists ? 'EXISTS' : 'MISSING'}`);
        
        if (!columnExists) {
          missingColumns.push(column);
        }
      } catch (error) {
        console.error(`  Error checking column farms.${column}:`, error.message);
        missingColumns.push(`${column} (error checking)`);
      }
    }
    
    // Check if indexes exist
    console.log('\nChecking indexes:');
    const requiredIndexes = [
      'idx_farms_subscription_plan_id',
      'idx_farms_subscription_status',
      'idx_farms_billing_email',
      'idx_farms_stripe_customer_id'
    ];
    
    let missingIndexes = [];
    
    for (const index of requiredIndexes) {
      try {
        const result = await sequelize.query(
          `SELECT EXISTS (
            SELECT 1 
            FROM pg_indexes 
            WHERE schemaname = '${currentSchema}' 
            AND tablename = 'farms' 
            AND indexname = '${index}'
          );`,
          { type: sequelize.QueryTypes.SELECT }
        );
        
        const indexExists = result[0].exists;
        console.log(`  - Index ${index}: ${indexExists ? 'EXISTS' : 'MISSING'}`);
        
        if (!indexExists) {
          missingIndexes.push(index);
        }
      } catch (error) {
        console.error(`  Error checking index ${index}:`, error.message);
        missingIndexes.push(`${index} (error checking)`);
      }
    }
    
    // Report results
    console.log('\nTest results:');
    if (missingColumns.length === 0) {
      console.log('✅ All required columns exist in the farms table.');
    } else {
      console.error(`❌ The following columns are missing: ${missingColumns.join(', ')}`);
    }
    
    if (missingIndexes.length === 0) {
      console.log('✅ All required indexes exist for the farms table.');
    } else {
      console.error(`❌ The following indexes are missing: ${missingIndexes.join(', ')}`);
    }
    
    if (missingColumns.length === 0 && missingIndexes.length === 0) {
      console.log('\n🎉 Migration was successful! All required columns and indexes exist.');
    } else {
      console.error('\n⚠️ Migration was not completely successful. Some columns or indexes are missing.');
    }
    
  } catch (error) {
    console.error('Error testing required columns:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

// Run the test
testAllRequiredColumnsToFarms()
  .then(() => {
    console.log('Test completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });