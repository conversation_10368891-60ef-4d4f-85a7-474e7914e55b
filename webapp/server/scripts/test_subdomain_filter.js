import { isSubdomainClean } from '../utils/subdomainUtils.js';

// Test cases for the subdomain filter
const testCases = [
  // Original words (should be filtered)
  { subdomain: 'inappropriate', expected: false, category: 'Original words' },
  { subdomain: 'offensive', expected: false, category: 'Original words' },
  { subdomain: 'explicit', expected: false, category: 'Original words' },

  // New words (should be filtered)
  { subdomain: 'obscene', expected: false, category: 'New words' },
  { subdomain: 'vulgar', expected: false, category: 'New words' },
  { subdomain: 'profane', expected: false, category: 'New words' },
  { subdomain: 'lewd', expected: false, category: 'New words' },
  { subdomain: 'nsfw', expected: false, category: 'New words' },
  { subdomain: 'porn', expected: false, category: 'New words' },
  { subdomain: 'racist', expected: false, category: 'New words' },
  { subdomain: 'sexist', expected: false, category: 'New words' },

  // Clean words (should pass)
  { subdomain: 'farm', expected: true, category: 'Clean words' },
  { subdomain: 'agriculture', expected: true, category: 'Clean words' },
  { subdomain: 'harvest', expected: true, category: 'Clean words' },
  { subdomain: 'crops', expected: true, category: 'Clean words' },
  { subdomain: 'livestock', expected: true, category: 'Clean words' },

  // Mixed cases (should be filtered)
  { subdomain: 'Farm-NSFW', expected: false, category: 'Mixed cases' },
  { subdomain: 'OBSCENE-farm', expected: false, category: 'Mixed cases' },
  { subdomain: 'Vulgar-Crops', expected: false, category: 'Mixed cases' },

  // Compound words (should be filtered)
  { subdomain: 'farmobscene', expected: false, category: 'Compound words' },
  { subdomain: 'vulgarharvest', expected: false, category: 'Compound words' },
  { subdomain: 'cropsporn', expected: false, category: 'Compound words' },

  // Profanity position tests (should be filtered)
  { subdomain: 'pornfarm', expected: false, category: 'Profanity position' },
  { subdomain: 'farm-porn-crops', expected: false, category: 'Profanity position' },
  { subdomain: 'harvestporn', expected: false, category: 'Profanity position' },

  // Split profanity tests (should be filtered)
  { subdomain: 'po-rn-farm', expected: false, category: 'Split profanity' },
  { subdomain: 'ob-scene-harvest', expected: false, category: 'Split profanity' },
  { subdomain: 'farm-se-xist', expected: false, category: 'Split profanity' }
];

// Run the tests
console.log('Testing subdomain filter...\n');

let passedTests = 0;
let failedTests = 0;
let currentCategory = '';

for (const test of testCases) {
  // Print category header if it's a new category
  if (test.category !== currentCategory) {
    currentCategory = test.category;
    console.log(`\n=== ${currentCategory} ===`);
  }

  const result = isSubdomainClean(test.subdomain);
  const passed = result === test.expected;

  if (passed) {
    passedTests++;
    console.log(`✅ PASS: '${test.subdomain}' - Expected: ${test.expected}, Got: ${result}`);
  } else {
    failedTests++;
    console.log(`❌ FAIL: '${test.subdomain}' - Expected: ${test.expected}, Got: ${result}`);
  }
}

// Print summary
console.log(`\n=== Summary ===`);
console.log(`Total tests: ${testCases.length}`);
console.log(`Passed: ${passedTests}`);
console.log(`Failed: ${failedTests}`);

if (failedTests === 0) {
  console.log('\n✅ All tests passed! The subdomain filter is working correctly.');
} else {
  console.log('\n❌ Some tests failed. Please check the filter implementation.');
}
