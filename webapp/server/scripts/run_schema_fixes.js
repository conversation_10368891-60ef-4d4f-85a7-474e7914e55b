import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runSchemaMigrations() {
  try {
    console.log('Starting schema migrations...');
    
    // Define the migration files to run in order
    const migrationFiles = [
      'add_farm_billing_columns.sql',
      'add_user_columns.sql',
      'add_user_farm_columns.sql'
    ];
    
    // Run each migration file
    for (const file of migrationFiles) {
      console.log(`Running migration: ${file}`);
      
      // Read the SQL file
      const sqlFilePath = path.join(__dirname, '../db', file);
      const sql = fs.readFileSync(sqlFilePath, 'utf8');
      
      // Execute the SQL
      await sequelize.query(sql);
      
      console.log(`Migration ${file} completed successfully`);
    }
    
    console.log('All schema migrations completed successfully');
  } catch (error) {
    console.error('Error running schema migrations:', error);
    process.exit(1);
  }
}

// Run the function
runSchemaMigrations()
  .then(() => {
    console.log('Migrations completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });