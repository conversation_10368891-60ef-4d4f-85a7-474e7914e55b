import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function allowNullFarmIdInRolePermissions() {
  try {
    console.log('Starting migration: Allowing NULL farm_id in role_permissions table...');

    // Define the migration file
    const migrationFile = 'allow_null_farm_id_in_role_permissions.sql';

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../db', migrationFile);
    let sql = fs.readFileSync(sqlFilePath, 'utf8');

    // Replace the schema variable with the actual schema name
    const schema = process.env.DB_SCHEMA || 'site';
    sql = sql.replace('${DB_SCHEMA:=public}', schema);

    console.log(`Using database schema: ${schema}`);

    // Execute the SQL
    await sequelize.query(sql);

    console.log(`Migration ${migrationFile} completed successfully`);
    console.log('role_permissions table now allows NULL farm_id values for system-wide default permissions');

    // Verify the change was applied
    const [result] = await sequelize.query(`
      SELECT column_name, is_nullable 
      FROM information_schema.columns 
      WHERE table_schema = '${schema}' 
      AND table_name = 'role_permissions' 
      AND column_name = 'farm_id'
    `);

    if (result.length > 0) {
      console.log('\nVerification of column change:');
      console.log('----------------------------');
      console.log(`Column: ${result[0].column_name}`);
      console.log(`Allows NULL: ${result[0].is_nullable === 'YES' ? 'Yes' : 'No'}`);
    } else {
      console.error('Could not verify column change - column not found');
    }

  } catch (error) {
    console.error('Error running migration:', error);
    process.exit(1);
  }
}

// Run the function
allowNullFarmIdInRolePermissions()
  .then(() => {
    console.log('\nMigration completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });