import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runMigration() {
  try {
    console.log('Starting farm_grants table migration...');

    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '../db/migrations/add_farm_grants_table.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Execute the migration
    await sequelize.query(migrationSQL);

    console.log('Farm grants table migration completed successfully!');

    // Record the migration in the database_migrations table if it exists
    try {
      await sequelize.query(`
        INSERT INTO site.database_migrations (name, applied_at, created_at, updated_at)
        SELECT 'add_farm_grants_table', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        WHERE EXISTS (
          SELECT 1 FROM information_schema.tables 
          WHERE table_schema = 'site' AND table_name = 'database_migrations'
        );
      `);
    } catch (recordError) {
      console.warn('Could not record migration in database_migrations table:', recordError.message);
    }

    // Verify the table was created
    const [results] = await sequelize.query(`
      SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'site' AND table_name = 'farm_grants'
      );
    `);

    if (results[0].exists) {
      console.log('Verification successful: farm_grants table exists.');
    } else {
      console.error('Verification failed: farm_grants table does not exist!');
    }

  } catch (error) {
    console.error('Error running farm_grants migration:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

// Run the migration
runMigration();
