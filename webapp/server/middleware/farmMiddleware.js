import { sequelize } from '../config/database.js';
import Farm from '../models/Farm.js';
import UserFarm from '../models/UserFarm.js';

// Middleware to set the database schema based on the farm ID
export const setFarmSchema = async (req, res, next) => {
  // Save the current search_path to restore it later
  let currentSearchPath;

  try {
    // Skip for public routes or if no user is authenticated
    if (!req.user) {
      return next();
    }

    let farmId;

    // Check if farmId is provided in the request params or body
    if (req.params.farmId) {
      farmId = req.params.farmId;
    } else if (req.body.farmId) {
      farmId = req.body.farmId;
    } else {
      // If no farmId is provided, get the most recent farm association for the user
      const userFarm = await UserFarm.findOne({
        where: { user_id: req.user.id },
        order: [['created_at', 'DESC']] // Get the most recent farm association
      });

      if (!userFarm) {
        console.warn('User has no associated farm');
        return next();
      }

      farmId = userFarm.farm_id;
    }

    // Find the farm to get its name (will be used for schema)
    const farm = await Farm.findByPk(farmId);

    if (!farm) {
      console.error(`Farm not found for ID: ${farmId}`);
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if the user has access to this farm
    if (req.params.farmId || req.body.farmId) {
      const userFarm = await UserFarm.findOne({
        where: { 
          user_id: req.user.id,
          farm_id: farmId
        }
      });

      if (!userFarm && !req.user.is_global_admin) {
        return res.status(403).json({ error: 'Unauthorized to access this farm' });
      }
    }

    // Convert farm name to a valid schema name (lowercase, no spaces, etc.)
    const schemaName = `farm_${farm.id.replace(/-/g, '_')}`;

    // Save the current search_path to restore it later
    const currentSearchPathResult = await sequelize.query('SHOW search_path;', { type: sequelize.QueryTypes.SELECT });
    currentSearchPath = currentSearchPathResult[0].search_path;

    // Set the schema for this request - using only the site schema for all farms
    const siteSchema = process.env.DB_SCHEMA || 'site';
    await sequelize.query(`SET search_path TO ${siteSchema};`);
    console.log(`Set search_path to schema: ${siteSchema}`);

    // Store the farm ID in the request for later use
    req.farmId = farmId;

    // For backward compatibility, still store the farm schema name but don't use it
    req.farmSchema = schemaName;

    // Store the original search_path in the request for later restoration
    req.originalSearchPath = currentSearchPath;

    // Add a response finish listener to restore the search_path after the request is complete
    res.on('finish', async () => {
      try {
        await sequelize.query(`SET search_path TO ${currentSearchPath};`);
        console.log(`Restored search_path to: ${currentSearchPath}`);
      } catch (restoreError) {
        console.error('Error restoring search_path:', restoreError);
      }
    });

    next();
  } catch (error) {
    console.error('Error setting farm schema:', error);

    // Restore the original search_path in case of error
    if (currentSearchPath) {
      try {
        await sequelize.query(`SET search_path TO ${currentSearchPath};`);
        console.log(`Restored search_path to: ${currentSearchPath}`);
      } catch (restoreError) {
        console.error('Error restoring search_path:', restoreError);
      }
    }

    return res.status(500).json({ error: 'Internal server error' });
  }
};

// Middleware to ensure the common schema is used
export const ensureFarmSchema = async (req, res, next) => {
  // Save the current search_path to restore it later if not already saved
  let currentSearchPath;
  let needToRestoreSearchPath = false;

  try {
    // If originalSearchPath is not set (in case this middleware is used independently)
    if (!req.originalSearchPath) {
      const currentSearchPathResult = await sequelize.query('SHOW search_path;', { type: sequelize.QueryTypes.SELECT });
      currentSearchPath = currentSearchPathResult[0].search_path;
      needToRestoreSearchPath = true;

      // Set search path to the common schema
      const siteSchema = process.env.DB_SCHEMA || 'site';
      await sequelize.query(`SET search_path TO ${siteSchema};`);
      console.log(`Set search_path to common schema: ${siteSchema}`);
    }

    // If we saved the search_path and need to restore it (when used independently)
    if (needToRestoreSearchPath && currentSearchPath) {
      // Add a response finish listener to restore the search_path after the request is complete
      res.on('finish', async () => {
        try {
          await sequelize.query(`SET search_path TO ${currentSearchPath};`);
          console.log(`Restored search_path to: ${currentSearchPath}`);
        } catch (restoreError) {
          console.error('Error restoring search_path:', restoreError);
        }
      });
    }

    next();
  } catch (error) {
    console.error('Error ensuring schema:', error);

    // Restore the original search_path in case of error if we saved it
    if (needToRestoreSearchPath && currentSearchPath) {
      try {
        await sequelize.query(`SET search_path TO ${currentSearchPath};`);
        console.log(`Restored search_path to: ${currentSearchPath}`);
      } catch (restoreError) {
        console.error('Error restoring search_path:', restoreError);
      }
    }

    return res.status(500).json({ error: 'Internal server error' });
  }
};
