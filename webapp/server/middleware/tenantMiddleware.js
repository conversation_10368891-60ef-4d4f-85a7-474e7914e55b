// This file is deprecated. Use farmMiddleware.js instead.
import { sequelize } from '../config/database.js';

// Middleware to set the database schema based on the tenant ID
export const setTenantSchema = async (req, res, next) => {
  console.warn('setTenantSchema is deprecated. Use setFarmSchema from farmMiddleware.js instead.');
  // Pass through to next middleware
  next();
};

// Middleware to ensure tenant schemas exist
export const ensureTenantSchema = async (req, res, next) => {
  console.warn('ensureTenantSchema is deprecated. Use ensureFarmSchema from farmMiddleware.js instead.');
  // Pass through to next middleware
  next();
};
