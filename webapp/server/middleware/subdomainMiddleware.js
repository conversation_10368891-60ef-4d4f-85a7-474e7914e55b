import { determineUserSubdomain } from '../controllers/authController.js';

/**
 * Middleware to validate that users are on the correct subdomain
 * and redirect them if necessary
 */
export const validateSubdomain = async (req, res, next) => {
  try {
    // Skip validation for certain routes
    const skipRoutes = [
      '/auth/login',
      '/auth/register',
      '/auth/forgot-password',
      '/auth/reset-password',
      '/auth/verify-email',
      '/auth/verify-2fa',
      '/auth/switch-farm',
      '/farms/by-subdomain'
    ];

    // Check if current route should be skipped
    if (skipRoutes.some(route => req.path.startsWith(route))) {
      return next();
    }

    // Skip if no user is authenticated
    if (!req.user) {
      return next();
    }

    // Get current subdomain from host
    const host = req.get('host');
    let currentSubdomain = null;
    if (host) {
      const hostParts = host.split('.');
      if (hostParts.length > 2) {
        currentSubdomain = hostParts[0];
      }
    }

    // Determine the correct subdomain for the user
    const subdomainResult = await determineUserSubdomain(req.user, currentSubdomain);
    const correctSubdomain = subdomainResult.subdomain;

    // If user is on the wrong subdomain, return an error with redirect info
    if (currentSubdomain !== correctSubdomain) {
      // Special handling for global admins on app subdomain
      if (req.user.is_global_admin && currentSubdomain === 'app') {
        return next();
      }

      // Return redirect information
      return res.status(302).json({
        error: 'Incorrect subdomain',
        redirectTo: correctSubdomain,
        message: `Please access this resource from the ${correctSubdomain} subdomain`
      });
    }

    // Add subdomain info to request for later use
    req.currentSubdomain = currentSubdomain;
    req.farmId = subdomainResult.farmId;

    next();
  } catch (error) {
    console.error('Subdomain validation error:', error);
    // Don't block the request on validation errors
    next();
  }
};

/**
 * Middleware to ensure users without farms are redirected to app subdomain
 */
export const ensureAppSubdomainForNonFarmUsers = async (req, res, next) => {
  try {
    // Skip if no user is authenticated
    if (!req.user) {
      return next();
    }

    // Skip for global admins
    if (req.user.is_global_admin) {
      return next();
    }

    // Check if user is a business owner without farms
    if (['supplier', 'vet'].includes(req.user.user_type) && req.user.is_business_owner) {
      const host = req.get('host');
      let currentSubdomain = null;
      if (host) {
        const hostParts = host.split('.');
        if (hostParts.length > 2) {
          currentSubdomain = hostParts[0];
        }
      }

      // If not on app subdomain, redirect
      if (currentSubdomain !== 'app') {
        return res.status(302).json({
          error: 'Incorrect subdomain',
          redirectTo: 'app',
          message: 'Business users should access the platform from the app subdomain'
        });
      }
    }

    next();
  } catch (error) {
    console.error('App subdomain validation error:', error);
    // Don't block the request on validation errors
    next();
  }
};

/**
 * Middleware to ensure global admins can access admin subdomain
 */
export const ensureAdminSubdomainForGlobalAdmins = async (req, res, next) => {
  try {
    // Skip if no user is authenticated
    if (!req.user) {
      return next();
    }

    // Only apply to global admins
    if (!req.user.is_global_admin) {
      return next();
    }

    // Check if accessing admin routes
    if (req.path.startsWith('/admin')) {
      const host = req.get('host');
      let currentSubdomain = null;
      if (host) {
        const hostParts = host.split('.');
        if (hostParts.length > 2) {
          currentSubdomain = hostParts[0];
        }
      }

      // If not on admin subdomain, suggest redirect
      if (currentSubdomain !== 'admin') {
        return res.status(302).json({
          error: 'Incorrect subdomain',
          redirectTo: 'admin',
          message: 'Admin functions should be accessed from the admin subdomain'
        });
      }
    }

    next();
  } catch (error) {
    console.error('Admin subdomain validation error:', error);
    // Don't block the request on validation errors
    next();
  }
};

/**
 * Utility function to get the correct subdomain for a user
 * @param {Object} user - The user object
 * @returns {Promise<string>} The correct subdomain for the user
 */
export const getCorrectSubdomainForUser = async (user) => {
  try {
    const subdomainResult = await determineUserSubdomain(user);
    return subdomainResult.subdomain;
  } catch (error) {
    console.error('Error determining subdomain for user:', error);
    return 'app'; // Default fallback
  }
};
