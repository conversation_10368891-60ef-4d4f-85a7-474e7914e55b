#!/bin/bash

# <PERSON><PERSON><PERSON> to replace indigo with primary in all GlobalAdmin files
echo "Replacing indigo with primary in GlobalAdmin files..."

# Find all files in the GlobalAdmin directory
files=$(find src/pages/GlobalAdmin -type f -name "*.tsx")

# Loop through each file and replace indigo with primary
for file in $files; do
  echo "Processing $file..."
  # Replace bg-indigo-600 with bg-primary-600
  sed -i '' 's/bg-indigo-600/bg-primary-600/g' "$file"
  # Replace hover:bg-indigo-700 with hover:bg-primary-700
  sed -i '' 's/hover:bg-indigo-700/hover:bg-primary-700/g' "$file"
  # Replace text-indigo-600 with text-primary-600
  sed -i '' 's/text-indigo-600/text-primary-600/g' "$file"
  # Replace hover:text-indigo-900 with hover:text-primary-900
  sed -i '' 's/hover:text-indigo-900/hover:text-primary-900/g' "$file"
  # Replace focus:ring-indigo-500 with focus:ring-primary-500
  sed -i '' 's/focus:ring-indigo-500/focus:ring-primary-500/g' "$file"
done

echo "Replacement complete!"