import React, { useState, useEffect } from 'react';
import { 
  getLatestIsobusData, 
  getEquipmentIsobusData, 
  getEquipmentIsobusImplements,
  IsobusData
} from '../services/isobusService';

interface EquipmentIsobusProps {
  equipmentId: string;
}

const EquipmentIsobus: React.FC<EquipmentIsobusProps> = ({ equipmentId }) => {
  const [latestData, setLatestData] = useState<IsobusData | null>(null);
  const [historicalData, setHistoricalData] = useState<IsobusData[]>([]);
  const [implementsList, setImplementsList] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'latest' | 'historical' | 'implements'>('latest');
  const [dateRange, setDateRange] = useState<{
    startDate: string;
    endDate: string;
  }>({
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 days ago
    endDate: new Date().toISOString().split('T')[0] // today
  });

  // Fetch latest ISOBUS data
  useEffect(() => {
    const fetchLatestData = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await getLatestIsobusData(equipmentId);
        setLatestData(data);
      } catch (err: any) {
        console.error('Error fetching latest ISOBUS data:', err);
        setError('Failed to load latest ISOBUS data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchLatestData();
  }, [equipmentId]);

  // Fetch historical data when tab changes to historical or date range changes
  useEffect(() => {
    if (activeTab === 'historical') {
      const fetchHistoricalData = async () => {
        try {
          setLoading(true);
          setError(null);
          const response = await getEquipmentIsobusData(
            equipmentId,
            dateRange.startDate,
            dateRange.endDate
          );
          setHistoricalData(response.isobusData);
        } catch (err: any) {
          console.error('Error fetching historical ISOBUS data:', err);
          setError('Failed to load historical ISOBUS data. Please try again later.');
        } finally {
          setLoading(false);
        }
      };

      fetchHistoricalData();
    }
  }, [equipmentId, activeTab, dateRange]);

  // Fetch implements when tab changes to implements
  useEffect(() => {
    if (activeTab === 'implements') {
      const fetchImplements = async () => {
        try {
          setLoading(true);
          setError(null);
          const data = await getEquipmentIsobusImplements(equipmentId);
          setImplementsList(data);
        } catch (err: any) {
          console.error('Error fetching ISOBUS implements:', err);
          setError('Failed to load ISOBUS implements. Please try again later.');
        } finally {
          setLoading(false);
        }
      };

      fetchImplements();
    }
  }, [equipmentId, activeTab]);

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Handle date range change
  const handleDateRangeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setDateRange(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Format JSON data for display
  const formatJsonData = (data: any) => {
    if (!data) return 'N/A';
    try {
      const jsonData = typeof data === 'string' ? JSON.parse(data) : data;
      return JSON.stringify(jsonData, null, 2);
    } catch (err) {
      return data.toString();
    }
  };

  if (loading && !latestData && !historicalData.length && !implementsList.length) {
    return (
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">ISOBUS Integration</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">ISOBUS data and implements for this equipment.</p>
        </div>
        <div className="border-t border-gray-200 p-6 flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading ISOBUS data...</p>
        </div>
      </div>
    );
  }

  if (error && !latestData && !historicalData.length && !implementsList.length) {
    return (
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">ISOBUS Integration</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">ISOBUS data and implements for this equipment.</p>
        </div>
        <div className="border-t border-gray-200">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 m-4 rounded relative" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
      <div className="px-4 py-5 sm:px-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900">ISOBUS Integration</h3>
        <p className="mt-1 max-w-2xl text-sm text-gray-500">ISOBUS data and implements for this equipment.</p>
      </div>

      {/* Tabs */}
      <div className="border-t border-gray-200">
        <div className="flex border-b">
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'latest'
                ? 'border-b-2 border-primary-500 text-primary-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('latest')}
          >
            Latest Data
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'historical'
                ? 'border-b-2 border-primary-500 text-primary-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('historical')}
          >
            Historical Data
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === 'implements'
                ? 'border-b-2 border-primary-500 text-primary-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('implements')}
          >
            Implements
          </button>
        </div>

        {/* Date range selector for historical tab */}
        {activeTab === 'historical' && (
          <div className="p-4 bg-gray-50 border-b border-gray-200">
            <div className="flex flex-wrap items-center gap-4">
              <div>
                <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">
                  Start Date
                </label>
                <input
                  type="date"
                  id="startDate"
                  name="startDate"
                  value={dateRange.startDate}
                  onChange={handleDateRangeChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                />
              </div>
              <div>
                <label htmlFor="endDate" className="block text-sm font-medium text-gray-700">
                  End Date
                </label>
                <input
                  type="date"
                  id="endDate"
                  name="endDate"
                  value={dateRange.endDate}
                  onChange={handleDateRangeChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                />
              </div>
            </div>
          </div>
        )}

        {/* Tab content */}
        <div className="p-4">
          {/* Latest Data Tab */}
          {activeTab === 'latest' && (
            <div>
              {loading && !latestData ? (
                <div className="flex justify-center py-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                  <p className="ml-3 text-gray-500">Loading latest data...</p>
                </div>
              ) : latestData ? (
                <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2 lg:grid-cols-3">
                  <div className="sm:col-span-2 lg:col-span-3">
                    <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                    <dd className="mt-1 text-sm text-gray-900">{formatDate(latestData.timestamp)}</dd>
                  </div>

                  {latestData.message_type && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Message Type</dt>
                      <dd className="mt-1 text-sm text-gray-900">{latestData.message_type}</dd>
                    </div>
                  )}

                  {latestData.pgn && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">PGN</dt>
                      <dd className="mt-1 text-sm text-gray-900">{latestData.pgn}</dd>
                    </div>
                  )}

                  {latestData.source_address && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Source Address</dt>
                      <dd className="mt-1 text-sm text-gray-900">{latestData.source_address}</dd>
                    </div>
                  )}

                  {latestData.priority !== null && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Priority</dt>
                      <dd className="mt-1 text-sm text-gray-900">{latestData.priority}</dd>
                    </div>
                  )}

                  {latestData.data_length !== null && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Data Length</dt>
                      <dd className="mt-1 text-sm text-gray-900">{latestData.data_length} bytes</dd>
                    </div>
                  )}

                  {latestData.implement_type && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Implement Type</dt>
                      <dd className="mt-1 text-sm text-gray-900">{latestData.implement_type}</dd>
                    </div>
                  )}

                  {latestData.implement_manufacturer && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Implement Manufacturer</dt>
                      <dd className="mt-1 text-sm text-gray-900">{latestData.implement_manufacturer}</dd>
                    </div>
                  )}

                  {latestData.implement_model && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Implement Model</dt>
                      <dd className="mt-1 text-sm text-gray-900">{latestData.implement_model}</dd>
                    </div>
                  )}

                  {latestData.task_name && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Task</dt>
                      <dd className="mt-1 text-sm text-gray-900">{latestData.task_name}</dd>
                    </div>
                  )}

                  {latestData.status && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Status</dt>
                      <dd className="mt-1 text-sm text-gray-900">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          latestData.status === 'active' ? 'bg-green-100 text-green-800' : 
                          latestData.status === 'idle' ? 'bg-yellow-100 text-yellow-800' : 
                          latestData.status === 'error' ? 'bg-red-100 text-red-800' : 
                          'bg-blue-100 text-blue-800'
                        }`}>
                          {latestData.status.charAt(0).toUpperCase() + latestData.status.slice(1)}
                        </span>
                      </dd>
                    </div>
                  )}

                  {latestData.error_code && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Error Code</dt>
                      <dd className="mt-1 text-sm text-gray-900">{latestData.error_code}</dd>
                    </div>
                  )}

                  {latestData.raw_data && (
                    <div className="sm:col-span-2 lg:col-span-3">
                      <dt className="text-sm font-medium text-gray-500">Raw Data</dt>
                      <dd className="mt-1 text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded overflow-auto">
                        {latestData.raw_data}
                      </dd>
                    </div>
                  )}

                  {latestData.parsed_data && (
                    <div className="sm:col-span-2 lg:col-span-3">
                      <dt className="text-sm font-medium text-gray-500">Parsed Data</dt>
                      <dd className="mt-1 text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded overflow-auto">
                        <pre>{formatJsonData(latestData.parsed_data)}</pre>
                      </dd>
                    </div>
                  )}
                </dl>
              ) : (
                <p className="text-sm text-gray-500 py-4">No ISOBUS data available for this equipment.</p>
              )}
            </div>
          )}

          {/* Historical Data Tab */}
          {activeTab === 'historical' && (
            <div>
              {loading ? (
                <div className="flex justify-center py-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                  <p className="ml-3 text-gray-500">Loading historical data...</p>
                </div>
              ) : historicalData.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Timestamp
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Message Type
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          PGN
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Implement
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Details
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {historicalData.map((data) => (
                        <tr key={data.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatDate(data.timestamp)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {data.message_type || 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {data.pgn || 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {data.implement_type ? (
                              <div>
                                <div>{data.implement_type}</div>
                                {data.implement_manufacturer && (
                                  <div className="text-xs text-gray-400">{data.implement_manufacturer} {data.implement_model}</div>
                                )}
                              </div>
                            ) : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {data.status ? (
                              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                data.status === 'active' ? 'bg-green-100 text-green-800' : 
                                data.status === 'idle' ? 'bg-yellow-100 text-yellow-800' : 
                                data.status === 'error' ? 'bg-red-100 text-red-800' : 
                                'bg-blue-100 text-blue-800'
                              }`}>
                                {data.status.charAt(0).toUpperCase() + data.status.slice(1)}
                              </span>
                            ) : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <button
                              onClick={() => {
                                alert(`ISOBUS Data Details:\n\nRaw Data: ${data.raw_data || 'N/A'}\n\nParsed Data: ${formatJsonData(data.parsed_data)}`);
                              }}
                              className="text-primary-600 hover:text-primary-900"
                            >
                              View
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-sm text-gray-500 py-4">No historical ISOBUS data available for this equipment in the selected date range.</p>
              )}
            </div>
          )}

          {/* Implements Tab */}
          {activeTab === 'implements' && (
            <div>
              {loading ? (
                <div className="flex justify-center py-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                  <p className="ml-3 text-gray-500">Loading implements data...</p>
                </div>
              ) : implementsList.length > 0 ? (
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                  {implementsList.map((implement, index) => (
                    <div key={index} className="bg-white overflow-hidden shadow rounded-lg">
                      <div className="px-4 py-5 sm:p-6">
                        <h3 className="text-lg leading-6 font-medium text-gray-900">
                          {implement.type || 'Unknown Implement'}
                        </h3>
                        <div className="mt-2 max-w-xl text-sm text-gray-500">
                          <p>{implement.manufacturer} {implement.model}</p>
                        </div>
                        <div className="mt-3 text-sm">
                          {implement.last_connected && (
                            <p className="text-gray-500">Last connected: {formatDate(implement.last_connected)}</p>
                          )}
                          {implement.status && (
                            <p className="mt-1">
                              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                implement.status === 'connected' ? 'bg-green-100 text-green-800' : 
                                implement.status === 'disconnected' ? 'bg-gray-100 text-gray-800' : 
                                'bg-yellow-100 text-yellow-800'
                              }`}>
                                {implement.status.charAt(0).toUpperCase() + implement.status.slice(1)}
                              </span>
                            </p>
                          )}
                        </div>
                      </div>
                      {implement.capabilities && (
                        <div className="border-t border-gray-200 px-4 py-4 sm:px-6">
                          <h4 className="text-sm font-medium text-gray-500">Capabilities</h4>
                          <ul className="mt-2 list-disc pl-5 text-sm text-gray-500">
                            {implement.capabilities.map((capability: string, i: number) => (
                              <li key={i}>{capability}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 py-4">No ISOBUS implements detected for this equipment.</p>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EquipmentIsobus;
