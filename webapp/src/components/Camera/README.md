# Amcrest Camera Integration

This directory contains components for integrating Amcrest cameras into the NxtAcre farm management platform using P2P (Peer-to-Peer) functionality.

## Overview

The integration allows users to add Amcrest cameras to the system using only the camera's serial number, username, and password, without requiring the camera to be on the same network as the application. The cameras can be viewed remotely through the Camera Dashboard.

## Components

### CameraForm

The `CameraForm` component is used to collect camera-specific configuration when adding or editing a camera device. It includes fields for:

- Camera type (Amcrest or other)
- Serial number
- Username
- Password
- P2P connection toggle
- Stream URL (automatically generated for P2P connections)

### CameraViewer

The `CameraViewer` component displays the camera feed using the amcrestwebview service. It embeds an iframe that connects to the amcrestwebview service with the camera's serial number, username, and password.

## Usage

### Adding a Camera

1. Navigate to the IoT Device Management page
2. Click "Add IoT Device"
3. Fill in the device name and select "Camera" as the device type
4. In the Camera Configuration section, enter the camera's serial number, username, and password
5. Save the device

### Viewing Cameras

1. Navigate to the IoT Device Management page
2. Click "Camera Dashboard"
3. All configured cameras will be displayed in a grid
4. Use the layout buttons to adjust the grid size (1x1, 2x2, 3x3)

## Security

Camera credentials are encrypted before being stored in the database using AES-256-CBC encryption. The encryption key is stored in the `.env` file as `ENCRYPTION_KEY`.

## Technical Details

### P2P Connection

The P2P connection is established through the amcrestwebview service, which allows direct connection between the client and the camera through the Amcrest cloud service, bypassing the need for port forwarding.

### Camera Configuration

Camera configuration is stored in the `configuration` JSON field of the IoTDevice model:

```json
{
  "cameraType": "amcrest",
  "serialNumber": "ABCD1234EFGH5678",
  "username": "admin",
  "password": "enc:encrypted_password",
  "p2pEnabled": true,
  "streamUrl": "p2p://ABCD1234EFGH5678"
}
```

### Encryption

Camera credentials are encrypted using the `encryption.js` utility in the `server/utils` directory. The utility provides functions for encrypting and decrypting sensitive data, specifically camera credentials.

## Troubleshooting

If you encounter issues with the camera integration, check the following:

1. Ensure the camera is powered on and connected to the internet
2. Verify that the serial number, username, and password are correct
3. Check that the camera is registered with the Amcrest cloud service
4. Ensure that the `ENCRYPTION_KEY` environment variable is set correctly