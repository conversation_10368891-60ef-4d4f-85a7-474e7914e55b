import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import { useFarm } from '../context/FarmContext';
import { Link } from 'react-router-dom';
import { API_URL } from '../config';

interface Task {
  id: string;
  title: string;
  dueDate: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'in_progress' | 'completed' | 'overdue';
  assignedTo: {
    id: string;
    name: string;
  };
}

const TaskStatusWidget: React.FC = () => {
  const { token } = useAuth();
  const { currentFarm } = useFarm();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTasks = async () => {
      if (!currentFarm || !token) return;

      try {
        setLoading(true);
        setError(null);

        // Get current date for determining overdue tasks
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        // Call the API to get tasks with the new URL format
        const response = await axios.get(`${API_URL}/tasks/farms/${currentFarm.id}`, {
          headers: { Authorization: `Bearer ${token}` },
          params: {
            limit: 5, // Limit to 5 tasks for the widget
            sortBy: 'due_date',
            sortOrder: 'ASC'
          }
        });

        if (response.data && response.data.success && response.data.tasks) {
          // Map the backend data to our frontend Task interface
          const taskData: Task[] = response.data.tasks.map((task: any) => {
            // Determine if task is overdue
            const dueDate = new Date(task.dueDate);
            dueDate.setHours(0, 0, 0, 0);

            let status = task.status;
            // If task is pending and due date is in the past, mark as overdue
            if (status === 'pending' && dueDate < today) {
              status = 'overdue';
            }

            return {
              id: task.id,
              title: task.title,
              dueDate: task.dueDate,
              priority: task.priority as 'low' | 'medium' | 'high',
              status: status as 'pending' | 'in_progress' | 'completed' | 'overdue',
              assignedTo: task.assignedTo ? {
                id: task.assignedTo.id,
                name: `${task.assignedTo.firstName} ${task.assignedTo.lastName}`
              } : {
                id: '0',
                name: 'Unassigned'
              }
            };
          });

          setTasks(taskData);
        } else {
          throw new Error('Invalid response format from server');
        }

        setLoading(false);
      } catch (err: any) {
        console.error('Error fetching tasks:', err);
        setError(err.response?.data?.error || 'Failed to load tasks');
        setLoading(false);

        // Fallback to empty array if API call fails
        setTasks([]);
      }
    };

    fetchTasks();
  }, [currentFarm, token]);

  const getPriorityBadgeClass = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'overdue':
        return 'bg-red-100 text-red-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDueDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Reset time part for comparison
    today.setHours(0, 0, 0, 0);
    tomorrow.setHours(0, 0, 0, 0);
    const compareDate = new Date(date);
    compareDate.setHours(0, 0, 0, 0);

    if (compareDate.getTime() === today.getTime()) {
      return 'Today';
    } else if (compareDate.getTime() === tomorrow.getTime()) {
      return 'Tomorrow';
    } else {
      return date.toLocaleDateString();
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
        <span className="ml-2 text-gray-500">Loading tasks...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-center text-red-500">
        <p>Error: {error}</p>
      </div>
    );
  }

  if (tasks.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        <p>No tasks available.</p>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="space-y-3">
        {tasks.map((task) => (
          <div key={task.id} className="border rounded-lg p-3 hover:bg-gray-50">
            <div className="flex justify-between items-start">
              <div>
                <Link to={`/tasks/${task.id}`} className="text-sm font-medium text-indigo-600 hover:text-indigo-800">
                  {task.title}
                </Link>
                <p className="text-xs text-gray-500 mt-1">
                  Assigned to: {task.assignedTo.name}
                </p>
              </div>
              <div className="flex space-x-2">
                <span className={`px-2 py-1 text-xs rounded-full ${getPriorityBadgeClass(task.priority)}`}>
                  {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                </span>
                <span className={`px-2 py-1 text-xs rounded-full ${getStatusBadgeClass(task.status)}`}>
                  {task.status === 'in_progress' ? 'In Progress' : task.status.charAt(0).toUpperCase() + task.status.slice(1)}
                </span>
              </div>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              Due: {formatDueDate(task.dueDate)}
            </div>
          </div>
        ))}
      </div>
      <div className="mt-3 text-right">
        <Link to="/tasks" className="text-sm text-indigo-600 hover:text-indigo-800">
          View all tasks →
        </Link>
      </div>
    </div>
  );
};

export default TaskStatusWidget;
