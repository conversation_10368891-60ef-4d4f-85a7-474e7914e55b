# Theme System

This directory contains components and utilities for the application's theme system, which supports light mode, dark mode, and system preference.

## Components

### ThemeToggleButton

A button component that allows users to toggle between light mode, dark mode, and system preference. The button displays an icon representing the current theme mode.

### ThemeWrapper

A wrapper component that provides the theme context to the application and adds the ThemeToggleButton to the top-right corner of the application.

## Context

### ThemeContext

The ThemeContext provides the theme state and functions to change the theme. It supports three theme modes:

- `light`: Forces light mode
- `dark`: Forces dark mode
- `system`: Uses the system preference (automatically switches between light and dark mode based on the user's system settings)

The theme preference is stored in localStorage, so it persists between sessions.

## Usage

### Using the Theme Context

To use the theme context in your components:

```tsx
import { useTheme } from '../../context/ThemeContext';

const MyComponent = () => {
  const { theme, currentTheme, setTheme } = useTheme();
  
  // theme: The selected theme mode ('light', 'dark', or 'system')
  // currentTheme: The actual applied theme ('light' or 'dark') after system preference is resolved
  // setTheme: Function to change the theme mode
  
  return (
    <div>
      <p>Current theme mode: {theme}</p>
      <p>Applied theme: {currentTheme}</p>
      <button onClick={() => setTheme('light')}>Light Mode</button>
      <button onClick={() => setTheme('dark')}>Dark Mode</button>
      <button onClick={() => setTheme('system')}>System Preference</button>
    </div>
  );
};
```

### Styling for Dark Mode

When styling components, use Tailwind's dark mode classes to provide different styles for light and dark modes:

```tsx
<div className="bg-white dark:bg-gray-900 text-black dark:text-white">
  This div has a white background and black text in light mode,
  and a dark gray background and white text in dark mode.
</div>
```

The `dark:` prefix applies styles only when dark mode is active.