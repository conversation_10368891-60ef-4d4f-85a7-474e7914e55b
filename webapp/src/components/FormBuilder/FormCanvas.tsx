import React, { forwardRef, useRef, useEffect, useState } from 'react';
import { useDrop } from 'react-dnd';
import FormElement from './FormElement';
import { FormElement as FormElementType } from './FormBuilder';

// Grid size for snapping
const GRID_SIZE = 10;

interface FormCanvasProps {
  elements: FormElementType[];
  selectedElement: FormElementType | null;
  onSelectElement: (element: FormElementType | null) => void;
  onUpdateElement: (element: FormElementType) => void;
  onDeleteElement: (elementId: string) => void;
  readOnly?: boolean;
}

const FormCanvas = forwardRef<HTMLDivElement, FormCanvasProps>(
  ({ elements, selectedElement, onSelectElement, onUpdateElement, onDeleteElement, readOnly = false }, ref) => {
    const canvasRef = useRef<HTMLDivElement | null>(null);

    // Set up the ref
    useEffect(() => {
      if (ref) {
        if (typeof ref === 'function') {
          ref(canvasRef.current);
        } else {
          ref.current = canvasRef.current;
        }
      }
    }, [ref]);

    // Set up drop target for drag and drop
    const [{ isOver }, drop] = useDrop({
      accept: 'FORM_ELEMENT',
      drop: (item: any, monitor) => {
        const delta = monitor.getDifferenceFromInitialOffset();
        if (!delta || !selectedElement) return;

        // Calculate new position and snap to grid
        const newX = snapToGrid(selectedElement.x_position + delta.x);
        const newY = snapToGrid(selectedElement.y_position + delta.y);

        // Find a non-overlapping position
        const { x, y } = findNonOverlappingPosition(selectedElement, newX, newY);

        onUpdateElement({
          ...selectedElement,
          x_position: x,
          y_position: y
        });
      },
      collect: (monitor) => ({
        isOver: !!monitor.isOver()
      })
    });

    // Connect the drop ref to the canvas ref
    drop(canvasRef);

    // Handle click on the canvas background (deselect elements)
    const handleCanvasClick = (e: React.MouseEvent) => {
      if (e.target === canvasRef.current) {
        onSelectElement(null);
      }
    };

    // Snap position to grid
    const snapToGrid = (position: number): number => {
      return Math.round(position / GRID_SIZE) * GRID_SIZE;
    };

    // Check if elements overlap
    const checkOverlap = (element: FormElementType, newX: number, newY: number, newWidth?: number, newHeight?: number): boolean => {
      const width = newWidth || element.width;
      const height = newHeight || element.height;

      // Check overlap with other elements
      return elements.some(el => {
        if (el.id === element.id || el.page_number !== element.page_number) return false;

        // Check if rectangles overlap
        return (
          newX < el.x_position + el.width &&
          newX + width > el.x_position &&
          newY < el.y_position + el.height &&
          newY + height > el.y_position
        );
      });
    };

    // Find a non-overlapping position
    const findNonOverlappingPosition = (element: FormElementType, startX: number, startY: number): { x: number, y: number } => {
      let x = startX;
      let y = startY;

      // If no overlap, return original position
      if (!checkOverlap(element, x, y)) {
        return { x, y };
      }

      // Try positions in a spiral pattern around the original position
      const directions = [[0, -1], [1, 0], [0, 1], [-1, 0]]; // Up, Right, Down, Left
      let step = 1;
      let direction = 0;
      let moves = 0;

      // Try up to 100 positions to find a non-overlapping one
      for (let i = 0; i < 100; i++) {
        x += directions[direction][0] * GRID_SIZE;
        y += directions[direction][1] * GRID_SIZE;

        moves++;
        if (moves === step) {
          direction = (direction + 1) % 4;
          moves = 0;
          if (direction % 2 === 0) {
            step++;
          }
        }

        // Check if this position works
        if (!checkOverlap(element, x, y)) {
          return { x, y };
        }
      }

      // If we couldn't find a non-overlapping position, return the original
      return { x: startX, y: startY };
    };

    // Handle element movement
    const handleElementMove = (elementId: string, deltaX: number, deltaY: number) => {
      if (readOnly) return;

      const element = elements.find(el => el.id === elementId);
      if (!element) return;

      // Calculate new position and snap to grid
      let newX = snapToGrid(element.x_position + deltaX);
      let newY = snapToGrid(element.y_position + deltaY);

      // Find a non-overlapping position if needed
      const { x, y } = findNonOverlappingPosition(element, newX, newY);

      onUpdateElement({
        ...element,
        x_position: x,
        y_position: y
      });
    };

    // Handle element resize
    const handleElementResize = (
      elementId: string,
      width: number,
      height: number
    ) => {
      if (readOnly) return;

      const element = elements.find(el => el.id === elementId);
      if (!element) return;

      // Snap dimensions to grid
      const newWidth = Math.max(20, snapToGrid(width));
      const newHeight = Math.max(20, snapToGrid(height));

      // Check if the new size would cause an overlap
      if (checkOverlap(element, element.x_position, element.y_position, newWidth, newHeight)) {
        // If it would cause an overlap, find a new position
        const { x, y } = findNonOverlappingPosition(
          { ...element, width: newWidth, height: newHeight },
          element.x_position,
          element.y_position
        );

        onUpdateElement({
          ...element,
          width: newWidth,
          height: newHeight,
          x_position: x,
          y_position: y
        });
      } else {
        // If no overlap, just update the size
        onUpdateElement({
          ...element,
          width: newWidth,
          height: newHeight
        });
      }
    };

    // Create grid background pattern
    const renderGrid = () => {
      if (readOnly) return null;

      return (
        <div className="absolute inset-0 pointer-events-none">
          <div 
            className="w-full h-full" 
            style={{
              backgroundImage: 'linear-gradient(to right, #f0f0f0 1px, transparent 1px), linear-gradient(to bottom, #f0f0f0 1px, transparent 1px)',
              backgroundSize: `${GRID_SIZE}px ${GRID_SIZE}px`,
              backgroundPosition: '0 0'
            }}
          />
        </div>
      );
    };

    return (
      <div
        ref={canvasRef}
        className={`relative w-full bg-white border border-gray-300 shadow-sm ${
          isOver ? 'bg-gray-50' : ''
        }`}
        style={{ height: '1056px' }} // A4 size at 96 DPI
        onClick={handleCanvasClick}
      >
        {renderGrid()}
        {elements.map((element) => (
          <FormElement
            key={element.id}
            element={element}
            isSelected={selectedElement?.id === element.id}
            onSelect={() => onSelectElement(element)}
            onMove={handleElementMove}
            onResize={handleElementResize}
            onDelete={() => onDeleteElement(element.id)}
            readOnly={readOnly}
          />
        ))}
      </div>
    );
  }
);

FormCanvas.displayName = 'FormCanvas';

export default FormCanvas;
