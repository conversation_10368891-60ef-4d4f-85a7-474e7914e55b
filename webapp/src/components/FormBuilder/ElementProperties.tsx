import React, { useState } from 'react';
import { FormElement } from './FormBuilder';

interface ElementPropertiesProps {
  element: FormElement;
  signers?: Array<{ id: string; name: string; email: string; role?: string }>;
  onUpdateElement: (element: FormElement) => void;
  onDeleteElement: (elementId: string) => void;
}

const ElementProperties: React.FC<ElementPropertiesProps> = ({
  element,
  signers = [],
  onUpdateElement,
  onDeleteElement
}) => {
  const [activeTab, setActiveTab] = useState('general');

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    // Handle checkbox inputs
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      onUpdateElement({
        ...element,
        [name]: checked
      });
      return;
    }
    
    // Handle number inputs
    if (type === 'number') {
      onUpdateElement({
        ...element,
        [name]: parseFloat(value) || 0
      });
      return;
    }
    
    // Handle all other inputs
    onUpdateElement({
      ...element,
      [name]: value
    });
  };

  // Handle delete button click
  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this element?')) {
      onDeleteElement(element.id);
    }
  };

  // Render general properties tab
  const renderGeneralProperties = () => {
    return (
      <div className="space-y-4">
        {/* Element Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Element Type
          </label>
          <div className="text-sm bg-gray-100 p-2 rounded">
            {element.element_type === 'field' ? `Field (${element.field_type})` : element.element_type}
          </div>
        </div>

        {/* Position */}
        <div className="grid grid-cols-2 gap-2">
          <div>
            <label htmlFor="x_position" className="block text-sm font-medium text-gray-700 mb-1">
              X Position
            </label>
            <input
              type="number"
              id="x_position"
              name="x_position"
              value={element.x_position}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
            />
          </div>
          <div>
            <label htmlFor="y_position" className="block text-sm font-medium text-gray-700 mb-1">
              Y Position
            </label>
            <input
              type="number"
              id="y_position"
              name="y_position"
              value={element.y_position}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
            />
          </div>
        </div>

        {/* Size */}
        <div className="grid grid-cols-2 gap-2">
          <div>
            <label htmlFor="width" className="block text-sm font-medium text-gray-700 mb-1">
              Width
            </label>
            <input
              type="number"
              id="width"
              name="width"
              value={element.width}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
            />
          </div>
          <div>
            <label htmlFor="height" className="block text-sm font-medium text-gray-700 mb-1">
              Height
            </label>
            <input
              type="number"
              id="height"
              name="height"
              value={element.height}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
            />
          </div>
        </div>

        {/* Z-Index */}
        <div>
          <label htmlFor="z_index" className="block text-sm font-medium text-gray-700 mb-1">
            Z-Index
          </label>
          <input
            type="number"
            id="z_index"
            name="z_index"
            value={element.z_index}
            onChange={handleChange}
            className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
          />
        </div>

        {/* Page Number */}
        <div>
          <label htmlFor="page_number" className="block text-sm font-medium text-gray-700 mb-1">
            Page Number
          </label>
          <input
            type="number"
            id="page_number"
            name="page_number"
            value={element.page_number}
            onChange={handleChange}
            min={1}
            className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
          />
        </div>
      </div>
    );
  };

  // Render content properties tab
  const renderContentProperties = () => {
    // Different content properties based on element type
    switch (element.element_type) {
      case 'text':
      case 'heading':
      case 'paragraph':
        return (
          <div className="space-y-4">
            <div>
              <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-1">
                Text Content
              </label>
              <textarea
                id="content"
                name="content"
                value={element.content || ''}
                onChange={handleChange}
                rows={4}
                className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
              />
            </div>
          </div>
        );
      
      case 'image':
        return (
          <div className="space-y-4">
            <div>
              <label htmlFor="image_path" className="block text-sm font-medium text-gray-700 mb-1">
                Image URL
              </label>
              <input
                type="text"
                id="image_path"
                name="image_path"
                value={element.image_path || ''}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
              />
            </div>
          </div>
        );
      
      case 'field':
        return renderFieldProperties();
      
      default:
        return (
          <div className="text-sm text-gray-500">
            No content properties available for this element type.
          </div>
        );
    }
  };

  // Render field-specific properties
  const renderFieldProperties = () => {
    if (!element.field_type) return null;

    return (
      <div className="space-y-4">
        {/* Field Name */}
        <div>
          <label htmlFor="field_name" className="block text-sm font-medium text-gray-700 mb-1">
            Field Name
          </label>
          <input
            type="text"
            id="field_name"
            name="field_name"
            value={element.field_name || ''}
            onChange={handleChange}
            className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
          />
        </div>

        {/* Required */}
        <div className="flex items-center">
          <input
            type="checkbox"
            id="is_required"
            name="is_required"
            checked={element.is_required || false}
            onChange={handleChange}
            className="mr-2"
          />
          <label htmlFor="is_required" className="text-sm font-medium text-gray-700">
            Required Field
          </label>
        </div>

        {/* Placeholder (for text fields) */}
        {['text', 'date'].includes(element.field_type) && (
          <div>
            <label htmlFor="placeholder" className="block text-sm font-medium text-gray-700 mb-1">
              Placeholder
            </label>
            <input
              type="text"
              id="placeholder"
              name="placeholder"
              value={element.placeholder || ''}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
            />
          </div>
        )}

        {/* Signer Assignment */}
        {['signature', 'initial', 'text', 'checkbox', 'radio', 'dropdown', 'date'].includes(element.field_type) && (
          <div>
            <label htmlFor="signer_id" className="block text-sm font-medium text-gray-700 mb-1">
              Assign to Signer
            </label>
            <select
              id="signer_id"
              name="signer_id"
              value={element.signer_id || ''}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
            >
              <option value="">Not assigned</option>
              {signers.map((signer) => (
                <option key={signer.id} value={signer.id}>
                  {signer.name} ({signer.role || 'No role'})
                </option>
              ))}
            </select>
          </div>
        )}
      </div>
    );
  };

  // Render style properties tab
  const renderStyleProperties = () => {
    return (
      <div className="space-y-4">
        {/* Background Color */}
        <div>
          <label htmlFor="background_color" className="block text-sm font-medium text-gray-700 mb-1">
            Background Color
          </label>
          <div className="flex">
            <input
              type="color"
              id="background_color_picker"
              value={element.background_color || '#ffffff'}
              onChange={(e) => {
                onUpdateElement({
                  ...element,
                  background_color: e.target.value
                });
              }}
              className="w-10 h-10 border border-gray-300 rounded mr-2"
            />
            <input
              type="text"
              id="background_color"
              name="background_color"
              value={element.background_color || ''}
              onChange={handleChange}
              placeholder="#ffffff"
              className="flex-1 border border-gray-300 rounded px-2 py-1 text-sm"
            />
          </div>
        </div>

        {/* Border */}
        <div className="grid grid-cols-2 gap-2">
          <div>
            <label htmlFor="border_style" className="block text-sm font-medium text-gray-700 mb-1">
              Border Style
            </label>
            <select
              id="border_style"
              name="border_style"
              value={element.border_style || ''}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
            >
              <option value="">None</option>
              <option value="solid">Solid</option>
              <option value="dashed">Dashed</option>
              <option value="dotted">Dotted</option>
            </select>
          </div>
          <div>
            <label htmlFor="border_width" className="block text-sm font-medium text-gray-700 mb-1">
              Border Width
            </label>
            <input
              type="number"
              id="border_width"
              name="border_width"
              value={element.border_width || 0}
              onChange={handleChange}
              min={0}
              className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
            />
          </div>
        </div>

        {/* Border Color */}
        <div>
          <label htmlFor="border_color" className="block text-sm font-medium text-gray-700 mb-1">
            Border Color
          </label>
          <div className="flex">
            <input
              type="color"
              id="border_color_picker"
              value={element.border_color || '#000000'}
              onChange={(e) => {
                onUpdateElement({
                  ...element,
                  border_color: e.target.value
                });
              }}
              className="w-10 h-10 border border-gray-300 rounded mr-2"
            />
            <input
              type="text"
              id="border_color"
              name="border_color"
              value={element.border_color || ''}
              onChange={handleChange}
              placeholder="#000000"
              className="flex-1 border border-gray-300 rounded px-2 py-1 text-sm"
            />
          </div>
        </div>

        {/* Font properties for text elements */}
        {['text', 'heading', 'paragraph'].includes(element.element_type) && (
          <>
            <div>
              <label htmlFor="font_family" className="block text-sm font-medium text-gray-700 mb-1">
                Font Family
              </label>
              <select
                id="font_family"
                name="font_family"
                value={element.font_family || ''}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
              >
                <option value="">Default</option>
                <option value="Arial, sans-serif">Arial</option>
                <option value="Helvetica, sans-serif">Helvetica</option>
                <option value="Times New Roman, serif">Times New Roman</option>
                <option value="Georgia, serif">Georgia</option>
                <option value="Courier New, monospace">Courier New</option>
                <option value="Verdana, sans-serif">Verdana</option>
              </select>
            </div>

            <div>
              <label htmlFor="font_size" className="block text-sm font-medium text-gray-700 mb-1">
                Font Size
              </label>
              <input
                type="number"
                id="font_size"
                name="font_size"
                value={element.font_size || ''}
                onChange={handleChange}
                min={8}
                className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
              />
            </div>

            <div>
              <label htmlFor="font_color" className="block text-sm font-medium text-gray-700 mb-1">
                Font Color
              </label>
              <div className="flex">
                <input
                  type="color"
                  id="font_color_picker"
                  value={element.font_color || '#000000'}
                  onChange={(e) => {
                    onUpdateElement({
                      ...element,
                      font_color: e.target.value
                    });
                  }}
                  className="w-10 h-10 border border-gray-300 rounded mr-2"
                />
                <input
                  type="text"
                  id="font_color"
                  name="font_color"
                  value={element.font_color || ''}
                  onChange={handleChange}
                  placeholder="#000000"
                  className="flex-1 border border-gray-300 rounded px-2 py-1 text-sm"
                />
              </div>
            </div>
          </>
        )}
      </div>
    );
  };

  return (
    <div className="element-properties">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium">Element Properties</h3>
        <button
          onClick={handleDelete}
          className="text-red-600 hover:text-red-800 text-sm"
        >
          Delete
        </button>
      </div>

      {/* Tabs */}
      <div className="flex border-b mb-4">
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'general' ? 'border-b-2 border-primary-500 text-primary-600' : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('general')}
        >
          General
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'content' ? 'border-b-2 border-primary-500 text-primary-600' : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('content')}
        >
          Content
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'style' ? 'border-b-2 border-primary-500 text-primary-600' : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('style')}
        >
          Style
        </button>
      </div>

      {/* Tab content */}
      <div className="overflow-y-auto max-h-[calc(100vh-300px)]">
        {activeTab === 'general' && renderGeneralProperties()}
        {activeTab === 'content' && renderContentProperties()}
        {activeTab === 'style' && renderStyleProperties()}
      </div>
    </div>
  );
};

export default ElementProperties;