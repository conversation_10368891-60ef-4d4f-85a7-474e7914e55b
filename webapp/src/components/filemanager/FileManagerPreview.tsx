import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';
import { FileItem } from './FileManagerTypes';

interface FileManagerPreviewProps {
  file: FileItem | null;
  onClose: () => void;
  isOpen: boolean;
}

const FileManagerPreview: React.FC<FileManagerPreviewProps> = ({ file, onClose, isOpen }) => {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [previewType, setPreviewType] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [textContent, setTextContent] = useState<string | null>(null);

  useEffect(() => {
    if (!file || !isOpen) {
      setPreviewUrl(null);
      setPreviewType('');
      setTextContent(null);
      return;
    }

    const fetchPreview = async () => {
      setLoading(true);
      setError(null);

      try {
        // Determine preview type based on file mime type or extension
        const mimeType = file.mime_type || '';
        const fileExtension = file.file_type?.toLowerCase() || '';
        
        // Set preview type
        if (mimeType.startsWith('image/')) {
          setPreviewType('image');
        } else if (mimeType === 'application/pdf') {
          setPreviewType('pdf');
        } else if (
          mimeType.startsWith('text/') || 
          ['txt', 'md', 'json', 'csv', 'xml', 'html', 'css', 'js', 'ts', 'jsx', 'tsx'].includes(fileExtension)
        ) {
          setPreviewType('text');
          
          // For text files, fetch the content
          const response = await axios.get(`${API_URL}/documents/${file.id}/content`, {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('token')}`
            }
          });
          
          setTextContent(response.data.content);
        } else if (
          ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(fileExtension) ||
          mimeType.includes('officedocument')
        ) {
          setPreviewType('office');
        } else if (
          ['mp4', 'webm', 'ogg'].includes(fileExtension) ||
          mimeType.startsWith('video/')
        ) {
          setPreviewType('video');
        } else if (
          ['mp3', 'wav', 'ogg'].includes(fileExtension) ||
          mimeType.startsWith('audio/')
        ) {
          setPreviewType('audio');
        } else {
          setPreviewType('unsupported');
        }

        // Set preview URL for supported types
        if (['image', 'pdf', 'video', 'audio'].includes(previewType)) {
          setPreviewUrl(`${API_URL}/documents/${file.id}/download`);
        }
      } catch (err: any) {
        console.error('Error fetching preview:', err);
        setError(err.response?.data?.error || 'Failed to load preview');
      } finally {
        setLoading(false);
      }
    };

    fetchPreview();
  }, [file, isOpen]);

  if (!isOpen || !file) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  {file.name}
                </h3>

                {loading ? (
                  <div className="flex justify-center items-center h-96">
                    <svg className="animate-spin h-10 w-10 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </div>
                ) : error ? (
                  <div className="flex flex-col items-center justify-center h-96">
                    <svg className="h-16 w-16 text-red-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    <p className="text-red-500">{error}</p>
                  </div>
                ) : (
                  <div className="preview-container h-96 overflow-auto">
                    {previewType === 'image' && previewUrl && (
                      <img 
                        src={previewUrl} 
                        alt={file.name} 
                        className="max-w-full max-h-full mx-auto"
                        onError={() => setError('Failed to load image')}
                      />
                    )}

                    {previewType === 'pdf' && previewUrl && (
                      <iframe 
                        src={`${previewUrl}#toolbar=0`} 
                        className="w-full h-full" 
                        title={file.name}
                        onError={() => setError('Failed to load PDF')}
                      />
                    )}

                    {previewType === 'text' && textContent !== null && (
                      <pre className="whitespace-pre-wrap text-sm p-4 bg-gray-50 rounded h-full overflow-auto">
                        {textContent}
                      </pre>
                    )}

                    {previewType === 'video' && previewUrl && (
                      <video 
                        src={previewUrl} 
                        controls 
                        className="max-w-full max-h-full mx-auto"
                        onError={() => setError('Failed to load video')}
                      />
                    )}

                    {previewType === 'audio' && previewUrl && (
                      <audio 
                        src={previewUrl} 
                        controls 
                        className="w-full mt-20"
                        onError={() => setError('Failed to load audio')}
                      />
                    )}

                    {previewType === 'office' && (
                      <div className="flex flex-col items-center justify-center h-full">
                        <svg className="h-16 w-16 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <p className="text-gray-500 mb-4">Preview not available for Office documents</p>
                        <a 
                          href={`${API_URL}/documents/${file.id}/download`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                        >
                          Download to view
                        </a>
                      </div>
                    )}

                    {previewType === 'unsupported' && (
                      <div className="flex flex-col items-center justify-center h-full">
                        <svg className="h-16 w-16 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <p className="text-gray-500 mb-4">Preview not available for this file type</p>
                        <a 
                          href={`${API_URL}/documents/${file.id}/download`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                        >
                          Download file
                        </a>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              onClick={onClose}
            >
              Close
            </button>
            <a
              href={`${API_URL}/documents/${file.id}/download`}
              target="_blank"
              rel="noopener noreferrer"
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Download
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FileManagerPreview;