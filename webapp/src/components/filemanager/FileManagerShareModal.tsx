import React, { useState, useEffect, useContext } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import { FileItem } from './FileManagerTypes';

interface User {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
}

interface Role {
  id: string;
  name: string;
  description: string;
}

interface FileManagerShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  item: FileItem | null;
}

const FileManagerShareModal: React.FC<FileManagerShareModalProps> = ({
  isOpen,
  onClose,
  item
}) => {
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [selectedUser, setSelectedUser] = useState<string>('');
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [shareType, setShareType] = useState<'user' | 'role'>('user');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [shareLink, setShareLink] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  // Fetch users and roles
  useEffect(() => {
    if (!isOpen || !item) return;

    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch users
        const usersResponse = await axios.get(`${API_URL}/users/farm/${currentFarm?.id}`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });

        setUsers(usersResponse.data || []);

        // Fetch roles
        const rolesResponse = await axios.get(`${API_URL}/roles/farm/${currentFarm?.id}`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });

        setRoles(rolesResponse.data || []);

        // Generate share link if it's a file and not in a secure folder
        if (item.type === 'file') {
          // Check if the file is in a secure folder
          if (item.is_secure) {
            setError('This file is in a secure folder and cannot be shared with a public link.');
          } else {
            const linkResponse = await axios.get(`${API_URL}/documents/${item.id}/share-link`, {
              headers: {
                Authorization: `Bearer ${localStorage.getItem('token')}`
              }
            });

            if (linkResponse.data && linkResponse.data.link) {
              setShareLink(linkResponse.data.link);
            }
          }
        }
      } catch (err: any) {
        console.error('Error fetching data:', err);
        setError(err.response?.data?.error || 'Failed to load data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [isOpen, item, currentFarm?.id]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccessMessage(null);

    if (!item) return;

    try {
      // Validate form
      if (shareType === 'user' && !selectedUser) {
        setError('Please select a user');
        return;
      }

      if (shareType === 'role' && !selectedRole) {
        setError('Please select a role');
        return;
      }

      const entityType = item.type;
      const entityId = item.id;

      // Prepare data
      const data = {
        userId: shareType === 'user' ? selectedUser : undefined,
        roleId: shareType === 'role' ? selectedRole : undefined,
        canView: true, // Always grant view permission when sharing
        canEdit: false,
        canDelete: false,
        canShare: false
      };

      // Send request
      await axios.post(`${API_URL}/documents/${entityType}/${entityId}/permissions`, data, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      setSuccessMessage('Item shared successfully');

      // Reset form
      setSelectedUser('');
      setSelectedRole('');
      setShareType('user');
    } catch (err: any) {
      console.error('Error sharing item:', err);
      setError(err.response?.data?.error || 'Failed to share item. Please try again later.');
    }
  };

  // Copy share link to clipboard
  const copyShareLink = () => {
    if (shareLink) {
      navigator.clipboard.writeText(shareLink)
        .then(() => {
          setSuccessMessage('Link copied to clipboard');
        })
        .catch(err => {
          console.error('Failed to copy link:', err);
          setError('Failed to copy link to clipboard');
        });
    }
  };

  if (!isOpen || !item) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Share: {item.name}
                </h3>

                {/* Error message */}
                {error && (
                  <div className="mb-4 p-4 text-sm text-red-700 bg-red-100 rounded-md">
                    {error}
                  </div>
                )}

                {/* Success message */}
                {successMessage && (
                  <div className="mb-4 p-4 text-sm text-green-700 bg-green-100 rounded-md">
                    {successMessage}
                  </div>
                )}

                {/* Loading state */}
                {loading ? (
                  <div className="flex justify-center items-center h-64">
                    <svg
                      className="animate-spin h-8 w-8 text-primary-500"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  </div>
                ) : (
                  <div>
                    {/* Share link section (for files only) */}
                    {item.type === 'file' && (
                      <div className="mb-6">
                        <h4 className="text-md font-medium text-gray-900 mb-2">Share Link</h4>
                        <div className="flex items-center">
                          <input
                            type="text"
                            readOnly
                            value={shareLink || 'No share link available'}
                            className="flex-1 p-2 border border-gray-300 rounded-l-md text-sm"
                          />
                          <button
                            onClick={copyShareLink}
                            disabled={!shareLink}
                            className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-r-md text-sm disabled:bg-gray-400"
                          >
                            Copy
                          </button>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          Anyone with this link can view this file
                        </p>
                      </div>
                    )}

                    {/* Share with user/role form */}
                    <form onSubmit={handleSubmit}>
                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Share With
                        </label>
                        <div className="flex space-x-4">
                          <label className="inline-flex items-center">
                            <input
                              type="radio"
                              className="form-radio h-4 w-4 text-primary-600"
                              value="user"
                              checked={shareType === 'user'}
                              onChange={() => setShareType('user')}
                            />
                            <span className="ml-2 text-sm text-gray-700">User</span>
                          </label>
                          <label className="inline-flex items-center">
                            <input
                              type="radio"
                              className="form-radio h-4 w-4 text-primary-600"
                              value="role"
                              checked={shareType === 'role'}
                              onChange={() => setShareType('role')}
                            />
                            <span className="ml-2 text-sm text-gray-700">Role</span>
                          </label>
                        </div>
                      </div>

                      {shareType === 'user' ? (
                        <div className="mb-4">
                          <label htmlFor="user" className="block text-sm font-medium text-gray-700 mb-2">
                            Select User
                          </label>
                          <select
                            id="user"
                            value={selectedUser}
                            onChange={(e) => setSelectedUser(e.target.value)}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                          >
                            <option value="">Select a user</option>
                            {users.map((user) => (
                              <option key={user.id} value={user.id}>
                                {user.first_name} {user.last_name} ({user.email})
                              </option>
                            ))}
                          </select>
                        </div>
                      ) : (
                        <div className="mb-4">
                          <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-2">
                            Select Role
                          </label>
                          <select
                            id="role"
                            value={selectedRole}
                            onChange={(e) => setSelectedRole(e.target.value)}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                          >
                            <option value="">Select a role</option>
                            {roles.map((role) => (
                              <option key={role.id} value={role.id}>
                                {role.name}
                              </option>
                            ))}
                          </select>
                        </div>
                      )}

                      <div className="mt-6">
                        <button
                          type="submit"
                          className="w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                        >
                          Share
                        </button>
                      </div>
                    </form>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              onClick={onClose}
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FileManagerShareModal;
