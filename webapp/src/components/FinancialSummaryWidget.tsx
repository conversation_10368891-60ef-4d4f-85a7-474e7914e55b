import { useState, useEffect } from 'react';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions
} from 'chart.js';
import axios from 'axios';
import { API_URL } from '../config';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface FinancialData {
  month: string;
  income: number;
  expenses: number;
}

const FinancialSummaryWidget = () => {
  const [financialData, setFinancialData] = useState<FinancialData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [year, setYear] = useState<number>(new Date().getFullYear());

  // Fetch financial data
  useEffect(() => {
    const fetchFinancialData = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/api/transactions/summary`, {
          params: { year },
          withCredentials: true
        });
        
        if (response.data && response.data.summary) {
          setFinancialData(response.data.summary);
        } else {
          setFinancialData([]);
        }
      } catch (err: any) {
        console.error('Error fetching financial data:', err);
        setError('Failed to load financial data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchFinancialData();
  }, [year]);

  // Handle year change
  const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setYear(parseInt(e.target.value));
  };

  // Prepare chart data
  const chartData = {
    labels: financialData.map(item => item.month),
    datasets: [
      {
        label: 'Income',
        data: financialData.map(item => item.income),
        backgroundColor: 'rgba(75, 192, 192, 0.6)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1
      },
      {
        label: 'Expenses',
        data: financialData.map(item => item.expenses),
        backgroundColor: 'rgba(255, 99, 132, 0.6)',
        borderColor: 'rgba(255, 99, 132, 1)',
        borderWidth: 1
      }
    ]
  };

  // Chart options
  const chartOptions: ChartOptions<'bar'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: `Financial Summary for ${year}`,
        font: {
          size: 16
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              label += new Intl.NumberFormat('en-US', { 
                style: 'currency', 
                currency: 'USD' 
              }).format(context.parsed.y);
            }
            return label;
          }
        }
      }
    },
    scales: {
      x: {
        title: {
          display: true,
          text: 'Month'
        }
      },
      y: {
        title: {
          display: true,
          text: 'Amount (USD)'
        },
        ticks: {
          callback: function(value) {
            return new Intl.NumberFormat('en-US', { 
              style: 'currency', 
              currency: 'USD',
              maximumSignificantDigits: 3
            }).format(Number(value));
          }
        }
      }
    }
  };

  // Calculate totals
  const totalIncome = financialData.reduce((sum, item) => sum + item.income, 0);
  const totalExpenses = financialData.reduce((sum, item) => sum + item.expenses, 0);
  const netProfit = totalIncome - totalExpenses;

  return (
    <div className="h-full flex flex-col">
      <div className="mb-4 flex justify-between items-center">
        <div className="text-sm font-medium text-gray-500">
          Select Year:
          <select
            value={year}
            onChange={handleYearChange}
            className="ml-2 p-1 border border-gray-300 rounded"
          >
            {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i).map(year => (
              <option key={year} value={year}>{year}</option>
            ))}
          </select>
        </div>
        
        <div className="flex space-x-4">
          <div className="text-sm">
            <span className="font-medium text-gray-500">Income:</span>
            <span className="ml-1 text-green-600">
              {new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(totalIncome)}
            </span>
          </div>
          <div className="text-sm">
            <span className="font-medium text-gray-500">Expenses:</span>
            <span className="ml-1 text-red-600">
              {new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(totalExpenses)}
            </span>
          </div>
          <div className="text-sm">
            <span className="font-medium text-gray-500">Net Profit:</span>
            <span className={`ml-1 ${netProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(netProfit)}
            </span>
          </div>
        </div>
      </div>

      <div className="flex-grow relative">
        {loading ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <p className="text-sm text-gray-500">Loading financial data...</p>
          </div>
        ) : error ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <p className="text-sm text-red-500">{error}</p>
          </div>
        ) : financialData.length === 0 ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <p className="text-sm text-gray-500">No financial data available for {year}.</p>
          </div>
        ) : (
          <Bar data={chartData} options={chartOptions} />
        )}
      </div>
    </div>
  );
};

export default FinancialSummaryWidget;