import React, { HTMLAttributes, SelectHTMLAttributes, forwardRef } from 'react';

export interface SelectProps extends HTMLAttributes<HTMLDivElement> {
  value?: string;
  onValueChange?: (value: string) => void;
}

export const Select = forwardRef<HTMLDivElement, SelectProps>(
  ({ className = '', children, value, onValueChange, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={`relative ${className}`}
        {...props}
      >
        {React.Children.map(children, child => {
          if (React.isValidElement(child)) {
            return React.cloneElement(child, {
              value,
              onValueChange,
            } as any);
          }
          return child;
        })}
      </div>
    );
  }
);

Select.displayName = 'Select';

export interface SelectTriggerProps extends HTMLAttributes<HTMLDivElement> {
  value?: string;
}

export const SelectTrigger = forwardRef<HTMLDivElement, SelectTriggerProps>(
  ({ className = '', children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={`flex items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 cursor-pointer ${className}`}
        {...props}
      >
        {children}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-4 w-4 opacity-50"
        >
          <path d="m6 9 6 6 6-6" />
        </svg>
      </div>
    );
  }
);

SelectTrigger.displayName = 'SelectTrigger';

export interface SelectValueProps extends HTMLAttributes<HTMLSpanElement> {
  placeholder?: string;
}

export const SelectValue = forwardRef<HTMLSpanElement, SelectValueProps>(
  ({ className = '', placeholder, children, ...props }, ref) => {
    return (
      <span
        ref={ref}
        className={`block truncate ${children ? '' : 'text-gray-500'} ${className}`}
        {...props}
      >
        {children || placeholder}
      </span>
    );
  }
);

SelectValue.displayName = 'SelectValue';

export interface SelectContentProps extends HTMLAttributes<HTMLDivElement> {}

export const SelectContent = forwardRef<HTMLDivElement, SelectContentProps>(
  ({ className = '', children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={`absolute z-50 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm ${className}`}
        {...props}
      >
        {children}
      </div>
    );
  }
);

SelectContent.displayName = 'SelectContent';

export interface SelectItemProps extends HTMLAttributes<HTMLDivElement> {
  value: string;
}

export const SelectItem = forwardRef<HTMLDivElement, SelectItemProps>(
  ({ className = '', children, value, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={`relative cursor-pointer select-none py-2 pl-10 pr-4 text-gray-900 hover:bg-gray-100 ${className}`}
        {...props}
        data-value={value}
      >
        <span className="block truncate">{children}</span>
      </div>
    );
  }
);

SelectItem.displayName = 'SelectItem';