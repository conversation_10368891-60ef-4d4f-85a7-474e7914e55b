import React, { ButtonHTMLAttributes, forwardRef } from 'react';

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'default', size = 'md', children, ...props }, ref) => {
    // Base styles
    let variantClasses = '';
    let sizeClasses = '';

    // Variant styles
    switch (variant) {
      case 'primary':
        variantClasses = 'bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500';
        break;
      case 'secondary':
        variantClasses = 'bg-secondary-500 text-white hover:bg-secondary-600 focus:ring-secondary-500';
        break;
      case 'outline':
        variantClasses = 'bg-transparent border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500';
        break;
      case 'ghost':
        variantClasses = 'bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-500';
        break;
      case 'destructive':
        variantClasses = 'bg-red-500 text-white hover:bg-red-600 focus:ring-red-500';
        break;
      default: // 'default'
        variantClasses = 'bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-500';
        break;
    }

    // Size styles
    switch (size) {
      case 'sm':
        sizeClasses = 'text-sm px-3 py-1.5 rounded';
        break;
      case 'lg':
        sizeClasses = 'text-lg px-6 py-3 rounded-md';
        break;
      default: // 'md'
        sizeClasses = 'text-base px-4 py-2 rounded-md';
        break;
    }

    return (
      <button
        ref={ref}
        className={`inline-flex items-center justify-center font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ${variantClasses} ${sizeClasses} ${className || ''}`}
        {...props}
      >
        {children}
      </button>
    );
  }
);

Button.displayName = "Button";

export { Button };