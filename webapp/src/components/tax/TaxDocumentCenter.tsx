import React, {useState, useEffect} from 'react';
import axios from 'axios';
import {API_URL} from '../../config';
import {useAuth} from '../../context/AuthContext';
import {useFarm} from '../../context/FarmContext';
import LoadingSpinner from '../LoadingSpinner';
import TaxDocument from "../../../server/models/TaxDocument";

interface TaxDocument {
    id: string;
    title: string;
    description?: string;
    documentType: string;
    category: string;
    taxYear: number;
    fileName?: string;
    fileSize?: number;
    fileType?: string;
    status: string;
    submittedDate?: string;
    createdBy?: {
        id: string;
        name: string;
        email: string;
    };
    createdAt: string;
    updatedAt: string;
    version?: number;
}

interface TaxDocumentCenterProps {
    farmId: string,
    year?: number,
    onDocumentSelect?: (document: TaxDocument) => void,
    onClose?: () => void,
    canCreate?: boolean,
    canEdit?: boolean,
    canDelete?: boolean
}

const TaxDocumentCenter: React.FC<TaxDocumentCenterProps> = ({
                                                                 farmId,
                                                                 year = new Date().getFullYear(),
                                                                 onDocumentSelect,
                                                                 onClose,
                                                                 canCreate,
                                                                 canEdit,
                                                                 canDelete
                                                             }) => {
    const {user, token} = useAuth();
    const {currentFarm} = useFarm();
    const [documents, setDocuments] = useState<TaxDocument[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [searchTerm, setSearchTerm] = useState<string>('');
    const [selectedCategory, setSelectedCategory] = useState<string>('all');
    const [selectedType, setSelectedType] = useState<string>('all');
    const [selectedStatus, setSelectedStatus] = useState<string>('all');
    const [selectedYear, setSelectedYear] = useState<number>(year);
    const [sortBy, setSortBy] = useState<string>('createdAt');
    const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
    const [previewDocument, setPreviewDocument] = useState<TaxDocument | null>(null);
    const [showUploadModal, setShowUploadModal] = useState<boolean>(false);

    // Document categories
    const categories = [
        {id: 'all', name: 'All Categories'},
        {id: 'income', name: 'Income'},
        {id: 'expense', name: 'Expenses'},
        {id: 'deduction', name: 'Deductions'},
        {id: 'credit', name: 'Tax Credits'},
        {id: 'payroll', name: 'Payroll'},
        {id: 'property', name: 'Property'},
        {id: 'filing', name: 'Tax Filings'},
        {id: 'other', name: 'Other'}
    ];

    // Document types
    const documentTypes = [
        {id: 'all', name: 'All Types'},
        {id: 'w2', name: 'W-2 Forms'},
        {id: '1099', name: '1099 Forms'},
        {id: 'receipt', name: 'Receipts'},
        {id: 'invoice', name: 'Invoices'},
        {id: 'statement', name: 'Statements'},
        {id: 'form', name: 'Tax Forms'},
        {id: 'letter', name: 'IRS Letters'},
        {id: 'notice', name: 'Tax Notices'},
        {id: 'other', name: 'Other Documents'}
    ];

    // Document statuses
    const statuses = [
        {id: 'all', name: 'All Statuses'},
        {id: 'draft', name: 'Draft'},
        {id: 'pending', name: 'Pending'},
        {id: 'approved', name: 'Approved'},
        {id: 'rejected', name: 'Rejected'},
        {id: 'filed', name: 'Filed'},
        {id: 'archived', name: 'Archived'}
    ];

    useEffect(() => {
        if (user && farmId) {
            fetchTaxDocuments();
        }
    }, [user, farmId, selectedYear]);

    const fetchTaxDocuments = async () => {
        try {
            setLoading(true);
            setError(null);

            const response = await axios.get(`${API_URL}/api/tax/farms/${farmId}/tax-documents`, {
                headers: {Authorization: `Bearer ${token}`},
                params: {year: selectedYear}
            });

            if (response.data && response.data.taxDocuments) {
                setDocuments(response.data.taxDocuments);
            } else {
                throw new Error('Invalid response format from server');
            }
        } catch (err: any) {
            console.error('Error fetching tax documents:', err);

            // Handle authentication errors without causing logout
            if (err.response && err.response.status === 401) {
                setError('Authentication error. Please refresh the page and try again.');
            } else {
                setError('Failed to load tax documents. Please try again later.');
            }
        } finally {
            setLoading(false);
        }
    };

    const handlePreview = (document: TaxDocument) => {
        setPreviewDocument(document);
    };

    const handleClosePreview = () => {
        setPreviewDocument(null);
    };

    const handleDownload = async (taxDocument: TaxDocument) => {
        try {
            if (!taxDocument.fileName) {
                throw new Error('No file available for download');
            }

            const response = await axios.get(`${API_URL}/api/tax/documents/${taxDocument.id}/download`, {
                headers: {Authorization: `Bearer ${token}`},
                responseType: 'blob'
            });

            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', taxDocument.fileName);
            document.body.appendChild(link);
            link.click();
            link.remove();
        } catch (err) {
            console.error('Error downloading document:', err);
            setError('Failed to download document. Please try again later.');
        }
    };

    const filteredDocuments = documents.filter(doc => {
        const matchesSearch = searchTerm === '' ||
            doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (doc.description && doc.description.toLowerCase().includes(searchTerm.toLowerCase()));

        const matchesCategory = selectedCategory === 'all' || doc.category === selectedCategory;
        const matchesType = selectedType === 'all' || doc.documentType === selectedType;
        const matchesStatus = selectedStatus === 'all' || doc.status === selectedStatus;

        return matchesSearch && matchesCategory && matchesType && matchesStatus;
    });

    const getStatusBadgeClass = (status: string) => {
        switch (status) {
            case 'approved':
                return 'bg-green-100 text-green-800';
            case 'pending':
                return 'bg-yellow-100 text-yellow-800';
            case 'rejected':
                return 'bg-red-100 text-red-800';
            case 'filed':
                return 'bg-blue-100 text-blue-800';
            case 'archived':
                return 'bg-gray-100 text-gray-800';
            case 'draft':
                return 'bg-purple-100 text-purple-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getFileIcon = (fileType?: string) => {
        if (!fileType) return '📄';

        const type = fileType.toLowerCase();
        if (type.includes('pdf')) return '📕';
        if (type.includes('image') || type.includes('jpg') || type.includes('png')) return '🖼️';
        if (type.includes('excel') || type.includes('spreadsheet') || type.includes('csv')) return '📊';
        if (type.includes('word') || type.includes('document')) return '📝';
        return '📄';
    };

    const formatFileSize = (bytes?: number) => {
        if (!bytes) return 'Unknown';

        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        if (bytes === 0) return '0 Byte';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i)) + ' ' + sizes[i];
    };

    if (loading && documents.length === 0) {
        return (
            <div className="flex justify-center items-center h-64">
                <LoadingSpinner/>
            </div>
        );
    }

    return (
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
                <div>
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Tax Document Center</h3>
                    <p className="mt-1 max-w-2xl text-sm text-gray-500">
                        Manage, organize, and access all your tax-related documents in one place.
                    </p>
                </div>
                <div className="flex space-x-2">
                    <button
                        type="button"
                        onClick={() => setShowUploadModal(true)}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                        Upload Document
                    </button>
                    {onClose && (
                        <button
                            type="button"
                            onClick={onClose}
                            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                        >
                            Close
                        </button>
                    )}
                </div>
            </div>

            {error && (
                <div className="bg-red-100 border-l-4 border-red-400 p-4 mb-4">
                    <div className="flex">
                        <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                 fill="currentColor" aria-hidden="true">
                                <path fillRule="evenodd"
                                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                      clipRule="evenodd"/>
                            </svg>
                        </div>
                        <div className="ml-3">
                            <p className="text-sm text-red-700">{error}</p>
                        </div>
                    </div>
                </div>
            )}

            <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
                {/* Filters and Search */}
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5 mb-6">
                    <div>
                        <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
                            Search Documents
                        </label>
                        <input
                            type="text"
                            id="search"
                            className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                            placeholder="Search by title or description"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                    </div>
                    <div>
                        <label htmlFor="category-filter" className="block text-sm font-medium text-gray-700 mb-1">
                            Category
                        </label>
                        <select
                            id="category-filter"
                            className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                            value={selectedCategory}
                            onChange={(e) => setSelectedCategory(e.target.value)}
                        >
                            {categories.map(category => (
                                <option key={category.id} value={category.id}>{category.name}</option>
                            ))}
                        </select>
                    </div>
                    <div>
                        <label htmlFor="type-filter" className="block text-sm font-medium text-gray-700 mb-1">
                            Document Type
                        </label>
                        <select
                            id="type-filter"
                            className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                            value={selectedType}
                            onChange={(e) => setSelectedType(e.target.value)}
                        >
                            {documentTypes.map(type => (
                                <option key={type.id} value={type.id}>{type.name}</option>
                            ))}
                        </select>
                    </div>
                    <div>
                        <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700 mb-1">
                            Status
                        </label>
                        <select
                            id="status-filter"
                            className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                            value={selectedStatus}
                            onChange={(e) => setSelectedStatus(e.target.value)}
                        >
                            {statuses.map(status => (
                                <option key={status.id} value={status.id}>{status.name}</option>
                            ))}
                        </select>
                    </div>
                    <div>
                        <label htmlFor="year-filter" className="block text-sm font-medium text-gray-700 mb-1">
                            Tax Year
                        </label>
                        <select
                            id="year-filter"
                            className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                            value={selectedYear}
                            onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                        >
                            {[...Array(5)].map((_, i) => {
                                const year = new Date().getFullYear() - i;
                                return (
                                    <option key={year} value={year}>
                                        {year}
                                    </option>
                                );
                            })}
                        </select>
                    </div>
                </div>

                {/* Document List */}
                <div className="mt-6">
                    <div className="flex justify-between items-center mb-4">
                        <h4 className="text-lg font-medium text-gray-900">
                            {filteredDocuments.length} {filteredDocuments.length === 1 ? 'Document' : 'Documents'} Found
                        </h4>
                        <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-500">Sort by:</span>
                            <select
                                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block sm:text-sm border-gray-300 rounded-md"
                                value={`${sortBy}-${sortDirection}`}
                                onChange={(e) => {
                                    const [field, direction] = e.target.value.split('-');
                                    setSortBy(field);
                                    setSortDirection(direction as 'asc' | 'desc');
                                }}
                            >
                                <option value="createdAt-desc">Date (Newest)</option>
                                <option value="createdAt-asc">Date (Oldest)</option>
                                <option value="title-asc">Title (A-Z)</option>
                                <option value="title-desc">Title (Z-A)</option>
                                <option value="documentType-asc">Type (A-Z)</option>
                                <option value="status-asc">Status</option>
                            </select>
                        </div>
                    </div>

                    {filteredDocuments.length === 0 ? (
                        <div className="text-center py-12 bg-gray-50 rounded-lg">
                            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24"
                                 stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1}
                                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                            <h3 className="mt-2 text-sm font-medium text-gray-900">No documents found</h3>
                            <p className="mt-1 text-sm text-gray-500">
                                Try adjusting your search or filter criteria.
                            </p>
                            <div className="mt-6">
                                <button
                                    type="button"
                                    onClick={() => setShowUploadModal(true)}
                                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                                >
                                    Upload New Document
                                </button>
                            </div>
                        </div>
                    ) : (
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                <tr>
                                    <th scope="col"
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Document
                                    </th>
                                    <th scope="col"
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Type
                                    </th>
                                    <th scope="col"
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Category
                                    </th>
                                    <th scope="col"
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Year
                                    </th>
                                    <th scope="col"
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th scope="col"
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                {filteredDocuments.map((document) => (
                                    <tr key={document.id}>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                                <div className="flex-shrink-0 text-2xl">
                                                    {getFileIcon(document.fileType)}
                                                </div>
                                                <div className="ml-4">
                                                    <div
                                                        className="text-sm font-medium text-gray-900">{document.title}</div>
                                                    <div className="text-sm text-gray-500">{document.description}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {document.documentType}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {document.category}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {document.taxYear}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                        <span
                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(document.status)}`}>
                          {document.status.charAt(0).toUpperCase() + document.status.slice(1)}
                        </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button
                                                type="button"
                                                onClick={() => handlePreview(document)}
                                                className="text-primary-600 hover:text-primary-900 mr-3"
                                            >
                                                Preview
                                            </button>
                                            {document.fileName && (
                                                <button
                                                    type="button"
                                                    onClick={() => handleDownload(document)}
                                                    className="text-primary-600 hover:text-primary-900 mr-3"
                                                >
                                                    Download
                                                </button>
                                            )}
                                            {onDocumentSelect && (
                                                <button
                                                    type="button"
                                                    onClick={() => onDocumentSelect(document)}
                                                    className="text-primary-600 hover:text-primary-900"
                                                >
                                                    Select
                                                </button>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                                </tbody>
                            </table>
                        </div>
                    )}
                </div>
            </div>

            {/* Document Preview Modal */}
            {previewDocument && (
                <div className="fixed inset-0 overflow-y-auto z-50">
                    <div
                        className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
                            <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
                        </div>

                        <span className="hidden sm:inline-block sm:align-middle sm:h-screen"
                              aria-hidden="true">&#8203;</span>

                        <div
                            className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                                <div className="sm:flex sm:items-start">
                                    <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                        <h3 className="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                            {previewDocument.title}
                                        </h3>
                                        <div className="mt-2">
                                            <p className="text-sm text-gray-500">
                                                {previewDocument.description}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                                <button
                                    type="button"
                                    onClick={handleClosePreview}
                                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                                >
                                    Close
                                </button>
                                {previewDocument.fileName && (
                                    <button
                                        type="button"
                                        onClick={() => handleDownload(previewDocument)}
                                        className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm"
                                    >
                                        Download
                                    </button>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default TaxDocumentCenter;
