import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useFarm } from '../../context/FarmContext';
import { format, parseISO, addDays, isAfter, isBefore, isEqual } from 'date-fns';

interface Field {
  id: string;
  name: string;
  size: number;
  size_unit: string;
}

interface Crop {
  id: string;
  name: string;
  variety: string;
}

interface HarvestScheduleType {
  id: string;
  farm_id: string;
  field_id: string;
  crop_id: string;
  scheduled_date: string;
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled';
  weather_dependent: boolean;
  optimal_conditions: {
    min_temperature: number;
    max_temperature: number;
    max_precipitation_chance: number;
    max_wind_speed: number;
    preferred_conditions: string[];
  } | null;
  actual_start_date: string | null;
  actual_end_date: string | null;
  yield_amount: number | null;
  yield_unit: string | null;
  notes: string | null;
  Field?: Field;
  Crop?: Crop;
}

interface HarvestRecommendation {
  scheduled_date: string;
  optimal_days: string[];
  warning_days: string[];
  reason: string;
  crop_name: string;
  crop_variety: string;
  field_name: string;
}

interface HarvestScheduleProps {
  farmId: string;
}

const HarvestSchedule: React.FC<HarvestScheduleProps> = ({ farmId }) => {
  const { selectedFarm } = useFarm();
  const [schedules, setSchedules] = useState<HarvestScheduleType[]>([]);
  const [fields, setFields] = useState<Field[]>([]);
  const [crops, setCrops] = useState<Crop[]>([]);
  const [recommendations, setRecommendations] = useState<Record<string, HarvestRecommendation>>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState<boolean>(false);
  const [formData, setFormData] = useState({
    fieldId: '',
    cropId: '',
    scheduledDate: format(new Date(), 'yyyy-MM-dd'),
    weatherDependent: true,
    notes: ''
  });

  // Fetch harvest schedules when farm changes
  useEffect(() => {
    const fetchData = async () => {
      if (!farmId) return;

      setLoading(true);
      setError(null);

      try {
        // Fetch fields
        const fieldsResponse = await axios.get(`/api/fields/farm/${farmId}`);
        setFields(fieldsResponse.data);

        // Fetch crops
        const cropsResponse = await axios.get(`/api/crops/farm/${farmId}`);
        setCrops(cropsResponse.data.crops);

        // Fetch harvest schedules
        const schedulesResponse = await axios.get(`/api/harvest-schedules/farm/${farmId}`);
        setSchedules(schedulesResponse.data.harvestSchedules || []);

        // Fetch recommendations for each schedule
        const recommendationsData: Record<string, HarvestRecommendation> = {};
        for (const schedule of schedulesResponse.data.harvestSchedules || []) {
          if (schedule.weather_dependent) {
            try {
              const recResponse = await axios.get(`/api/harvest-schedules/${schedule.id}/recommendations`);
              recommendationsData[schedule.id] = recResponse.data.recommendations;
            } catch (recError) {
              console.error(`Error fetching recommendations for schedule ${schedule.id}:`, recError);
            }
          }
        }
        setRecommendations(recommendationsData);
      } catch (err) {
        console.error('Error fetching harvest data:', err);
        setError('Failed to load harvest data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [farmId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      
      const response = await axios.post('/api/harvest-schedules', {
        farmId,
        fieldId: formData.fieldId,
        cropId: formData.cropId,
        scheduledDate: formData.scheduledDate,
        weatherDependent: formData.weatherDependent,
        notes: formData.notes
      });
      
      // Add the new schedule to the list
      setSchedules(prev => [...prev, response.data.harvestSchedule]);
      
      // If recommendations were returned, add them
      if (response.data.recommendations) {
        setRecommendations(prev => ({
          ...prev,
          [response.data.harvestSchedule.id]: response.data.recommendations
        }));
      }
      
      // Reset form
      setFormData({
        fieldId: '',
        cropId: '',
        scheduledDate: format(new Date(), 'yyyy-MM-dd'),
        weatherDependent: true,
        notes: ''
      });
      
      // Hide form
      setShowForm(false);
    } catch (err) {
      console.error('Error creating harvest schedule:', err);
      setError('Failed to create harvest schedule. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (scheduleId: string, newStatus: 'planned' | 'in_progress' | 'completed' | 'cancelled') => {
    try {
      setLoading(true);
      
      const response = await axios.put(`/api/harvest-schedules/${scheduleId}`, {
        status: newStatus,
        actualStartDate: newStatus === 'in_progress' ? format(new Date(), 'yyyy-MM-dd') : undefined,
        actualEndDate: newStatus === 'completed' ? format(new Date(), 'yyyy-MM-dd') : undefined
      });
      
      // Update the schedule in the list
      setSchedules(prev => prev.map(schedule => 
        schedule.id === scheduleId ? response.data.harvestSchedule : schedule
      ));
    } catch (err) {
      console.error('Error updating harvest schedule status:', err);
      setError('Failed to update harvest schedule status. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (scheduleId: string) => {
    if (!window.confirm('Are you sure you want to delete this harvest schedule?')) {
      return;
    }
    
    try {
      setLoading(true);
      
      await axios.delete(`/api/harvest-schedules/${scheduleId}`);
      
      // Remove the schedule from the list
      setSchedules(prev => prev.filter(schedule => schedule.id !== scheduleId));
      
      // Remove recommendations if they exist
      if (recommendations[scheduleId]) {
        const newRecommendations = { ...recommendations };
        delete newRecommendations[scheduleId];
        setRecommendations(newRecommendations);
      }
    } catch (err) {
      console.error('Error deleting harvest schedule:', err);
      setError('Failed to delete harvest schedule. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'planned':
        return 'bg-blue-100 text-blue-800';
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    return format(parseISO(dateString), 'MMM d, yyyy');
  };

  const isDateInRange = (date: string, startDate: string, endDate: string) => {
    const dateObj = parseISO(date);
    const startObj = parseISO(startDate);
    const endObj = parseISO(endDate);
    
    return (isAfter(dateObj, startObj) || isEqual(dateObj, startObj)) && 
           (isBefore(dateObj, endObj) || isEqual(dateObj, endObj));
  };

  const getNextWeekDates = () => {
    const today = new Date();
    const dates = [];
    
    for (let i = 0; i < 10; i++) {
      const date = addDays(today, i);
      dates.push(format(date, 'yyyy-MM-dd'));
    }
    
    return dates;
  };

  const nextWeekDates = getNextWeekDates();

  if (loading && !schedules.length) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error && !schedules.length) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <span className="block sm:inline">{error}</span>
      </div>
    );
  }

  return (
    <div className="bg-white shadow overflow-hidden sm:rounded-lg">
      <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
        <div>
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Harvest Schedules
          </h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Plan and track your harvests with weather-based recommendations
          </p>
        </div>
        <button
          onClick={() => setShowForm(!showForm)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          {showForm ? 'Cancel' : 'Add Harvest Schedule'}
        </button>
      </div>

      {showForm && (
        <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
              <div className="sm:col-span-3">
                <label htmlFor="fieldId" className="block text-sm font-medium text-gray-700">
                  Field
                </label>
                <select
                  id="fieldId"
                  name="fieldId"
                  value={formData.fieldId}
                  onChange={handleInputChange}
                  required
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                >
                  <option value="">Select a field</option>
                  {fields.map(field => (
                    <option key={field.id} value={field.id}>
                      {field.name} ({field.size} {field.size_unit})
                    </option>
                  ))}
                </select>
              </div>

              <div className="sm:col-span-3">
                <label htmlFor="cropId" className="block text-sm font-medium text-gray-700">
                  Crop
                </label>
                <select
                  id="cropId"
                  name="cropId"
                  value={formData.cropId}
                  onChange={handleInputChange}
                  required
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                >
                  <option value="">Select a crop</option>
                  {crops.map(crop => (
                    <option key={crop.id} value={crop.id}>
                      {crop.name} {crop.variety ? `(${crop.variety})` : ''}
                    </option>
                  ))}
                </select>
              </div>

              <div className="sm:col-span-3">
                <label htmlFor="scheduledDate" className="block text-sm font-medium text-gray-700">
                  Scheduled Date
                </label>
                <input
                  type="date"
                  id="scheduledDate"
                  name="scheduledDate"
                  value={formData.scheduledDate}
                  onChange={handleInputChange}
                  required
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              </div>

              <div className="sm:col-span-3">
                <div className="flex items-center h-full mt-6">
                  <input
                    id="weatherDependent"
                    name="weatherDependent"
                    type="checkbox"
                    checked={formData.weatherDependent}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label htmlFor="weatherDependent" className="ml-2 block text-sm text-gray-700">
                    Weather dependent harvest
                  </label>
                </div>
              </div>

              <div className="sm:col-span-6">
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                  Notes
                </label>
                <textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  rows={3}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              </div>
            </div>

            <div className="mt-6 flex justify-end">
              <button
                type="button"
                onClick={() => setShowForm(false)}
                className="mr-3 bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Save
              </button>
            </div>
          </form>
        </div>
      )}

      {schedules.length === 0 ? (
        <div className="px-4 py-5 sm:p-6 text-center">
          <p className="text-gray-500">No harvest schedules found. Create one to get started.</p>
        </div>
      ) : (
        <div className="border-t border-gray-200">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Field & Crop
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Scheduled Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Weather Recommendations
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {schedules.map(schedule => (
                  <tr key={schedule.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {schedule.Field?.name || 'Unknown Field'}
                      </div>
                      <div className="text-sm text-gray-500">
                        {schedule.Crop?.name || 'Unknown Crop'} {schedule.Crop?.variety ? `(${schedule.Crop.variety})` : ''}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {formatDate(schedule.scheduled_date)}
                      </div>
                      {schedule.actual_start_date && (
                        <div className="text-xs text-gray-500">
                          Started: {formatDate(schedule.actual_start_date)}
                        </div>
                      )}
                      {schedule.actual_end_date && (
                        <div className="text-xs text-gray-500">
                          Completed: {formatDate(schedule.actual_end_date)}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(schedule.status)}`}>
                        {schedule.status.replace('_', ' ')}
                      </span>
                      {schedule.weather_dependent && (
                        <div className="text-xs text-gray-500 mt-1">
                          Weather dependent
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      {schedule.weather_dependent && recommendations[schedule.id] ? (
                        <div>
                          <div className="text-sm text-gray-900 mb-2">
                            {recommendations[schedule.id].reason}
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {nextWeekDates.map(date => {
                              const isOptimal = recommendations[schedule.id].optimal_days.includes(date);
                              const isWarning = recommendations[schedule.id].warning_days.includes(date);
                              const isScheduled = date === schedule.scheduled_date;
                              
                              let bgColor = 'bg-gray-100';
                              if (isScheduled) bgColor = 'bg-blue-100 border-2 border-blue-500';
                              else if (isOptimal) bgColor = 'bg-green-100';
                              else if (isWarning) bgColor = 'bg-red-100';
                              
                              return (
                                <div 
                                  key={date} 
                                  className={`${bgColor} px-2 py-1 text-xs rounded flex flex-col items-center`}
                                  title={isOptimal ? 'Optimal day' : isWarning ? 'Not recommended' : 'Neutral day'}
                                >
                                  <span>{format(parseISO(date), 'EEE')}</span>
                                  <span>{format(parseISO(date), 'd')}</span>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      ) : schedule.weather_dependent ? (
                        <span className="text-sm text-gray-500">Loading recommendations...</span>
                      ) : (
                        <span className="text-sm text-gray-500">Not weather dependent</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex flex-col space-y-2">
                        {schedule.status === 'planned' && (
                          <button
                            onClick={() => handleStatusChange(schedule.id, 'in_progress')}
                            className="text-primary-600 hover:text-primary-900"
                          >
                            Start Harvest
                          </button>
                        )}
                        {schedule.status === 'in_progress' && (
                          <button
                            onClick={() => handleStatusChange(schedule.id, 'completed')}
                            className="text-green-600 hover:text-green-900"
                          >
                            Complete
                          </button>
                        )}
                        {(schedule.status === 'planned' || schedule.status === 'in_progress') && (
                          <button
                            onClick={() => handleStatusChange(schedule.id, 'cancelled')}
                            className="text-red-600 hover:text-red-900"
                          >
                            Cancel
                          </button>
                        )}
                        <button
                          onClick={() => handleDelete(schedule.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default HarvestSchedule;