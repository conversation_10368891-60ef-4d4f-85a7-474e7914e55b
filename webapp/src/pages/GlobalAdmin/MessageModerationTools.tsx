import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../../context/AuthContext';
import { format } from 'date-fns';

// Types for message moderation
interface FlaggedMessage {
  id: string;
  conversation_id: string;
  sender_id: string;
  message_type: string;
  content: string;
  created_at: string;
  updated_at: string;
  sender_first_name: string;
  sender_last_name: string;
  sender_email: string;
  flag_reason: string;
  flagged_by: string;
  flagged_at: string;
  status: 'flagged' | 'hidden' | 'deleted' | 'approved';
}

interface ModerationAction {
  id: string;
  message_id: string;
  action_type: 'flag' | 'hide' | 'delete' | 'approve';
  performed_by: string;
  performed_at: string;
  reason: string;
}

const MessageModerationTools: React.FC = () => {
  const { getToken } = useAuth();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [flaggedMessages, setFlaggedMessages] = useState<FlaggedMessage[]>([]);
  const [moderationHistory, setModerationHistory] = useState<ModerationAction[]>([]);
  const [selectedMessage, setSelectedMessage] = useState<FlaggedMessage | null>(null);
  const [actionReason, setActionReason] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dateRange, setDateRange] = useState<{ start: string; end: string }>({
    start: format(new Date(new Date().setDate(new Date().getDate() - 30)), 'yyyy-MM-dd'),
    end: format(new Date(), 'yyyy-MM-dd'),
  });

  // Load flagged messages
  const loadFlaggedMessages = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const token = await getToken();
      const headers = { Authorization: `Bearer ${token}` };

      // Fetch flagged messages
      const response = await axios.get('/api/admin/chat/moderation/messages', {
        headers,
        params: {
          start_date: dateRange.start,
          end_date: dateRange.end,
          status: statusFilter !== 'all' ? statusFilter : undefined,
          search: searchTerm || undefined,
        },
      });
      setFlaggedMessages(response.data);
    } catch (error) {
      console.error('Error loading flagged messages:', error);
      setError('Failed to load flagged messages. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  // Load moderation history
  const loadModerationHistory = async () => {
    try {
      const token = await getToken();
      const headers = { Authorization: `Bearer ${token}` };

      // Fetch moderation history
      const response = await axios.get('/api/admin/chat/moderation/history', {
        headers,
        params: {
          start_date: dateRange.start,
          end_date: dateRange.end,
        },
      });
      setModerationHistory(response.data);
    } catch (error) {
      console.error('Error loading moderation history:', error);
      // Don't set error state here to avoid disrupting the main functionality
    }
  };

  // Load data on mount and when filters change
  useEffect(() => {
    loadFlaggedMessages();
    loadModerationHistory();
  }, [dateRange, statusFilter, searchTerm]);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Handle status filter change
  const handleStatusFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setStatusFilter(e.target.value);
  };

  // Handle date range change
  const handleDateRangeChange = (e: React.ChangeEvent<HTMLInputElement>, field: 'start' | 'end') => {
    setDateRange(prev => ({
      ...prev,
      [field]: e.target.value,
    }));
  };

  // Handle message selection
  const handleSelectMessage = (message: FlaggedMessage) => {
    setSelectedMessage(message);
    setActionReason('');
  };

  // Handle action reason change
  const handleActionReasonChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setActionReason(e.target.value);
  };

  // Perform moderation action
  const performAction = async (action: 'approve' | 'hide' | 'delete') => {
    if (!selectedMessage) return;

    try {
      setIsLoading(true);
      const token = await getToken();
      const headers = { Authorization: `Bearer ${token}` };

      // Perform moderation action
      await axios.post(
        `/api/admin/chat/moderation/messages/${selectedMessage.id}/${action}`,
        { reason: actionReason },
        { headers }
      );

      // Refresh data
      await loadFlaggedMessages();
      await loadModerationHistory();

      // Clear selection
      setSelectedMessage(null);
      setActionReason('');
    } catch (error) {
      console.error(`Error performing ${action} action:`, error);
      setError(`Failed to ${action} message. Please try again later.`);
    } finally {
      setIsLoading(false);
    }
  };

  // Render loading state
  if (isLoading && flaggedMessages.length === 0) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-6">Message Moderation Tools</h1>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
          <p className="ml-3 text-gray-500">Loading flagged messages...</p>
        </div>
      </div>
    );
  }

  // Render error state
  if (error && flaggedMessages.length === 0) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-6">Message Moderation Tools</h1>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Message Moderation Tools</h1>

      {/* Filters */}
      <div className="mb-6 bg-white p-4 rounded-lg shadow">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div className="mb-4 md:mb-0">
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <input
              type="text"
              id="search"
              value={searchTerm}
              onChange={handleSearchChange}
              placeholder="Search messages..."
              className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
            />
          </div>
          <div className="mb-4 md:mb-0 md:ml-4">
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              id="status"
              value={statusFilter}
              onChange={handleStatusFilterChange}
              className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
            >
              <option value="all">All</option>
              <option value="flagged">Flagged</option>
              <option value="hidden">Hidden</option>
              <option value="deleted">Deleted</option>
              <option value="approved">Approved</option>
            </select>
          </div>
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
            <div>
              <label htmlFor="start-date" className="block text-sm font-medium text-gray-700 mb-1">
                Start Date
              </label>
              <input
                type="date"
                id="start-date"
                value={dateRange.start}
                onChange={(e) => handleDateRangeChange(e, 'start')}
                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
            <div>
              <label htmlFor="end-date" className="block text-sm font-medium text-gray-700 mb-1">
                End Date
              </label>
              <input
                type="date"
                id="end-date"
                value={dateRange.end}
                onChange={(e) => handleDateRangeChange(e, 'end')}
                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
            <div className="flex items-end">
              <button
                onClick={loadFlaggedMessages}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Apply Filters
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Flagged messages list */}
        <div className="lg:col-span-2">
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
              <h3 className="text-lg leading-6 font-medium text-gray-900">Flagged Messages</h3>
            </div>
            <ul className="divide-y divide-gray-200">
              {flaggedMessages.length > 0 ? (
                flaggedMessages.map((message) => (
                  <li
                    key={message.id}
                    className={`hover:bg-gray-50 cursor-pointer ${
                      selectedMessage?.id === message.id ? 'bg-indigo-50' : ''
                    }`}
                    onClick={() => handleSelectMessage(message)}
                  >
                    <div className="px-4 py-4 sm:px-6">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-indigo-600 truncate">
                          {message.sender_first_name} {message.sender_last_name}
                        </p>
                        <div className="ml-2 flex-shrink-0 flex">
                          <p
                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              message.status === 'flagged'
                                ? 'bg-yellow-100 text-yellow-800'
                                : message.status === 'hidden'
                                ? 'bg-gray-100 text-gray-800'
                                : message.status === 'deleted'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-green-100 text-green-800'
                            }`}
                          >
                            {message.status.charAt(0).toUpperCase() + message.status.slice(1)}
                          </p>
                        </div>
                      </div>
                      <div className="mt-2 sm:flex sm:justify-between">
                        <div className="sm:flex">
                          <p className="flex items-center text-sm text-gray-500">
                            {message.content.length > 100
                              ? `${message.content.substring(0, 100)}...`
                              : message.content}
                          </p>
                        </div>
                        <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                          <p>
                            {new Date(message.created_at).toLocaleString()}
                          </p>
                        </div>
                      </div>
                      <div className="mt-2 text-sm text-gray-500">
                        <span className="font-medium">Flag reason: </span>
                        {message.flag_reason}
                      </div>
                    </div>
                  </li>
                ))
              ) : (
                <li className="px-4 py-4 sm:px-6 text-center text-gray-500">No flagged messages found</li>
              )}
            </ul>
          </div>
        </div>

        {/* Message details and actions */}
        <div className="lg:col-span-1">
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
              <h3 className="text-lg leading-6 font-medium text-gray-900">Message Details</h3>
            </div>
            {selectedMessage ? (
              <div className="px-4 py-5 sm:p-6">
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-500">Sender</h4>
                  <p className="mt-1 text-sm text-gray-900">
                    {selectedMessage.sender_first_name} {selectedMessage.sender_last_name} ({selectedMessage.sender_email})
                  </p>
                </div>
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-500">Message</h4>
                  <p className="mt-1 text-sm text-gray-900">{selectedMessage.content}</p>
                </div>
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-500">Sent At</h4>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(selectedMessage.created_at).toLocaleString()}
                  </p>
                </div>
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-500">Flag Reason</h4>
                  <p className="mt-1 text-sm text-gray-900">{selectedMessage.flag_reason}</p>
                </div>
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-500">Status</h4>
                  <p
                    className={`mt-1 inline-flex px-2 text-xs leading-5 font-semibold rounded-full ${
                      selectedMessage.status === 'flagged'
                        ? 'bg-yellow-100 text-yellow-800'
                        : selectedMessage.status === 'hidden'
                        ? 'bg-gray-100 text-gray-800'
                        : selectedMessage.status === 'deleted'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-green-100 text-green-800'
                    }`}
                  >
                    {selectedMessage.status.charAt(0).toUpperCase() + selectedMessage.status.slice(1)}
                  </p>
                </div>
                <div className="mb-4">
                  <label htmlFor="action-reason" className="block text-sm font-medium text-gray-700">
                    Action Reason
                  </label>
                  <textarea
                    id="action-reason"
                    rows={3}
                    value={actionReason}
                    onChange={handleActionReasonChange}
                    className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md mt-1"
                    placeholder="Enter reason for this action..."
                  ></textarea>
                </div>
                <div className="flex flex-col space-y-2">
                  <button
                    onClick={() => performAction('approve')}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                    disabled={isLoading || selectedMessage.status === 'approved'}
                  >
                    Approve Message
                  </button>
                  <button
                    onClick={() => performAction('hide')}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                    disabled={isLoading || selectedMessage.status === 'hidden'}
                  >
                    Hide Message
                  </button>
                  <button
                    onClick={() => performAction('delete')}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    disabled={isLoading || selectedMessage.status === 'deleted'}
                  >
                    Delete Message
                  </button>
                </div>
              </div>
            ) : (
              <div className="px-4 py-5 sm:p-6 text-center text-gray-500">
                Select a message to view details and perform actions
              </div>
            )}
          </div>

          {/* Moderation History */}
          <div className="mt-6 bg-white shadow overflow-hidden sm:rounded-md">
            <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
              <h3 className="text-lg leading-6 font-medium text-gray-900">Recent Moderation Actions</h3>
            </div>
            <ul className="divide-y divide-gray-200 max-h-64 overflow-y-auto">
              {moderationHistory.length > 0 ? (
                moderationHistory.map((action) => (
                  <li key={action.id} className="px-4 py-3 sm:px-6">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900">
                        {action.action_type.charAt(0).toUpperCase() + action.action_type.slice(1)}
                      </p>
                      <p className="text-sm text-gray-500">
                        {new Date(action.performed_at).toLocaleString()}
                      </p>
                    </div>
                    <p className="text-sm text-gray-500 mt-1">
                      Reason: {action.reason || 'No reason provided'}
                    </p>
                  </li>
                ))
              ) : (
                <li className="px-4 py-4 sm:px-6 text-center text-gray-500">No moderation actions found</li>
              )}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MessageModerationTools;