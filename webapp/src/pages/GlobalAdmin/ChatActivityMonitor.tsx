import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../../context/AuthContext';
import { format } from 'date-fns';

// Types for chat activity data
interface ChatMessage {
  id: string;
  conversation_id: string;
  sender_id: string;
  message_type: string;
  content: string;
  created_at: string;
  sender_first_name: string;
  sender_last_name: string;
  sender_email: string;
}

interface ChatConversation {
  id: string;
  name: string;
  type: 'direct' | 'group' | 'channel';
  created_at: string;
  updated_at: string;
  message_count: number;
  participant_count: number;
  last_message_at: string;
}

interface ChatUser {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  message_count: number;
  last_active_at: string;
}

interface ChatStats {
  total_messages: number;
  total_conversations: number;
  active_users: number;
  messages_today: number;
  messages_this_week: number;
  messages_this_month: number;
}

const ChatActivityMonitor: React.FC = () => {
  const { getToken } = useAuth();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<ChatStats | null>(null);
  const [recentMessages, setRecentMessages] = useState<ChatMessage[]>([]);
  const [activeConversations, setActiveConversations] = useState<ChatConversation[]>([]);
  const [activeUsers, setActiveUsers] = useState<ChatUser[]>([]);
  const [selectedTab, setSelectedTab] = useState<'overview' | 'messages' | 'conversations' | 'users'>('overview');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [dateRange, setDateRange] = useState<{ start: string; end: string }>({
    start: format(new Date(new Date().setDate(new Date().getDate() - 30)), 'yyyy-MM-dd'),
    end: format(new Date(), 'yyyy-MM-dd'),
  });

  // Load chat activity data
  const loadChatActivity = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const token = await getToken();
      const headers = { Authorization: `Bearer ${token}` };

      // Fetch chat statistics
      const statsResponse = await axios.get('/api/admin/chat/stats', {
        headers,
        params: {
          start_date: dateRange.start,
          end_date: dateRange.end,
        },
      });
      setStats(statsResponse.data);

      // Fetch recent messages
      const messagesResponse = await axios.get('/api/admin/chat/messages', {
        headers,
        params: {
          start_date: dateRange.start,
          end_date: dateRange.end,
          limit: 50,
          search: searchTerm || undefined,
        },
      });
      setRecentMessages(messagesResponse.data);

      // Fetch active conversations
      const conversationsResponse = await axios.get('/api/admin/chat/conversations', {
        headers,
        params: {
          start_date: dateRange.start,
          end_date: dateRange.end,
          limit: 50,
          search: searchTerm || undefined,
        },
      });
      setActiveConversations(conversationsResponse.data);

      // Fetch active users
      const usersResponse = await axios.get('/api/admin/chat/users', {
        headers,
        params: {
          start_date: dateRange.start,
          end_date: dateRange.end,
          limit: 50,
          search: searchTerm || undefined,
        },
      });
      setActiveUsers(usersResponse.data);
    } catch (error) {
      console.error('Error loading chat activity:', error);
      setError('Failed to load chat activity data. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  // Load data on mount and when filters change
  useEffect(() => {
    loadChatActivity();
  }, [dateRange, searchTerm]);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Handle date range change
  const handleDateRangeChange = (e: React.ChangeEvent<HTMLInputElement>, field: 'start' | 'end') => {
    setDateRange(prev => ({
      ...prev,
      [field]: e.target.value,
    }));
  };

  // Render loading state
  if (isLoading && !stats) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-6">Chat Activity Monitor</h1>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
          <p className="ml-3 text-gray-500">Loading chat activity data...</p>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-6">Chat Activity Monitor</h1>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Chat Activity Monitor</h1>

      {/* Filters */}
      <div className="mb-6 bg-white p-4 rounded-lg shadow">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div className="mb-4 md:mb-0">
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <input
              type="text"
              id="search"
              value={searchTerm}
              onChange={handleSearchChange}
              placeholder="Search messages, conversations, or users..."
              className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
            />
          </div>
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
            <div>
              <label htmlFor="start-date" className="block text-sm font-medium text-gray-700 mb-1">
                Start Date
              </label>
              <input
                type="date"
                id="start-date"
                value={dateRange.start}
                onChange={(e) => handleDateRangeChange(e, 'start')}
                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
            <div>
              <label htmlFor="end-date" className="block text-sm font-medium text-gray-700 mb-1">
                End Date
              </label>
              <input
                type="date"
                id="end-date"
                value={dateRange.end}
                onChange={(e) => handleDateRangeChange(e, 'end')}
                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
            <div className="flex items-end">
              <button
                onClick={loadChatActivity}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Apply Filters
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="mb-6">
        <nav className="flex space-x-4" aria-label="Tabs">
          <button
            onClick={() => setSelectedTab('overview')}
            className={`px-3 py-2 font-medium text-sm rounded-md ${
              selectedTab === 'overview'
                ? 'bg-indigo-100 text-indigo-700'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setSelectedTab('messages')}
            className={`px-3 py-2 font-medium text-sm rounded-md ${
              selectedTab === 'messages'
                ? 'bg-indigo-100 text-indigo-700'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Messages
          </button>
          <button
            onClick={() => setSelectedTab('conversations')}
            className={`px-3 py-2 font-medium text-sm rounded-md ${
              selectedTab === 'conversations'
                ? 'bg-indigo-100 text-indigo-700'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Conversations
          </button>
          <button
            onClick={() => setSelectedTab('users')}
            className={`px-3 py-2 font-medium text-sm rounded-md ${
              selectedTab === 'users'
                ? 'bg-indigo-100 text-indigo-700'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Users
          </button>
        </nav>
      </div>

      {/* Content based on selected tab */}
      {selectedTab === 'overview' && stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <dt className="text-sm font-medium text-gray-500 truncate">Total Messages</dt>
              <dd className="mt-1 text-3xl font-semibold text-gray-900">{stats.total_messages}</dd>
            </div>
          </div>
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <dt className="text-sm font-medium text-gray-500 truncate">Total Conversations</dt>
              <dd className="mt-1 text-3xl font-semibold text-gray-900">{stats.total_conversations}</dd>
            </div>
          </div>
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <dt className="text-sm font-medium text-gray-500 truncate">Active Users</dt>
              <dd className="mt-1 text-3xl font-semibold text-gray-900">{stats.active_users}</dd>
            </div>
          </div>
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <dt className="text-sm font-medium text-gray-500 truncate">Messages Today</dt>
              <dd className="mt-1 text-3xl font-semibold text-gray-900">{stats.messages_today}</dd>
            </div>
          </div>
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <dt className="text-sm font-medium text-gray-500 truncate">Messages This Week</dt>
              <dd className="mt-1 text-3xl font-semibold text-gray-900">{stats.messages_this_week}</dd>
            </div>
          </div>
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <dt className="text-sm font-medium text-gray-500 truncate">Messages This Month</dt>
              <dd className="mt-1 text-3xl font-semibold text-gray-900">{stats.messages_this_month}</dd>
            </div>
          </div>
        </div>
      )}

      {selectedTab === 'messages' && (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {recentMessages.length > 0 ? (
              recentMessages.map((message) => (
                <li key={message.id}>
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-indigo-600 truncate">
                        {message.sender_first_name} {message.sender_last_name}
                      </p>
                      <div className="ml-2 flex-shrink-0 flex">
                        <p className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          {message.message_type}
                        </p>
                      </div>
                    </div>
                    <div className="mt-2 sm:flex sm:justify-between">
                      <div className="sm:flex">
                        <p className="flex items-center text-sm text-gray-500">
                          {message.content.length > 100
                            ? `${message.content.substring(0, 100)}...`
                            : message.content}
                        </p>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <p>
                          {new Date(message.created_at).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>
                </li>
              ))
            ) : (
              <li className="px-4 py-4 sm:px-6 text-center text-gray-500">No messages found</li>
            )}
          </ul>
        </div>
      )}

      {selectedTab === 'conversations' && (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {activeConversations.length > 0 ? (
              activeConversations.map((conversation) => (
                <li key={conversation.id}>
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-indigo-600 truncate">
                        {conversation.name || `Conversation ${conversation.id.substring(0, 8)}`}
                      </p>
                      <div className="ml-2 flex-shrink-0 flex">
                        <p className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          {conversation.type}
                        </p>
                      </div>
                    </div>
                    <div className="mt-2 sm:flex sm:justify-between">
                      <div className="sm:flex">
                        <p className="flex items-center text-sm text-gray-500">
                          {conversation.message_count} messages • {conversation.participant_count} participants
                        </p>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <p>
                          Last message: {new Date(conversation.last_message_at).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>
                </li>
              ))
            ) : (
              <li className="px-4 py-4 sm:px-6 text-center text-gray-500">No conversations found</li>
            )}
          </ul>
        </div>
      )}

      {selectedTab === 'users' && (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {activeUsers.length > 0 ? (
              activeUsers.map((user) => (
                <li key={user.id}>
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-indigo-600 truncate">
                        {user.first_name} {user.last_name}
                      </p>
                      <div className="ml-2 flex-shrink-0 flex">
                        <p className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          {user.message_count} messages
                        </p>
                      </div>
                    </div>
                    <div className="mt-2 sm:flex sm:justify-between">
                      <div className="sm:flex">
                        <p className="flex items-center text-sm text-gray-500">
                          {user.email}
                        </p>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <p>
                          Last active: {new Date(user.last_active_at).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>
                </li>
              ))
            ) : (
              <li className="px-4 py-4 sm:px-6 text-center text-gray-500">No active users found</li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
};

export default ChatActivityMonitor;