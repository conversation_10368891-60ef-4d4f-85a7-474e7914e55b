import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';

interface MatrixRoom {
  room_id: string;
  name: string;
  canonical_alias: string;
  joined_members: number;
  joined_local_members: number;
  state_events: number;
  version: string;
  creator: string;
  encryption: string;
  federatable: boolean;
  public: boolean;
}

interface RoomMember {
  user_id: string;
  display_name: string;
  avatar_url: string;
  membership: string;
}

const MatrixRoomManagement: React.FC = () => {
  const [rooms, setRooms] = useState<MatrixRoom[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [showMembersModal, setShowMembersModal] = useState<boolean>(false);
  const [showPurgeHistoryModal, setShowPurgeHistoryModal] = useState<boolean>(false);
  const [currentRoom, setCurrentRoom] = useState<MatrixRoom | null>(null);
  const [roomMembers, setRoomMembers] = useState<RoomMember[]>([]);
  const [loadingMembers, setLoadingMembers] = useState<boolean>(false);
  const [purgeFormData, setPurgeFormData] = useState({
    purge_up_to_ts: Math.floor(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago by default
    delete_local_events: true
  });
  const [pagination, setPagination] = useState({
    from: 0,
    limit: 20,
    total: 0
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  useEffect(() => {
    fetchRooms();
  }, [pagination.from, pagination.limit, sortBy, sortOrder]);

  const fetchRooms = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.get(
        `${API_URL}/admin/matrix/rooms?from=${pagination.from}&limit=${pagination.limit}&order_by=${sortBy}&dir=${sortOrder}`
      );
      
      if (response.data && response.data.rooms) {
        setRooms(response.data.rooms);
        setPagination(prev => ({
          ...prev,
          total: response.data.total || 0
        }));
      } else {
        setRooms([]);
      }

      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching Matrix rooms:', err);
      setError(err.response?.data?.error || 'Failed to load Matrix rooms');
      setLoading(false);
    }
  };

  const handleDeleteRoom = (room: MatrixRoom) => {
    setCurrentRoom(room);
    setShowDeleteModal(true);
  };

  const handleViewMembers = async (room: MatrixRoom) => {
    setCurrentRoom(room);
    setLoadingMembers(true);
    setRoomMembers([]);
    setShowMembersModal(true);
    
    try {
      const response = await axios.get(`${API_URL}/admin/matrix/rooms/${encodeURIComponent(room.room_id)}/members`);
      
      if (response.data && response.data.members) {
        setRoomMembers(response.data.members);
      }
    } catch (err: any) {
      console.error('Error fetching room members:', err);
      setError(err.response?.data?.error || 'Failed to load room members');
    } finally {
      setLoadingMembers(false);
    }
  };

  const handlePurgeHistory = (room: MatrixRoom) => {
    setCurrentRoom(room);
    setPurgeFormData({
      purge_up_to_ts: Math.floor(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago by default
      delete_local_events: true
    });
    setShowPurgeHistoryModal(true);
  };

  const confirmDeleteRoom = async () => {
    if (!currentRoom) return;

    try {
      setError(null);

      await axios.delete(`${API_URL}/admin/matrix/rooms/${encodeURIComponent(currentRoom.room_id)}`, {
        data: {
          message: 'This room has been deleted by an administrator'
        }
      });

      // Refresh the room list
      fetchRooms();
      setShowDeleteModal(false);
    } catch (err: any) {
      console.error('Error deleting Matrix room:', err);
      setError(err.response?.data?.error || 'Failed to delete Matrix room');
    }
  };

  const confirmPurgeHistory = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentRoom) return;

    try {
      setError(null);

      await axios.post(`${API_URL}/admin/matrix/rooms/${encodeURIComponent(currentRoom.room_id)}/purge-history`, {
        purge_up_to_ts: purgeFormData.purge_up_to_ts,
        delete_local_events: purgeFormData.delete_local_events
      });

      setShowPurgeHistoryModal(false);
    } catch (err: any) {
      console.error('Error purging room history:', err);
      setError(err.response?.data?.error || 'Failed to purge room history');
    }
  };

  const handlePurgeFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    
    if (type === 'checkbox') {
      setPurgeFormData(prev => ({ ...prev, [name]: checked }));
    } else if (type === 'datetime-local') {
      // Convert datetime-local value to timestamp
      const timestamp = new Date(value).getTime();
      setPurgeFormData(prev => ({ ...prev, [name]: Math.floor(timestamp / 1000) }));
    } else if (name === 'days_ago') {
      // Calculate timestamp based on days ago
      const daysAgo = parseInt(value, 10) || 0;
      const timestamp = Math.floor(Date.now() - daysAgo * 24 * 60 * 60 * 1000);
      setPurgeFormData(prev => ({ ...prev, purge_up_to_ts: Math.floor(timestamp / 1000) }));
    } else {
      setPurgeFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleNextPage = () => {
    if (pagination.from + pagination.limit < pagination.total) {
      setPagination(prev => ({
        ...prev,
        from: prev.from + prev.limit
      }));
    }
  };

  const handlePrevPage = () => {
    if (pagination.from > 0) {
      setPagination(prev => ({
        ...prev,
        from: Math.max(0, prev.from - prev.limit)
      }));
    }
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchRooms();
  };

  const filteredRooms = searchQuery
    ? rooms.filter(room => 
        room.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        room.room_id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (room.canonical_alias && room.canonical_alias.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    : rooms;

  if (loading && rooms.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Manage Matrix Rooms</h2>
        <div className="flex space-x-2">
          <form onSubmit={handleSearch} className="flex">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search rooms..."
              className="shadow appearance-none border rounded-l py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            />
            <button
              type="submit"
              className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded-r"
            >
              Search
            </button>
          </form>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> {error}</span>
        </div>
      )}

      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th 
                scope="col" 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('name')}
              >
                Room Name
                {sortBy === 'name' && (
                  <span className="ml-1">{sortOrder === 'asc' ? '↑' : '↓'}</span>
                )}
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Room ID
              </th>
              <th 
                scope="col" 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('joined_members')}
              >
                Members
                {sortBy === 'joined_members' && (
                  <span className="ml-1">{sortOrder === 'asc' ? '↑' : '↓'}</span>
                )}
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredRooms.map(room => (
              <tr key={room.room_id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{room.name || 'Unnamed Room'}</div>
                  {room.canonical_alias && (
                    <div className="text-xs text-gray-500">{room.canonical_alias}</div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-500 truncate max-w-xs">{room.room_id}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {room.joined_members}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    room.public ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
                  }`}>
                    {room.public ? 'Public' : 'Private'}
                  </span>
                  {room.encryption && (
                    <span className="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
                      Encrypted
                    </span>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button 
                    onClick={() => handleViewMembers(room)}
                    className="text-blue-600 hover:text-blue-900 mr-3"
                  >
                    Members
                  </button>
                  <button 
                    onClick={() => handlePurgeHistory(room)}
                    className="text-yellow-600 hover:text-yellow-900 mr-3"
                  >
                    Purge History
                  </button>
                  <button 
                    onClick={() => handleDeleteRoom(room)}
                    className="text-red-600 hover:text-red-900"
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="flex justify-between items-center mt-4">
        <div className="text-sm text-gray-700">
          Showing <span className="font-medium">{pagination.from + 1}</span> to{' '}
          <span className="font-medium">{Math.min(pagination.from + pagination.limit, pagination.total)}</span> of{' '}
          <span className="font-medium">{pagination.total}</span> rooms
        </div>
        <div className="flex space-x-2">
          <button
            onClick={handlePrevPage}
            disabled={pagination.from === 0}
            className={`px-3 py-1 rounded ${
              pagination.from === 0
                ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                : 'bg-primary-600 text-white hover:bg-primary-700'
            }`}
          >
            Previous
          </button>
          <button
            onClick={handleNextPage}
            disabled={pagination.from + pagination.limit >= pagination.total}
            className={`px-3 py-1 rounded ${
              pagination.from + pagination.limit >= pagination.total
                ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                : 'bg-primary-600 text-white hover:bg-primary-700'
            }`}
          >
            Next
          </button>
        </div>
      </div>

      {/* Delete Room Confirmation Modal */}
      {showDeleteModal && currentRoom && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Confirm Delete Room</h3>
              <button 
                onClick={() => setShowDeleteModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-5">
              <p className="text-gray-700 mb-4">
                Are you sure you want to delete the room <span className="font-semibold">{currentRoom.name || 'Unnamed Room'}</span>?
              </p>
              <p className="text-red-600 mb-4">
                This will remove the room and all its messages. This action cannot be undone.
              </p>
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowDeleteModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={confirmDeleteRoom}
                  className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                >
                  Delete Room
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* View Room Members Modal */}
      {showMembersModal && currentRoom && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Room Members: {currentRoom.name || 'Unnamed Room'}</h3>
              <button 
                onClick={() => setShowMembersModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-5">
              {loadingMembers ? (
                <div className="flex justify-center items-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
                </div>
              ) : roomMembers.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          User ID
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Display Name
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Membership
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {roomMembers.map(member => (
                        <tr key={member.user_id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">{member.user_id}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500">{member.display_name || 'No display name'}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              member.membership === 'join' ? 'bg-green-100 text-green-800' : 
                              member.membership === 'invite' ? 'bg-yellow-100 text-yellow-800' : 
                              member.membership === 'leave' ? 'bg-gray-100 text-gray-800' : 
                              'bg-red-100 text-red-800'
                            }`}>
                              {member.membership}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-gray-700">No members found in this room.</p>
              )}
              <div className="flex justify-end mt-4">
                <button
                  type="button"
                  onClick={() => setShowMembersModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Purge History Modal */}
      {showPurgeHistoryModal && currentRoom && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Purge Room History</h3>
              <button 
                onClick={() => setShowPurgeHistoryModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <form onSubmit={confirmPurgeHistory} className="p-5">
              <p className="text-gray-700 mb-4">
                Purge message history for room <span className="font-semibold">{currentRoom.name || 'Unnamed Room'}</span>.
              </p>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2">
                  Purge messages older than:
                </label>
                <div className="mb-2">
                  <label className="block text-gray-700 text-sm mb-1">
                    Days ago:
                  </label>
                  <input
                    type="number"
                    name="days_ago"
                    min="1"
                    max="365"
                    defaultValue="7"
                    onChange={handlePurgeFormChange}
                    className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  />
                </div>
                <p className="text-xs text-gray-500 mb-2">
                  Messages older than {new Date(purgeFormData.purge_up_to_ts * 1000).toLocaleString()} will be purged.
                </p>
              </div>
              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="delete_local_events"
                    checked={purgeFormData.delete_local_events}
                    onChange={handlePurgeFormChange}
                    className="form-checkbox h-5 w-5 text-primary-600"
                  />
                  <span className="ml-2 text-gray-700">Delete local events</span>
                </label>
              </div>
              <p className="text-yellow-600 mb-4">
                Warning: This will permanently delete message history and cannot be undone.
              </p>
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowPurgeHistoryModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded"
                >
                  Purge History
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default MatrixRoomManagement;