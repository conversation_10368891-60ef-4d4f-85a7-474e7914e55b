import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';
import { Card, CardContent, Typography, Box, Tabs, Tab, CircularProgress, Alert, Paper, Divider } from '@mui/material';

// Define a type for the environment variables
interface EnvVariables {
  [key: string]: string;
}

// Define a type for the API response
interface ApiResponse {
  success: boolean;
  environment: string;
  variables: EnvVariables;
  error?: string;
  details?: string;
}

const EnvironmentVariables: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [backendEnvVars, setBackendEnvVars] = useState<EnvVariables>({});
  const [frontendEnvVars, setFrontendEnvVars] = useState<EnvVariables>({});
  const [environment, setEnvironment] = useState<string>('development');
  const [tabValue, setTabValue] = useState<number>(0);

  // Function to fetch environment variables
  const fetchEnvironmentVariables = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Fetch backend environment variables
      const backendResponse = await axios.get<ApiResponse>(`${API_URL}/environment/variables`);
      
      if (backendResponse.data.success) {
        setBackendEnvVars(backendResponse.data.variables);
        setEnvironment(backendResponse.data.environment);
      } else {
        setError(backendResponse.data.error || 'Failed to fetch backend environment variables');
      }
      
      // Fetch frontend environment variables
      const frontendResponse = await axios.get<ApiResponse>(`${API_URL}/environment/frontend-variables`);
      
      if (frontendResponse.data.success) {
        setFrontendEnvVars(frontendResponse.data.variables);
      } else {
        setError(frontendResponse.data.error || 'Failed to fetch frontend environment variables');
      }
    } catch (err) {
      console.error('Error fetching environment variables:', err);
      setError('Failed to fetch environment variables. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch environment variables on component mount
  useEffect(() => {
    fetchEnvironmentVariables();
  }, []);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Render environment variables as a list
  const renderEnvVars = (envVars: EnvVariables) => {
    return Object.entries(envVars).map(([key, value]) => (
      <Box key={key} sx={{ mb: 1 }}>
        <Typography variant="subtitle1" fontWeight="bold" component="span">
          {key}:
        </Typography>
        <Typography variant="body1" component="span" sx={{ ml: 1 }}>
          {value}
        </Typography>
        <Divider sx={{ mt: 1 }} />
      </Box>
    ));
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Environment Variables
      </Typography>
      
      <Typography variant="subtitle1" gutterBottom>
        Current Environment: <strong>{environment}</strong>
      </Typography>
      
      <Typography variant="body1" paragraph>
        This page displays the environment variables available to the application. 
        Backend variables are filtered to hide sensitive information.
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      <Paper sx={{ mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} centered>
          <Tab label="Backend Variables" />
          <Tab label="Frontend Variables" />
        </Tabs>
      </Paper>
      
      <Card>
        <CardContent>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box>
              {tabValue === 0 ? (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Backend Environment Variables
                  </Typography>
                  {Object.keys(backendEnvVars).length > 0 ? (
                    renderEnvVars(backendEnvVars)
                  ) : (
                    <Typography>No backend environment variables found.</Typography>
                  )}
                </Box>
              ) : (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Frontend Environment Variables
                  </Typography>
                  {Object.keys(frontendEnvVars).length > 0 ? (
                    renderEnvVars(frontendEnvVars)
                  ) : (
                    <Typography>No frontend environment variables found.</Typography>
                  )}
                </Box>
              )}
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default EnvironmentVariables;