import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { 
  getFarmCropTypes, 
  createCropType, 
  updateCropType, 
  deleteCropType,
  CropType 
} from '../../services/cropTypeService';

const CropTypeManagement: React.FC = () => {
  const { selectedFarm } = useFarm();
  const navigate = useNavigate();
  
  const [cropTypes, setCropTypes] = useState<CropType[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  const [showForm, setShowForm] = useState<boolean>(false);
  const [editingCropType, setEditingCropType] = useState<CropType | null>(null);
  
  const [formData, setFormData] = useState<CropType>({
    farm_id: '',
    name: '',
    description: '',
    growing_season: '',
    days_to_maturity: undefined,
    planting_depth: undefined,
    row_spacing: undefined,
    plant_spacing: undefined,
    ideal_soil_ph: undefined,
    ideal_temperature: undefined
  });

  // Fetch crop types when farm changes
  useEffect(() => {
    const fetchCropTypes = async () => {
      if (!selectedFarm) return;
      
      setLoading(true);
      setError(null);
      
      try {
        const data = await getFarmCropTypes(selectedFarm.id);
        setCropTypes(data);
      } catch (err) {
        console.error('Error fetching crop types:', err);
        setError('Failed to load crop types. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchCropTypes();
  }, [selectedFarm]);
  
  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    // Convert numeric values
    if (['days_to_maturity', 'planting_depth', 'row_spacing', 'plant_spacing', 'ideal_soil_ph', 'ideal_temperature'].includes(name)) {
      setFormData(prev => ({
        ...prev,
        [name]: value === '' ? undefined : Number(value)
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedFarm) {
      setError('Please select a farm first.');
      return;
    }
    
    if (!formData.name) {
      setError('Crop type name is required.');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const cropTypeData = {
        ...formData,
        farm_id: selectedFarm.id
      };
      
      let result;
      
      if (editingCropType && editingCropType.id) {
        // Update existing crop type
        result = await updateCropType(editingCropType.id, cropTypeData);
        
        if (result) {
          setCropTypes(prev => 
            prev.map(ct => ct.id === editingCropType.id ? result as CropType : ct)
          );
        }
      } else {
        // Create new crop type
        result = await createCropType(cropTypeData);
        
        if (result) {
          setCropTypes(prev => [...prev, result as CropType]);
        }
      }
      
      if (result) {
        // Reset form and close it
        setFormData({
          farm_id: '',
          name: '',
          description: '',
          growing_season: '',
          days_to_maturity: undefined,
          planting_depth: undefined,
          row_spacing: undefined,
          plant_spacing: undefined,
          ideal_soil_ph: undefined,
          ideal_temperature: undefined
        });
        setEditingCropType(null);
        setShowForm(false);
      } else {
        setError('Failed to save crop type. Please try again.');
      }
    } catch (err) {
      console.error('Error saving crop type:', err);
      setError('An error occurred while saving the crop type.');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle edit button click
  const handleEdit = (cropType: CropType) => {
    setEditingCropType(cropType);
    setFormData(cropType);
    setShowForm(true);
  };
  
  // Handle delete button click
  const handleDelete = async (cropTypeId: string) => {
    if (!window.confirm('Are you sure you want to delete this crop type?')) {
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const success = await deleteCropType(cropTypeId);
      
      if (success) {
        setCropTypes(prev => prev.filter(ct => ct.id !== cropTypeId));
      } else {
        setError('Failed to delete crop type. Please try again.');
      }
    } catch (err) {
      console.error('Error deleting crop type:', err);
      setError('An error occurred while deleting the crop type.');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle add button click
  const handleAdd = () => {
    setEditingCropType(null);
    setFormData({
      farm_id: selectedFarm?.id || '',
      name: '',
      description: '',
      growing_season: '',
      days_to_maturity: undefined,
      planting_depth: undefined,
      row_spacing: undefined,
      plant_spacing: undefined,
      ideal_soil_ph: undefined,
      ideal_temperature: undefined
    });
    setShowForm(true);
  };
  
  // Handle cancel button click
  const handleCancel = () => {
    setShowForm(false);
    setEditingCropType(null);
  };
  
  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Crop Type Management</h1>
          <button
            onClick={handleAdd}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add Crop Type
          </button>
        </div>
        
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        )}
        
        {loading && !showForm ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        ) : (
          <>
            {showForm ? (
              <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
                <div className="px-4 py-5 sm:px-6">
                  <h2 className="text-lg leading-6 font-medium text-gray-900">
                    {editingCropType ? 'Edit Crop Type' : 'Add New Crop Type'}
                  </h2>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    {editingCropType 
                      ? 'Update the details for this crop type.' 
                      : 'Fill in the details to create a new crop type.'}
                  </p>
                </div>
                <div className="border-t border-gray-200">
                  <form onSubmit={handleSubmit} className="p-6">
                    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                      {/* Name */}
                      <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                          Name <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleChange}
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                          required
                        />
                      </div>
                      
                      {/* Growing Season */}
                      <div>
                        <label htmlFor="growing_season" className="block text-sm font-medium text-gray-700 mb-1">
                          Growing Season
                        </label>
                        <select
                          id="growing_season"
                          name="growing_season"
                          value={formData.growing_season || ''}
                          onChange={handleChange}
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        >
                          <option value="">Select a season</option>
                          <option value="spring">Spring</option>
                          <option value="summer">Summer</option>
                          <option value="fall">Fall</option>
                          <option value="winter">Winter</option>
                          <option value="year-round">Year-round</option>
                        </select>
                      </div>
                      
                      {/* Days to Maturity */}
                      <div>
                        <label htmlFor="days_to_maturity" className="block text-sm font-medium text-gray-700 mb-1">
                          Days to Maturity
                        </label>
                        <input
                          type="number"
                          id="days_to_maturity"
                          name="days_to_maturity"
                          value={formData.days_to_maturity === undefined ? '' : formData.days_to_maturity}
                          onChange={handleChange}
                          min="0"
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        />
                      </div>
                      
                      {/* Planting Depth */}
                      <div>
                        <label htmlFor="planting_depth" className="block text-sm font-medium text-gray-700 mb-1">
                          Planting Depth (inches)
                        </label>
                        <input
                          type="number"
                          id="planting_depth"
                          name="planting_depth"
                          value={formData.planting_depth === undefined ? '' : formData.planting_depth}
                          onChange={handleChange}
                          min="0"
                          step="0.1"
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        />
                      </div>
                      
                      {/* Row Spacing */}
                      <div>
                        <label htmlFor="row_spacing" className="block text-sm font-medium text-gray-700 mb-1">
                          Row Spacing (inches)
                        </label>
                        <input
                          type="number"
                          id="row_spacing"
                          name="row_spacing"
                          value={formData.row_spacing === undefined ? '' : formData.row_spacing}
                          onChange={handleChange}
                          min="0"
                          step="0.1"
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        />
                      </div>
                      
                      {/* Plant Spacing */}
                      <div>
                        <label htmlFor="plant_spacing" className="block text-sm font-medium text-gray-700 mb-1">
                          Plant Spacing (inches)
                        </label>
                        <input
                          type="number"
                          id="plant_spacing"
                          name="plant_spacing"
                          value={formData.plant_spacing === undefined ? '' : formData.plant_spacing}
                          onChange={handleChange}
                          min="0"
                          step="0.1"
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        />
                      </div>
                      
                      {/* Ideal Soil pH */}
                      <div>
                        <label htmlFor="ideal_soil_ph" className="block text-sm font-medium text-gray-700 mb-1">
                          Ideal Soil pH
                        </label>
                        <input
                          type="number"
                          id="ideal_soil_ph"
                          name="ideal_soil_ph"
                          value={formData.ideal_soil_ph === undefined ? '' : formData.ideal_soil_ph}
                          onChange={handleChange}
                          min="0"
                          max="14"
                          step="0.1"
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        />
                      </div>
                      
                      {/* Ideal Temperature */}
                      <div>
                        <label htmlFor="ideal_temperature" className="block text-sm font-medium text-gray-700 mb-1">
                          Ideal Temperature (°F)
                        </label>
                        <input
                          type="number"
                          id="ideal_temperature"
                          name="ideal_temperature"
                          value={formData.ideal_temperature === undefined ? '' : formData.ideal_temperature}
                          onChange={handleChange}
                          min="0"
                          step="0.1"
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        />
                      </div>
                    </div>
                    
                    {/* Description */}
                    <div className="mt-6">
                      <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                        Description
                      </label>
                      <textarea
                        id="description"
                        name="description"
                        value={formData.description || ''}
                        onChange={handleChange}
                        rows={4}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      ></textarea>
                    </div>
                    
                    {/* Form Actions */}
                    <div className="mt-6 flex justify-end space-x-3">
                      <button
                        type="button"
                        onClick={handleCancel}
                        className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={loading}
                        className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
                      >
                        {loading ? 'Saving...' : 'Save Crop Type'}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            ) : (
              <>
                {cropTypes.length === 0 ? (
                  <div className="bg-white shadow overflow-hidden sm:rounded-lg">
                    <div className="px-4 py-5 sm:p-6 text-center">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">No crop types found</h3>
                      <div className="mt-2 max-w-xl text-sm text-gray-500 mx-auto">
                        <p>Get started by adding your first crop type.</p>
                      </div>
                      <div className="mt-5">
                        <button
                          onClick={handleAdd}
                          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                        >
                          Add Crop Type
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="bg-white shadow overflow-hidden sm:rounded-lg">
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Name
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Growing Season
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Days to Maturity
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Description
                            </th>
                            <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Actions
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {cropTypes.map((cropType) => (
                            <tr key={cropType.id}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {cropType.name}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {cropType.growing_season 
                                  ? cropType.growing_season.charAt(0).toUpperCase() + cropType.growing_season.slice(1) 
                                  : 'N/A'}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {cropType.days_to_maturity || 'N/A'}
                              </td>
                              <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                                {cropType.description || 'N/A'}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <button
                                  onClick={() => handleEdit(cropType)}
                                  className="text-primary-600 hover:text-primary-900 mr-4"
                                >
                                  Edit
                                </button>
                                <button
                                  onClick={() => cropType.id && handleDelete(cropType.id)}
                                  className="text-red-600 hover:text-red-900"
                                >
                                  Delete
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </>
            )}
          </>
        )}
      </div>
    </Layout>
  );
};

export default CropTypeManagement;