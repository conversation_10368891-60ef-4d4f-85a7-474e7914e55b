import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import axios from 'axios';
import { API_URL } from '../../config';
import { Link, useParams, useNavigate, useLocation } from 'react-router-dom';
import LoadingSpinner from '../../components/LoadingSpinner';
import { 
  PayrollTaxReport, 
  TaxForecast, 
  TaxIntegrationPanel, 
  TaxDocumentCenter,
  TaxDeductionForm,
  TaxDocumentForm,
  EmployeeTaxInfoForm,
  ContractorTaxInfoForm,
  TaxPaymentForm,
  TaxFilingForm
} from '../../components/tax';
import useTaxPermissions from '../../hooks/useTaxPermissions';

interface TaxCategory {
  id: string;
  name: string;
  description: string;
  amount: number;
  deductible: boolean;
}

interface TaxDeduction {
  id: string;
  name: string;
  description: string;
  amount: number;
  category: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  updatedAt: string;
}

interface TaxSummary {
  totalIncome: number;
  totalExpenses: number;
  totalDeductions: number;
  estimatedTaxableIncome: number;
  estimatedTaxLiability: number;
  potentialSavings: number;
}

interface TaxDocument {
  id: string;
  title: string;
  description?: string;
  documentType: string;
  taxYear: number;
  fileName?: string;
  fileSize?: number;
  fileType?: string;
  status: string;
  submittedDate?: string;
  createdBy?: {
    id: string;
    name: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface EmployeeTaxInfo {
  id: string;
  employeeId: string;
  employee: {
    id: string;
    name: string;
    email: string;
    position: string;
  };
  taxYear: number;
  filingStatus?: string;
  withholdingAllowances: number;
  additionalWithholding: number;
  isExempt: boolean;
  w2Generated: boolean;
  w2Document?: {
    id: string;
    title: string;
    fileName?: string;
    status: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface ContractorTaxInfo {
  id: string;
  contractorName: string;
  businessName?: string;
  taxYear: number;
  totalPayments: number;
  form1099Generated: boolean;
  form1099Document?: {
    id: string;
    title: string;
    fileName?: string;
    status: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface TaxPayment {
  id: string;
  paymentDate: string;
  amount: number;
  paymentMethod: string;
  paymentReference?: string;
  taxYear: number;
  taxPeriod: string;
  taxType: string;
  description?: string;
  receiptDocument?: {
    id: string;
    title: string;
    fileName?: string;
    status: string;
  };
  createdBy?: {
    id: string;
    name: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface TaxFiling {
  id: string;
  taxYear: number;
  filingType: string;
  formNumber?: string;
  dueDate: string;
  filingDate?: string;
  extensionFiled: boolean;
  extensionDate?: string;
  status: string;
  filingMethod?: string;
  confirmationNumber?: string;
  totalTax?: number;
  totalPaid?: number;
  balanceDue?: number;
  refundAmount?: number;
  notes?: string;
  filingDocument?: {
    id: string;
    title: string;
    fileName?: string;
    status: string;
  };
  createdBy?: {
    id: string;
    name: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface TaxRecommendation {
  id: string;
  title: string;
  description: string;
  potentialSavings: number;
  priority: string;
  category: string;
}

const TaxManagement = () => {
  const { user, token } = useAuth();
  const { currentFarm } = useFarm();
  const { tab } = useParams<{ tab?: string }>();
  const navigate = useNavigate();
  const location = useLocation();

  const [taxCategories, setTaxCategories] = useState<TaxCategory[]>([]);
  const [taxDeductions, setTaxDeductions] = useState<TaxDeduction[]>([]);
  const [taxSummary, setTaxSummary] = useState<TaxSummary | null>(null);
  const [taxDocuments, setTaxDocuments] = useState<TaxDocument[]>([]);
  const [employeeTaxInfo, setEmployeeTaxInfo] = useState<EmployeeTaxInfo[]>([]);
  const [contractorTaxInfo, setContractorTaxInfo] = useState<ContractorTaxInfo[]>([]);
  const [taxPayments, setTaxPayments] = useState<TaxPayment[]>([]);
  const [taxFilings, setTaxFilings] = useState<TaxFiling[]>([]);
  const [taxRecommendations, setTaxRecommendations] = useState<TaxRecommendation[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedYear, setSelectedYear] = useState<string>(new Date().getFullYear().toString());

  // Define valid tab values
  type TabType = 'summary' | 'deductions' | 'planning' | 'documents' | 'employees' | 'contractors' | 'payments' | 'filings' | 'payroll' | 'forecast' | 'integration';

  // Set active tab based on URL parameter or default to 'summary'
  const activeTab = (tab as TabType) || 'summary';

  // Get tax management permissions
  const { canView, canCreate, canEdit, canDelete, isLoading: permissionsLoading, error: permissionsError } = useTaxPermissions();

  useEffect(() => {
    if (user && currentFarm) {
      fetchTaxData();
    }
  }, [user, currentFarm, selectedYear]);

  const fetchTaxData = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!currentFarm?.id) {
        throw new Error('No farm selected');
      }

      const year = parseInt(selectedYear);

      // Fetch tax summary
      const summaryResponse = await axios.get(`${API_URL}/api/tax/farms/${currentFarm.id}/tax-summary`, {
        headers: { Authorization: `Bearer ${token}` },
        params: { year }
      });

      if (summaryResponse.data && summaryResponse.data.taxSummary) {
        setTaxSummary(summaryResponse.data.taxSummary);
      }

      // Fetch tax categories
      const categoriesResponse = await axios.get(`${API_URL}/api/tax/farms/${currentFarm.id}/tax-categories`, {
        headers: { Authorization: `Bearer ${token}` },
        params: { year }
      });

      if (categoriesResponse.data && categoriesResponse.data.taxCategories) {
        setTaxCategories(categoriesResponse.data.taxCategories);
      }

      // Fetch tax deductions
      const deductionsResponse = await axios.get(`${API_URL}/api/tax/farms/${currentFarm.id}/tax-deductions`, {
        headers: { Authorization: `Bearer ${token}` },
        params: { year }
      });

      if (deductionsResponse.data && deductionsResponse.data.taxDeductions) {
        setTaxDeductions(deductionsResponse.data.taxDeductions);
      }

      // Fetch tax documents
      const documentsResponse = await axios.get(`${API_URL}/api/tax/farms/${currentFarm.id}/tax-documents`, {
        headers: { Authorization: `Bearer ${token}` },
        params: { year }
      });

      if (documentsResponse.data && documentsResponse.data.taxDocuments) {
        setTaxDocuments(documentsResponse.data.taxDocuments);
      }

      // Fetch employee tax info
      const employeeTaxResponse = await axios.get(`${API_URL}/api/tax/farms/${currentFarm.id}/employee-tax-info`, {
        headers: { Authorization: `Bearer ${token}` },
        params: { year }
      });

      if (employeeTaxResponse.data && employeeTaxResponse.data.employeeTaxInfo) {
        setEmployeeTaxInfo(employeeTaxResponse.data.employeeTaxInfo);
      }

      // Fetch contractor tax info
      const contractorTaxResponse = await axios.get(`${API_URL}/api/tax/farms/${currentFarm.id}/contractor-tax-info`, {
        headers: { Authorization: `Bearer ${token}` },
        params: { year }
      });

      if (contractorTaxResponse.data && contractorTaxResponse.data.contractorTaxInfo) {
        setContractorTaxInfo(contractorTaxResponse.data.contractorTaxInfo);
      }

      // Fetch tax payments
      const paymentsResponse = await axios.get(`${API_URL}/api/tax/farms/${currentFarm.id}/tax-payments`, {
        headers: { Authorization: `Bearer ${token}` },
        params: { year }
      });

      if (paymentsResponse.data && paymentsResponse.data.taxPayments) {
        setTaxPayments(paymentsResponse.data.taxPayments);
      }

      // Fetch tax filings
      const filingsResponse = await axios.get(`${API_URL}/api/tax/farms/${currentFarm.id}/tax-filings`, {
        headers: { Authorization: `Bearer ${token}` },
        params: { year }
      });

      if (filingsResponse.data && filingsResponse.data.taxFilings) {
        setTaxFilings(filingsResponse.data.taxFilings);
      }

      // Fetch tax planning recommendations
      const recommendationsResponse = await axios.get(`${API_URL}/api/tax/farms/${currentFarm.id}/tax-planning-recommendations`, {
        headers: { Authorization: `Bearer ${token}` },
        params: { year }
      });

      if (recommendationsResponse.data && recommendationsResponse.data.recommendations) {
        setTaxRecommendations(recommendationsResponse.data.recommendations);
      }

    } catch (err: any) {
      console.error('Error fetching tax data:', err);

      // Handle authentication errors without causing logout
      if (err.response && err.response.status === 401) {
        setError('Authentication error. Please refresh the page and try again.');
      } else {
        setError('Failed to load tax data. Please try again later.');
      }
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Show loading spinner while permissions or data are loading
  if ((loading && !taxSummary) || permissionsLoading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-full">
          <LoadingSpinner />
        </div>
      </Layout>
    );
  }

  // Show error message if user doesn't have view permission
  if (!canView && !permissionsLoading) {
    return (
      <Layout>
        <div className="px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-red-50 border-l-4 border-red-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">You do not have permission to view tax management information.</p>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900">Tax Management</h1>
            <p className="mt-2 text-sm text-gray-700">
              Tools for agricultural tax planning, tracking, and reporting.
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <div className="inline-flex rounded-md shadow-sm">
              <select
                id="year-select"
                name="year-select"
                className="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-primary-500 focus:outline-none focus:ring-primary-500 sm:text-sm"
                value={selectedYear}
                onChange={(e) => setSelectedYear(e.target.value)}
              >
                <option value="2024">2024</option>
                <option value="2023">2023</option>
                <option value="2022">2022</option>
                <option value="2021">2021</option>
              </select>
            </div>
          </div>
        </div>

        {error && (
          <div className="mt-4 bg-red-50 border-l-4 border-red-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="mt-8">
          <div className="hidden sm:block">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8 overflow-x-auto" aria-label="Tabs">
                <Link
                  to="/financial-management/tax/summary"
                  className={`${
                    activeTab === 'summary'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  Tax Summary
                </Link>
                <Link
                  to="/financial-management/tax/deductions"
                  className={`${
                    activeTab === 'deductions'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  Deductions
                </Link>
                <Link
                  to="/financial-management/tax/documents"
                  className={`${
                    activeTab === 'documents'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  Tax Documents
                </Link>
                <Link
                  to="/financial-management/tax/employees"
                  className={`${
                    activeTab === 'employees'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  Employee Taxes
                </Link>
                <Link
                  to="/financial-management/tax/contractors"
                  className={`${
                    activeTab === 'contractors'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  Contractor Taxes
                </Link>
                <Link
                  to="/financial-management/tax/payments"
                  className={`${
                    activeTab === 'payments'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  Tax Payments
                </Link>
                <Link
                  to="/financial-management/tax/filings"
                  className={`${
                    activeTab === 'filings'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  Tax Filings
                </Link>
                <Link
                  to="/financial-management/tax/planning"
                  className={`${
                    activeTab === 'planning'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  Tax Planning
                </Link>
                <Link
                  to="/financial-management/tax/payroll"
                  className={`${
                    activeTab === 'payroll'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  Payroll Taxes
                </Link>
                <Link
                  to="/financial-management/tax/forecast"
                  className={`${
                    activeTab === 'forecast'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  Tax Forecast
                </Link>
                <Link
                  to="/financial-management/tax/integration"
                  className={`${
                    activeTab === 'integration'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  Integration
                </Link>
              </nav>
            </div>
          </div>
        </div>

        {/* Tab Content */}
        <div className="mt-8">
          {/* Tax Summary Tab */}
          {activeTab === 'summary' && taxSummary && (
            <div>
              <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Farm Income</dt>
                    <dd className="mt-1 text-3xl font-semibold text-gray-900">{formatCurrency(taxSummary.totalIncome)}</dd>
                  </div>
                </div>
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Farm Expenses</dt>
                    <dd className="mt-1 text-3xl font-semibold text-gray-900">{formatCurrency(taxSummary.totalExpenses)}</dd>
                  </div>
                </div>
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Deductions</dt>
                    <dd className="mt-1 text-3xl font-semibold text-gray-900">{formatCurrency(taxSummary.totalDeductions)}</dd>
                  </div>
                </div>
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <dt className="text-sm font-medium text-gray-500 truncate">Estimated Taxable Income</dt>
                    <dd className="mt-1 text-3xl font-semibold text-gray-900">{formatCurrency(taxSummary.estimatedTaxableIncome)}</dd>
                  </div>
                </div>
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <dt className="text-sm font-medium text-gray-500 truncate">Estimated Tax Liability</dt>
                    <dd className="mt-1 text-3xl font-semibold text-gray-900">{formatCurrency(taxSummary.estimatedTaxLiability)}</dd>
                  </div>
                </div>
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <dt className="text-sm font-medium text-gray-500 truncate">Potential Tax Savings</dt>
                    <dd className="mt-1 text-3xl font-semibold text-green-600">{formatCurrency(taxSummary.potentialSavings)}</dd>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Deductions Tab */}
          {activeTab === 'deductions' && (
            <div>
              <div className="bg-white shadow overflow-hidden sm:rounded-lg">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">Tax Deductions</h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    Available tax deductions for your farm business.
                  </p>
                </div>
                <div className="border-t border-gray-200">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Deduction
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Category
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Amount
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {taxDeductions.map((deduction) => (
                          <tr key={deduction.id}>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm font-medium text-gray-900">{deduction.name}</div>
                              <div className="text-sm text-gray-500">{deduction.description}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {deduction.category}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {formatCurrency(deduction.amount)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(deduction.status)}`}>
                                {deduction.status.charAt(0).toUpperCase() + deduction.status.slice(1)}
                              </span>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              <div className="mt-8">
                <TaxDeductionForm 
                  farmId={currentFarm?.id || ''}
                  year={parseInt(selectedYear)}
                  onDeductionAdded={fetchTaxData}
                />
              </div>
            </div>
          )}

          {/* Tax Documents Tab */}
          {activeTab === 'documents' && (
            <div>
              <TaxDocumentCenter 
                farmId={currentFarm?.id || ''}
                year={parseInt(selectedYear)}
                canCreate={canCreate}
                canEdit={canEdit}
                canDelete={canDelete}
              />
              <div className="mt-8">
                <TaxDocumentForm
                  farmId={currentFarm?.id || ''}
                  year={parseInt(selectedYear)}
                  onDocumentAdded={fetchTaxData}
                />
              </div>
            </div>
          )}

          {/* Employee Taxes Tab */}
          {activeTab === 'employees' && (
            <div>
              <div className="bg-white shadow overflow-hidden sm:rounded-lg">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">Employee Tax Information</h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    Manage tax information for your farm employees.
                  </p>
                </div>
                <div className="border-t border-gray-200">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Employee
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Filing Status
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            W2 Status
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Tax Year
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {employeeTaxInfo.length > 0 ? (
                          employeeTaxInfo.map((info) => (
                            <tr key={info.id}>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm font-medium text-gray-900">{info.employee.name}</div>
                                <div className="text-sm text-gray-500">{info.employee.position}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {info.filingStatus ? info.filingStatus.replace('_', ' ').split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ') : 'Not Set'}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                {info.w2Generated ? (
                                  <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    Generated
                                  </span>
                                ) : (
                                  <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    Not Generated
                                  </span>
                                )}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {info.taxYear}
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan={4} className="px-6 py-4 text-center text-sm text-gray-500">
                              No employee tax information found.
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              <div className="mt-8">
                <EmployeeTaxInfoForm 
                  farmId={currentFarm?.id || ''}
                  year={parseInt(selectedYear)}
                  onInfoAdded={fetchTaxData}
                />
              </div>
            </div>
          )}

          {/* Contractor Taxes Tab */}
          {activeTab === 'contractors' && (
            <div>
              <div className="bg-white shadow overflow-hidden sm:rounded-lg">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">Contractor Tax Information</h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    Manage tax information for your farm contractors.
                  </p>
                </div>
                <div className="border-t border-gray-200">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Contractor
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Total Payments
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            1099 Status
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Tax Year
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {contractorTaxInfo.length > 0 ? (
                          contractorTaxInfo.map((info) => (
                            <tr key={info.id}>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm font-medium text-gray-900">{info.contractorName}</div>
                                <div className="text-sm text-gray-500">{info.businessName}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {formatCurrency(info.totalPayments)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                {info.form1099Generated ? (
                                  <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    Generated
                                  </span>
                                ) : (
                                  <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    Not Generated
                                  </span>
                                )}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {info.taxYear}
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan={4} className="px-6 py-4 text-center text-sm text-gray-500">
                              No contractor tax information found.
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              <div className="mt-8">
                <ContractorTaxInfoForm 
                  farmId={currentFarm?.id || ''}
                  year={parseInt(selectedYear)}
                  onInfoAdded={fetchTaxData}
                />
              </div>
            </div>
          )}

          {/* Tax Payments Tab */}
          {activeTab === 'payments' && (
            <div>
              <div className="bg-white shadow overflow-hidden sm:rounded-lg">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">Tax Payments</h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    Track and manage your farm's tax payments.
                  </p>
                </div>
                <div className="border-t border-gray-200">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Payment Date
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Amount
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Tax Type
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Period
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Method
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {taxPayments.length > 0 ? (
                          taxPayments.map((payment) => (
                            <tr key={payment.id}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {new Date(payment.paymentDate).toLocaleDateString()}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {formatCurrency(payment.amount)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {payment.taxType.charAt(0).toUpperCase() + payment.taxType.slice(1)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {payment.taxPeriod.toUpperCase()}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {payment.paymentMethod.replace('_', ' ').split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                              No tax payments found.
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              <div className="mt-8">
                <TaxPaymentForm 
                  farmId={currentFarm?.id || ''}
                  year={parseInt(selectedYear)}
                  onPaymentAdded={fetchTaxData}
                />
              </div>
            </div>
          )}

          {/* Tax Filings Tab */}
          {activeTab === 'filings' && (
            <div>
              <div className="bg-white shadow overflow-hidden sm:rounded-lg">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">Tax Filings</h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    Track and manage your farm's tax filings.
                  </p>
                </div>
                <div className="border-t border-gray-200">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Filing Type
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Due Date
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Tax Year
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {taxFilings.length > 0 ? (
                          taxFilings.map((filing) => (
                            <tr key={filing.id}>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm font-medium text-gray-900">
                                  {filing.filingType.replace('_', ' ').split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                                </div>
                                <div className="text-sm text-gray-500">{filing.formNumber}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {new Date(filing.dueDate).toLocaleDateString()}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(filing.status)}`}>
                                  {filing.status.replace('_', ' ').split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {filing.taxYear}
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan={4} className="px-6 py-4 text-center text-sm text-gray-500">
                              No tax filings found.
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              <div className="mt-8">
                <TaxFilingForm 
                  farmId={currentFarm?.id || ''}
                  year={parseInt(selectedYear)}
                  onSave={(filing, filingDocument) => {
                    fetchTaxData();
                  }}
                  onCancel={() => {
                    // Do nothing on cancel
                  }}
                />
              </div>
            </div>
          )}

          {/* Tax Planning Tab */}
          {activeTab === 'planning' && (
            <div>
              <div className="bg-white shadow overflow-hidden sm:rounded-lg">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">Tax Planning Recommendations</h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    Personalized recommendations to optimize your farm's tax position.
                  </p>
                </div>
                <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
                  <div className="space-y-6">
                    {taxRecommendations.length > 0 ? (
                      taxRecommendations.map((recommendation) => (
                        <div key={recommendation.id} className={`bg-${recommendation.priority === 'high' ? 'green' : recommendation.priority === 'medium' ? 'blue' : 'yellow'}-50 p-4 rounded-md`}>
                          <div className="flex">
                            <div className="ml-3">
                              <h3 className={`text-sm font-medium text-${recommendation.priority === 'high' ? 'green' : recommendation.priority === 'medium' ? 'blue' : 'yellow'}-800`}>
                                {recommendation.title}
                              </h3>
                              <div className={`mt-2 text-sm text-${recommendation.priority === 'high' ? 'green' : recommendation.priority === 'medium' ? 'blue' : 'yellow'}-700`}>
                                <p>{recommendation.description}</p>
                                {recommendation.potentialSavings > 0 && (
                                  <p className="mt-1">
                                    <strong>Potential Savings:</strong> {formatCurrency(recommendation.potentialSavings)}
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <>
                        <div className="bg-green-50 p-4 rounded-md">
                          <div className="flex">
                            <div className="ml-3">
                              <h3 className="text-sm font-medium text-green-800">Consider Section 179 Deduction</h3>
                              <div className="mt-2 text-sm text-green-700">
                                <p>You have equipment purchases that may qualify for Section 179 deduction. This could allow you to deduct up to $1,080,000 in 2023.</p>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="bg-blue-50 p-4 rounded-md">
                          <div className="flex">
                            <div className="ml-3">
                              <h3 className="text-sm font-medium text-blue-800">Income Deferral Opportunity</h3>
                              <div className="mt-2 text-sm text-blue-700">
                                <p>Consider deferring income to next year by delaying crop sales until January. This could potentially reduce your current year tax liability.</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Payroll Taxes Tab */}
          {activeTab === 'payroll' && (
            <div>
              <PayrollTaxReport 
                farmId={currentFarm?.id || ''} 
                year={parseInt(selectedYear)}
              />
            </div>
          )}

          {/* Tax Forecast Tab */}
          {activeTab === 'forecast' && (
            <div>
              <TaxForecast 
                farmId={currentFarm?.id || ''} 
                year={parseInt(selectedYear)}
              />
            </div>
          )}

          {/* Integration Tab */}
          {activeTab === 'integration' && (
            <div>
              <TaxIntegrationPanel 
                year={parseInt(selectedYear)}
              />
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default TaxManagement;
