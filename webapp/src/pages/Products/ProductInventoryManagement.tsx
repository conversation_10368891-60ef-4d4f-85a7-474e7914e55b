import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';
import { 
  getProductInventory, 
  linkProductToInventory, 
  updateProductInventoryLink, 
  deleteProductInventoryLink, 
  ProductInventoryLink 
} from '../../services/productInventoryService';

interface Product {
  id: string;
  name: string;
  unit: string;
}

interface InventoryItem {
  id: string;
  name: string;
  quantity: number;
  unit: string;
}

const ProductInventoryManagement = () => {
  const { productId } = useParams<{ productId: string }>();
  const navigate = useNavigate();

  const [product, setProduct] = useState<Product | null>(null);
  const [productInventory, setProductInventory] = useState<ProductInventoryLink[]>([]);
  const [availableInventoryItems, setAvailableInventoryItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Form state for adding new link
  const [selectedInventoryItem, setSelectedInventoryItem] = useState<string>('');
  const [quantityPerUnit, setQuantityPerUnit] = useState<number>(1);
  const [addingLink, setAddingLink] = useState(false);

  // Fetch product and inventory data
  useEffect(() => {
    const fetchData = async () => {
      if (!productId) return;

      setLoading(true);
      setError(null);

      try {
        // Fetch product details
        const productResponse = await axios.get(`${API_URL}/products/${productId}`);
        setProduct(productResponse.data);

        // Fetch inventory items linked to this product
        const inventoryLinks = await getProductInventory(productId);
        setProductInventory(inventoryLinks);

        // Fetch all inventory items for the farm
        const farmId = productResponse.data.farm_id;
        const inventoryResponse = await axios.get(`${API_URL}/api/inventory?farmId=${farmId}`);

        // Filter out inventory items that are already linked to this product
        const linkedItemIds = inventoryLinks.map(link => link.inventory_item_id);
        const availableItems = inventoryResponse.data.filter(
          (item: InventoryItem) => !linkedItemIds.includes(item.id)
        );

        setAvailableInventoryItems(availableItems);
      } catch (err: any) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [productId]);

  // Handle adding a new inventory link
  const handleAddLink = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!productId || !selectedInventoryItem) {
      return;
    }

    setAddingLink(true);

    try {
      await linkProductToInventory(productId, selectedInventoryItem, quantityPerUnit);

      // Refresh the data
      const inventoryLinks = await getProductInventory(productId);
      setProductInventory(inventoryLinks);

      // Update available inventory items
      const linkedItemIds = inventoryLinks.map(link => link.inventory_item_id);
      const updatedAvailableItems = availableInventoryItems.filter(
        item => !linkedItemIds.includes(item.id)
      );
      setAvailableInventoryItems(updatedAvailableItems);

      // Reset form
      setSelectedInventoryItem('');
      setQuantityPerUnit(1);
    } catch (err: any) {
      console.error('Error adding inventory link:', err);
      setError('Failed to add inventory link. Please try again later.');
    } finally {
      setAddingLink(false);
    }
  };

  // Handle updating an inventory link
  const handleUpdateLink = async (linkId: string, newQuantityPerUnit: number) => {
    try {
      await updateProductInventoryLink(linkId, newQuantityPerUnit);

      // Refresh the data
      const inventoryLinks = await getProductInventory(productId!);
      setProductInventory(inventoryLinks);
    } catch (err: any) {
      console.error('Error updating inventory link:', err);
      setError('Failed to update inventory link. Please try again later.');
    }
  };

  // Handle deleting an inventory link
  const handleDeleteLink = async (linkId: string) => {
    if (!window.confirm('Are you sure you want to remove this inventory link?')) {
      return;
    }

    try {
      await deleteProductInventoryLink(linkId);

      // Find the deleted link to add its inventory item back to available items
      const deletedLink = productInventory.find(link => link.id === linkId);
      if (deletedLink && deletedLink.InventoryItem) {
        setAvailableInventoryItems(prev => [...prev, deletedLink.InventoryItem!]);
      }

      // Remove the link from the list
      setProductInventory(prev => prev.filter(link => link.id !== linkId));
    } catch (err: any) {
      console.error('Error deleting inventory link:', err);
      setError('Failed to delete inventory link. Please try again later.');
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading...</p>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
        <div className="flex justify-center mt-4">
          <Link
            to={`/products/${productId}`}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Product
          </Link>
        </div>
      </Layout>
    );
  }

  if (!product) {
    return (
      <Layout>
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">Product not found.</span>
        </div>
        <div className="flex justify-center mt-4">
          <Link
            to="/products"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Products
          </Link>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Manage Inventory for {product.name}</h1>
        <Link
          to={`/products/${productId}`}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Product
        </Link>
      </div>

      {/* Current Inventory Links */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
        <div className="px-4 py-5 sm:px-6">
          <h2 className="text-lg leading-6 font-medium text-gray-900">Current Inventory Links</h2>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Inventory items currently linked to this product.
          </p>
        </div>

        <div className="border-t border-gray-200">
          {productInventory.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Item Name
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Current Stock
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Used Per Unit
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {productInventory.map((link) => {
                    const inventoryItem = link.InventoryItem;
                    if (!inventoryItem) return null;

                    return (
                      <tr key={link.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          <Link to={`/inventory/${inventoryItem.id}`} className="text-primary-600 hover:text-primary-900">
                            {inventoryItem.name}
                          </Link>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {inventoryItem.quantity} {inventoryItem.unit}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <input
                            type="number"
                            min="0.01"
                            step="0.01"
                            className="w-20 px-2 py-1 border border-gray-300 rounded-md"
                            value={link.quantity_per_unit}
                            onChange={(e) => {
                              const newValue = parseFloat(e.target.value);
                              if (!isNaN(newValue) && newValue > 0) {
                                handleUpdateLink(link.id, newValue);
                              }
                            }}
                          />
                          {' '}{inventoryItem.unit} per {product.unit}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <button
                            onClick={() => handleDeleteLink(link.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            Remove
                          </button>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="px-4 py-5 sm:px-6 text-center">
              <p className="text-sm text-gray-500">No inventory items are linked to this product yet.</p>
            </div>
          )}
        </div>
      </div>

      {/* Add New Inventory Link */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:px-6">
          <h2 className="text-lg leading-6 font-medium text-gray-900">Add Inventory Link</h2>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Link this product to an inventory item to track stock levels. Each product can be linked to multiple inventory items.
          </p>
          <div className="mt-2 bg-blue-50 border-l-4 border-blue-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-blue-700">
                  <strong>How it works:</strong> When a product is sold, the system will automatically deduct the required quantities from the linked inventory items. The "Quantity Per Unit" field determines how much of each inventory item is used to make one unit of this product.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
          {availableInventoryItems.length > 0 ? (
            <form onSubmit={handleAddLink} className="space-y-4">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                <div>
                  <label htmlFor="inventory-item" className="block text-sm font-medium text-gray-700 mb-1">
                    Inventory Item
                  </label>
                  <select
                    id="inventory-item"
                    value={selectedInventoryItem}
                    onChange={(e) => setSelectedInventoryItem(e.target.value)}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                    required
                  >
                    <option value="">Select an inventory item</option>
                    {availableInventoryItems.map((item) => (
                      <option key={item.id} value={item.id}>
                        {item.name} ({item.quantity} {item.unit} available)
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="quantity-per-unit" className="block text-sm font-medium text-gray-700 mb-1">
                    Quantity Per Unit
                  </label>
                  <div className="mt-1 flex rounded-md shadow-sm">
                    <input
                      type="number"
                      id="quantity-per-unit"
                      min="0.01"
                      step="0.01"
                      value={quantityPerUnit}
                      onChange={(e) => {
                        const value = parseFloat(e.target.value);
                        if (!isNaN(value) && value > 0) {
                          setQuantityPerUnit(value);
                        }
                      }}
                      className="focus:ring-primary-500 focus:border-primary-500 flex-1 block w-full rounded-md sm:text-sm border-gray-300"
                      placeholder="1.0"
                      required
                    />
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    Amount of inventory used per unit of product
                  </p>
                </div>

                <div className="flex items-end">
                  <button
                    type="submit"
                    disabled={addingLink || !selectedInventoryItem}
                    className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
                      (addingLink || !selectedInventoryItem) ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                  >
                    {addingLink ? 'Adding...' : 'Add Link'}
                  </button>
                </div>
              </div>
            </form>
          ) : (
            <div className="text-center py-4">
              <p className="text-sm text-gray-500 mb-4">
                All available inventory items are already linked to this product.
              </p>
              <Link
                to="/inventory/new"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Create New Inventory Item
              </Link>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default ProductInventoryManagement;
