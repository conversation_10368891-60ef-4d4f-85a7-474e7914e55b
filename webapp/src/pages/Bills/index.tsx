import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import BillsList from './BillsList';
import BillDetail from './BillDetail';
import BillForm from './BillForm';
import BillCategoriesList from './BillCategoriesList';
import BillCategoryForm from './BillCategoryForm';

// Export individual components
export { BillsList, BillDetail, BillForm, BillCategoriesList, BillCategoryForm };

// Default export for the main routes
const BillsRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={<BillsList />} />
      <Route path="/new" element={<BillForm />} />
      <Route path="/:billId" element={<BillDetail />} />
      <Route path="/:billId/edit" element={<BillForm />} />
      <Route path="/categories" element={<BillCategoriesList />} />
      <Route path="/categories/new" element={<BillCategoryForm />} />
      <Route path="/categories/:categoryId/edit" element={<BillCategoryForm />} />
    </Routes>
  );
};

export default BillsRoutes;