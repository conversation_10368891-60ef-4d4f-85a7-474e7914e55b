import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { <PERSON><PERSON>, Table, Spinner, Alert } from 'react-bootstrap';
import { FaEdit, FaTrash } from 'react-icons/fa';
import axios from 'axios';

interface BillCategory {
  id: string;
  name: string;
  description: string;
  createdAt: string;
}

const BillCategoriesList: React.FC = () => {
  const [categories, setCategories] = useState<BillCategory[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/api/bill-categories');
        setCategories(response.data);
        setError(null);
      } catch (err) {
        console.error('Error fetching bill categories:', err);
        setError('Failed to load bill categories. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this category?')) {
      try {
        await axios.delete(`/api/bill-categories/${id}`);
        setCategories(categories.filter(category => category.id !== id));
      } catch (err) {
        console.error('Error deleting bill category:', err);
        setError('Failed to delete the category. Please try again later.');
      }
    }
  };

  if (loading) {
    return (
      <div className="text-center my-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
      </div>
    );
  }

  return (
    <div className="container mt-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1>Bill Categories</h1>
        <Link to="/bills/categories/new">
          <Button variant="primary">Add New Category</Button>
        </Link>
      </div>

      {error && <Alert variant="danger">{error}</Alert>}

      {categories.length === 0 ? (
        <Alert variant="info">No bill categories found. Create your first category to get started.</Alert>
      ) : (
        <Table striped bordered hover responsive>
          <thead>
            <tr>
              <th>Name</th>
              <th>Description</th>
              <th>Created At</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {categories.map((category) => (
              <tr key={category.id}>
                <td>{category.name}</td>
                <td>{category.description}</td>
                <td>{new Date(category.createdAt).toLocaleDateString()}</td>
                <td>
                  <Link to={`/bills/categories/${category.id}/edit`} className="btn btn-sm btn-warning me-2">
                    <FaEdit /> Edit
                  </Link>
                  <Button 
                    variant="danger" 
                    size="sm" 
                    onClick={() => handleDelete(category.id)}
                  >
                    <FaTrash /> Delete
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
      )}
    </div>
  );
};

export default BillCategoriesList;