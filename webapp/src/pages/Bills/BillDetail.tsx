import { useState, useEffect } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { format } from 'date-fns';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { getBillById, deleteBill, addBillPayment, linkTransaction, unlinkTransaction, deleteAttachment } from '../../services/billsService';
import { Bill, BillPaymentFormData, BillTransactionLinkFormData } from '../../types/bills';

const BillDetail = () => {
  const { billId } = useParams<{ billId: string }>();
  const [bill, setBill] = useState<Bill | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showLinkTransactionModal, setShowLinkTransactionModal] = useState(false);
  const [transactions, setTransactions] = useState<any[]>([]);
  const [paymentFormData, setPaymentFormData] = useState<BillPaymentFormData>({
    amount: 0,
    paymentDate: format(new Date(), 'yyyy-MM-dd'),
    paymentMethod: '',
    referenceNumber: '',
    notes: ''
  });
  const [transactionLinkFormData, setTransactionLinkFormData] = useState<BillTransactionLinkFormData>({
    transactionId: '',
    amount: 0,
    notes: ''
  });

  const { selectedFarm } = useFarm();
  const navigate = useNavigate();

  // Fetch bill details
  useEffect(() => {
    if (!billId) return;

    const fetchBillDetails = async () => {
      try {
        setLoading(true);
        setError(null);

        const billData = await getBillById(billId);
        setBill(billData);

        // TODO: Fetch available transactions for linking
        // This would be implemented in a real application
        // For now, we'll use an empty array
        setTransactions([]);
      } catch (err: any) {
        console.error('Error fetching bill details:', err);
        setError(err.response?.data?.error || 'Failed to load bill details');
      } finally {
        setLoading(false);
      }
    };

    fetchBillDetails();
  }, [billId]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMM d, yyyy');
  };

  // Handle bill deletion
  const handleDelete = async () => {
    if (!billId) return;

    try {
      setLoading(true);
      await deleteBill(billId);
      navigate('/bills');
    } catch (err: any) {
      console.error('Error deleting bill:', err);
      setError(err.response?.data?.error || 'Failed to delete bill');
      setLoading(false);
    }
  };

  // Handle payment form changes
  const handlePaymentFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setPaymentFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle payment submission
  const handlePaymentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!billId) return;

    try {
      setLoading(true);
      await addBillPayment(billId, paymentFormData);
      
      // Refresh bill data
      const updatedBill = await getBillById(billId);
      setBill(updatedBill);
      
      // Reset form and close modal
      setPaymentFormData({
        amount: 0,
        paymentDate: format(new Date(), 'yyyy-MM-dd'),
        paymentMethod: '',
        referenceNumber: '',
        notes: ''
      });
      setShowPaymentModal(false);
    } catch (err: any) {
      console.error('Error adding payment:', err);
      setError(err.response?.data?.error || 'Failed to add payment');
    } finally {
      setLoading(false);
    }
  };

  // Handle transaction link form changes
  const handleTransactionLinkFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setTransactionLinkFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle transaction link submission
  const handleTransactionLinkSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!billId) return;

    try {
      setLoading(true);
      await linkTransaction(billId, transactionLinkFormData);
      
      // Refresh bill data
      const updatedBill = await getBillById(billId);
      setBill(updatedBill);
      
      // Reset form and close modal
      setTransactionLinkFormData({
        transactionId: '',
        amount: 0,
        notes: ''
      });
      setShowLinkTransactionModal(false);
    } catch (err: any) {
      console.error('Error linking transaction:', err);
      setError(err.response?.data?.error || 'Failed to link transaction');
    } finally {
      setLoading(false);
    }
  };

  // Handle transaction unlink
  const handleUnlinkTransaction = async (linkId: string) => {
    if (!billId) return;

    try {
      setLoading(true);
      await unlinkTransaction(billId, linkId);
      
      // Refresh bill data
      const updatedBill = await getBillById(billId);
      setBill(updatedBill);
    } catch (err: any) {
      console.error('Error unlinking transaction:', err);
      setError(err.response?.data?.error || 'Failed to unlink transaction');
    } finally {
      setLoading(false);
    }
  };

  // Handle attachment delete
  const handleDeleteAttachment = async (attachmentId: string) => {
    if (!billId) return;

    try {
      setLoading(true);
      await deleteAttachment(billId, attachmentId);
      
      // Refresh bill data
      const updatedBill = await getBillById(billId);
      setBill(updatedBill);
    } catch (err: any) {
      console.error('Error deleting attachment:', err);
      setError(err.response?.data?.error || 'Failed to delete attachment');
    } finally {
      setLoading(false);
    }
  };

  // Calculate total paid amount
  const calculateTotalPaid = () => {
    if (!bill?.payments || bill.payments.length === 0) return 0;
    return bill.payments.reduce((sum, payment) => sum + payment.amount, 0);
  };

  // Calculate remaining amount
  const calculateRemainingAmount = () => {
    if (!bill) return 0;
    const totalPaid = calculateTotalPaid();
    return bill.amount - totalPaid;
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Bill Details</h1>
        <div className="flex space-x-2">
          <Link
            to="/bills"
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Bills
          </Link>
          <Link
            to={`/bills/${billId}/edit`}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Edit Bill
          </Link>
          <button
            type="button"
            onClick={() => setShowDeleteConfirm(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Delete
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading && !bill ? (
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-gray-500">Loading bill details...</p>
        </div>
      ) : bill ? (
        <div className="space-y-6">
          {/* Bill Overview */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">Bill Overview</h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">Details about the bill and its status.</p>
              </div>
              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                ${bill.status === 'paid' ? 'bg-green-100 text-green-800' :
                  bill.status === 'overdue' ? 'bg-red-100 text-red-800' :
                  bill.status === 'partial' ? 'bg-blue-100 text-blue-800' :
                  'bg-yellow-100 text-yellow-800'}`}>
                {bill.status.charAt(0).toUpperCase() + bill.status.slice(1)}
              </span>
            </div>
            <div className="border-t border-gray-200">
              <dl>
                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Title</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{bill.title}</dd>
                </div>
                <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Description</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{bill.description || 'N/A'}</dd>
                </div>
                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Category</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    {bill.category ? (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" 
                        style={{ backgroundColor: bill.category.color ? `${bill.category.color}20` : '#E5E7EB', color: bill.category.color || '#374151' }}>
                        {bill.category.name}
                      </span>
                    ) : 'Uncategorized'}
                  </dd>
                </div>
                <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Vendor</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{bill.vendor ? bill.vendor.name : 'N/A'}</dd>
                </div>
                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Amount</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{formatCurrency(bill.amount)}</dd>
                </div>
                <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Due Date</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{formatDate(bill.due_date)}</dd>
                </div>
                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Payment Method</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{bill.payment_method || 'N/A'}</dd>
                </div>
                <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Reference Number</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{bill.reference_number || 'N/A'}</dd>
                </div>
                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Notes</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{bill.notes || 'N/A'}</dd>
                </div>
                <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Recurring</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    {bill.is_recurring ? (
                      <div>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-2">
                          Yes
                        </span>
                        {bill.recurringSchedule && (
                          <span>
                            {bill.recurringSchedule.frequency.charAt(0).toUpperCase() + bill.recurringSchedule.frequency.slice(1)} - 
                            Next due: {formatDate(bill.recurringSchedule.next_due_date)}
                          </span>
                        )}
                      </div>
                    ) : 'No'}
                  </dd>
                </div>
                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Created By</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    {bill.creator ? `${bill.creator.first_name} ${bill.creator.last_name}` : 'N/A'}
                  </dd>
                </div>
                <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Created At</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{formatDate(bill.created_at)}</dd>
                </div>
              </dl>
            </div>
          </div>

          {/* Payment Summary */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">Payment Summary</h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">Overview of payments and remaining balance.</p>
              </div>
              <button
                type="button"
                onClick={() => {
                  setPaymentFormData({
                    ...paymentFormData,
                    amount: calculateRemainingAmount()
                  });
                  setShowPaymentModal(true);
                }}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                disabled={bill.status === 'paid'}
              >
                Add Payment
              </button>
            </div>
            <div className="border-t border-gray-200">
              <dl>
                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Total Amount</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{formatCurrency(bill.amount)}</dd>
                </div>
                <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Total Paid</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{formatCurrency(calculateTotalPaid())}</dd>
                </div>
                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Remaining Balance</dt>
                  <dd className="mt-1 text-sm font-bold text-gray-900 sm:mt-0 sm:col-span-2">{formatCurrency(calculateRemainingAmount())}</dd>
                </div>
              </dl>
            </div>
          </div>

          {/* Payments */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">Payment History</h3>
              <p className="mt-1 max-w-2xl text-sm text-gray-500">List of all payments made for this bill.</p>
            </div>
            <div className="border-t border-gray-200">
              {bill.payments && bill.payments.length > 0 ? (
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Method
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Reference
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Notes
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {bill.payments.map((payment) => (
                      <tr key={payment.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(payment.payment_date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {payment.payment_method || 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {payment.reference_number || 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {payment.notes || 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium">
                          {formatCurrency(payment.amount)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <div className="px-6 py-4 text-center text-sm text-gray-500">
                  No payments have been made for this bill yet.
                </div>
              )}
            </div>
          </div>

          {/* Linked Transactions */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">Linked Transactions</h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">Transactions linked to this bill.</p>
              </div>
              <button
                type="button"
                onClick={() => setShowLinkTransactionModal(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Link Transaction
              </button>
            </div>
            <div className="border-t border-gray-200">
              {bill.transactions && bill.transactions.length > 0 ? (
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Linked Amount
                      </th>
                      <th scope="col" className="relative px-6 py-3">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {bill.transactions.map((transaction) => (
                      <tr key={transaction.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(transaction.transaction.transaction_date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {transaction.transaction.description}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {transaction.transaction.transaction_type}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                          {formatCurrency(transaction.transaction.amount)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium">
                          {formatCurrency(transaction.amount)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => handleUnlinkTransaction(transaction.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            Unlink
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <div className="px-6 py-4 text-center text-sm text-gray-500">
                  No transactions have been linked to this bill yet.
                </div>
              )}
            </div>
          </div>

          {/* Attachments */}
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">Attachments</h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">Files attached to this bill.</p>
              </div>
              <Link
                to={`/bills/${billId}/attachments/upload`}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Upload Attachment
              </Link>
            </div>
            <div className="border-t border-gray-200">
              {bill.attachments && bill.attachments.length > 0 ? (
                <ul className="divide-y divide-gray-200">
                  {bill.attachments.map((attachment) => (
                    <li key={attachment.id} className="px-6 py-4 flex items-center justify-between">
                      <div className="flex items-center">
                        <svg className="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                        <div>
                          <p className="text-sm font-medium text-gray-900">{attachment.file_name}</p>
                          <p className="text-xs text-gray-500">
                            {attachment.file_type || 'Unknown type'} • 
                            {attachment.file_size ? ` ${Math.round(attachment.file_size / 1024)} KB • ` : ' '}
                            Uploaded {format(new Date(attachment.uploaded_at), 'MMM d, yyyy')}
                          </p>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <a
                          href={attachment.file_path}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-primary-600 hover:text-primary-900"
                        >
                          View
                        </a>
                        <button
                          onClick={() => handleDeleteAttachment(attachment.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          Delete
                        </button>
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="px-6 py-4 text-center text-sm text-gray-500">
                  No attachments have been uploaded for this bill yet.
                </div>
              )}
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <svg className="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Bill not found</h3>
          <p className="text-gray-500 mb-6">The bill you're looking for doesn't exist or you don't have permission to view it.</p>
          <Link
            to="/bills"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Bills
          </Link>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Delete Bill</h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to delete this bill? This action cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={handleDelete}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Delete
                </button>
                <button
                  type="button"
                  onClick={() => setShowDeleteConfirm(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Payment Modal */}
      {showPaymentModal && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <form onSubmit={handlePaymentSubmit}>
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">Add Payment</h3>
                      <div className="mt-4 space-y-4">
                        <div>
                          <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
                            Amount <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="number"
                            name="amount"
                            id="amount"
                            required
                            min="0.01"
                            step="0.01"
                            className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                            value={paymentFormData.amount}
                            onChange={handlePaymentFormChange}
                          />
                        </div>
                        <div>
                          <label htmlFor="paymentDate" className="block text-sm font-medium text-gray-700">
                            Payment Date <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="date"
                            name="paymentDate"
                            id="paymentDate"
                            required
                            className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                            value={paymentFormData.paymentDate}
                            onChange={handlePaymentFormChange}
                          />
                        </div>
                        <div>
                          <label htmlFor="paymentMethod" className="block text-sm font-medium text-gray-700">
                            Payment Method
                          </label>
                          <input
                            type="text"
                            name="paymentMethod"
                            id="paymentMethod"
                            className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                            value={paymentFormData.paymentMethod}
                            onChange={handlePaymentFormChange}
                            placeholder="e.g., Credit Card, Check, Cash"
                          />
                        </div>
                        <div>
                          <label htmlFor="referenceNumber" className="block text-sm font-medium text-gray-700">
                            Reference Number
                          </label>
                          <input
                            type="text"
                            name="referenceNumber"
                            id="referenceNumber"
                            className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                            value={paymentFormData.referenceNumber}
                            onChange={handlePaymentFormChange}
                            placeholder="e.g., Check #, Transaction ID"
                          />
                        </div>
                        <div>
                          <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                            Notes
                          </label>
                          <textarea
                            name="notes"
                            id="notes"
                            rows={3}
                            className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                            value={paymentFormData.notes}
                            onChange={handlePaymentFormChange}
                          ></textarea>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="submit"
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    Add Payment
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowPaymentModal(false)}
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Link Transaction Modal */}
      {showLinkTransactionModal && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <form onSubmit={handleTransactionLinkSubmit}>
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">Link Transaction</h3>
                      <div className="mt-4 space-y-4">
                        <div>
                          <label htmlFor="transactionId" className="block text-sm font-medium text-gray-700">
                            Transaction <span className="text-red-500">*</span>
                          </label>
                          <select
                            name="transactionId"
                            id="transactionId"
                            required
                            className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                            value={transactionLinkFormData.transactionId}
                            onChange={handleTransactionLinkFormChange}
                          >
                            <option value="">Select a transaction</option>
                            {transactions.map(transaction => (
                              <option key={transaction.id} value={transaction.id}>
                                {format(new Date(transaction.transaction_date), 'MMM d, yyyy')} - {transaction.description} ({formatCurrency(transaction.amount)})
                              </option>
                            ))}
                          </select>
                          {transactions.length === 0 && (
                            <p className="mt-2 text-sm text-red-500">
                              No transactions available for linking. Please ensure you have linked a financial account.
                            </p>
                          )}
                        </div>
                        <div>
                          <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
                            Amount to Link <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="number"
                            name="amount"
                            id="amount"
                            required
                            min="0.01"
                            step="0.01"
                            className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                            value={transactionLinkFormData.amount}
                            onChange={handleTransactionLinkFormChange}
                          />
                        </div>
                        <div>
                          <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                            Notes
                          </label>
                          <textarea
                            name="notes"
                            id="notes"
                            rows={3}
                            className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                            value={transactionLinkFormData.notes}
                            onChange={handleTransactionLinkFormChange}
                          ></textarea>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="submit"
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    Link Transaction
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowLinkTransactionModal(false)}
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default BillDetail;