import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useFarm } from '../../context/FarmContext';
import { toast } from 'react-hot-toast';
import { 
  createSeasonalWorker, 
  getSeasonalWorkers, 
  deleteSeasonalWorker 
} from '../../services/laborService';
import Layout from '../../components/Layout';
import { format } from 'date-fns';

const SeasonalWorkerManagement: React.FC = () => {
  const navigate = useNavigate();
  const { currentFarm } = useFarm();
  const [isLoading, setIsLoading] = useState(false);
  const [workers, setWorkers] = useState<any[]>([]);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    contactInfo: '',
    startDate: format(new Date(), 'yyyy-MM-dd'),
    endDate: format(new Date(new Date().setMonth(new Date().getMonth() + 3)), 'yyyy-MM-dd'),
    position: '',
    status: 'active',
    documentationStatus: 'pending',
    notes: ''
  });

  useEffect(() => {
    if (currentFarm?.id) {
      fetchWorkers();
    }
  }, [currentFarm]);

  const fetchWorkers = async () => {
    try {
      setIsLoading(true);
      const data = await getSeasonalWorkers(Number(currentFarm!.id));
      setWorkers(data);
    } catch (error) {
      console.error('Error fetching seasonal workers:', error);
      toast.error('Failed to fetch seasonal workers');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentFarm?.id) {
      toast.error('Please select a farm first');
      return;
    }

    try {
      setIsLoading(true);
      await createSeasonalWorker({
        farmId: Number(currentFarm.id),
        ...formData,
        status: formData.status as 'active' | 'inactive' | 'pending',
        documentationStatus: formData.documentationStatus as 'complete' | 'incomplete' | 'pending'
      });

      toast.success('Seasonal worker added successfully');
      setFormData({
        firstName: '',
        lastName: '',
        contactInfo: '',
        startDate: format(new Date(), 'yyyy-MM-dd'),
        endDate: format(new Date(new Date().setMonth(new Date().getMonth() + 3)), 'yyyy-MM-dd'),
        position: '',
        status: 'active',
        documentationStatus: 'pending',
        notes: ''
      });
      fetchWorkers();
    } catch (error) {
      console.error('Error creating seasonal worker:', error);
      toast.error('Failed to add seasonal worker');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this seasonal worker?')) {
      try {
        setIsLoading(true);
        await deleteSeasonalWorker(id);
        toast.success('Seasonal worker deleted successfully');
        fetchWorkers();
      } catch (error) {
        console.error('Error deleting seasonal worker:', error);
        toast.error('Failed to delete seasonal worker');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const getStatusLabel = (status: string) => {
    const statuses: Record<string, string> = {
      'active': 'Active',
      'inactive': 'Inactive',
      'pending': 'Pending'
    };
    return statuses[status] || status;
  };

  const getDocumentationStatusLabel = (status: string) => {
    const statuses: Record<string, string> = {
      'complete': 'Complete',
      'incomplete': 'Incomplete',
      'pending': 'Pending'
    };
    return statuses[status] || status;
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'active': 'bg-green-100 text-green-800',
      'inactive': 'bg-red-100 text-red-800',
      'pending': 'bg-yellow-100 text-yellow-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getDocumentationStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'complete': 'bg-green-100 text-green-800',
      'incomplete': 'bg-red-100 text-red-800',
      'pending': 'bg-yellow-100 text-yellow-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Seasonal Worker Management</h1>
        <button
          onClick={() => navigate('/labor')}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
        >
          Back to Dashboard
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Add Seasonal Worker</h2>
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                  First Name
                </label>
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>
              <div>
                <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                  Last Name
                </label>
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>
            </div>

            <div className="mb-4">
              <label htmlFor="contactInfo" className="block text-sm font-medium text-gray-700 mb-1">
                Contact Information
              </label>
              <input
                type="text"
                id="contactInfo"
                name="contactInfo"
                value={formData.contactInfo}
                onChange={handleInputChange}
                placeholder="Phone number or email"
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Start Date
                </label>
                <input
                  type="date"
                  id="startDate"
                  name="startDate"
                  value={formData.startDate}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>
              <div>
                <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
                  End Date (Optional)
                </label>
                <input
                  type="date"
                  id="endDate"
                  name="endDate"
                  value={formData.endDate}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>

            <div className="mb-4">
              <label htmlFor="position" className="block text-sm font-medium text-gray-700 mb-1">
                Position
              </label>
              <input
                type="text"
                id="position"
                name="position"
                value={formData.position}
                onChange={handleInputChange}
                placeholder="e.g., Harvester, Field Worker, etc."
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="pending">Pending</option>
                </select>
              </div>
              <div>
                <label htmlFor="documentationStatus" className="block text-sm font-medium text-gray-700 mb-1">
                  Documentation Status
                </label>
                <select
                  id="documentationStatus"
                  name="documentationStatus"
                  value={formData.documentationStatus}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                >
                  <option value="complete">Complete</option>
                  <option value="incomplete">Incomplete</option>
                  <option value="pending">Pending</option>
                </select>
              </div>
            </div>

            <div className="mb-4">
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={3}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-400"
            >
              {isLoading ? 'Adding...' : 'Add Seasonal Worker'}
            </button>
          </form>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Seasonal Workers</h2>
          {isLoading && <p className="text-gray-500">Loading...</p>}

          {!isLoading && workers.length === 0 && (
            <p className="text-gray-500">No seasonal workers found.</p>
          )}

          {!isLoading && workers.length > 0 && (
            <div className="space-y-4">
              {workers.map((worker) => (
                <div key={worker.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{worker.first_name} {worker.last_name}</h3>
                      <p className="text-sm text-gray-500 mb-2">
                        {worker.position} • {worker.contact_info}
                      </p>
                      <div className="flex space-x-2 mb-2">
                        <span className={`inline-block px-2 py-1 rounded text-xs font-medium ${getStatusColor(worker.status)}`}>
                          {getStatusLabel(worker.status)}
                        </span>
                        <span className={`inline-block px-2 py-1 rounded text-xs font-medium ${getDocumentationStatusColor(worker.documentation_status)}`}>
                          Docs: {getDocumentationStatusLabel(worker.documentation_status)}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">Period:</span> {new Date(worker.start_date).toLocaleDateString()} 
                        {worker.end_date && ` - ${new Date(worker.end_date).toLocaleDateString()}`}
                      </p>
                    </div>
                    <button
                      onClick={() => handleDelete(worker.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      Delete
                    </button>
                  </div>
                  {worker.notes && (
                    <div className="mt-2">
                      <p className="text-sm font-medium text-gray-700">Notes:</p>
                      <p className="text-sm text-gray-600">{worker.notes}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default SeasonalWorkerManagement;
