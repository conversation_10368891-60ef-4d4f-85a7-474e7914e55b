import { Routes, Route } from 'react-router-dom';
import LaborDashboard from './LaborDashboard';
import SeasonalWorkerManagement from './SeasonalWorkerManagement';
import LaborCostAnalysis from './LaborCostAnalysis';
import ComplianceTracking from './ComplianceTracking';
import WorkerCertificationTracking from './WorkerCertificationTracking';

const LaborRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={<LaborDashboard />} />
      <Route path="/seasonal-workers/*" element={<SeasonalWorkerManagement />} />
      <Route path="/cost-analysis/*" element={<LaborCostAnalysis />} />
      <Route path="/compliance/*" element={<ComplianceTracking />} />
      <Route path="/certifications/*" element={<WorkerCertificationTracking />} />
    </Routes>
  );
};

export default LaborRoutes;
