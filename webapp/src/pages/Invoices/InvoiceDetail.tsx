import { useState } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import Layout from '../../components/Layout';

const InvoiceDetail = () => {
  const { invoiceId } = useParams<{ invoiceId: string }>();
  const navigate = useNavigate();
  
  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Invoice Details</h1>
        <div className="flex space-x-2">
          <Link
            to="/invoices"
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Invoices
          </Link>
          <Link
            to={`/invoices/${invoiceId}/edit`}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Edit Invoice
          </Link>
        </div>
      </div>
      
      <div className="bg-white shadow rounded-lg p-8 text-center">
        <p className="text-gray-500 mb-4">This feature is coming soon!</p>
        <p className="text-sm text-gray-400">
          View invoice details, line items, payment status, and customer information.
        </p>
      </div>
    </Layout>
  );
};

export default InvoiceDetail;