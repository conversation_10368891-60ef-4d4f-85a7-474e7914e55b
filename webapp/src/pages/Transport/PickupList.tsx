import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { getPickups, deletePickup, formatDate, Pickup } from '../../services/pickupService';

// Using Pickup interface from pickupService.ts

const PickupList = () => {
  const [pickups, setPickups] = useState<Pickup[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  // Fetch pickups for the selected farm
  useEffect(() => {
    const fetchPickups = async () => {
      if (!currentFarm) return;

      setLoading(true);
      setError(null);

      try {
        const pickupsData = await getPickups(currentFarm.id);
        setPickups(pickupsData);
      } catch (err: any) {
        console.error('Error fetching pickups:', err);
        setError('Failed to load pickups. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchPickups();
  }, [currentFarm]);

  // Handle pickup deletion
  const handleDeletePickup = async (pickupId: string) => {
    if (!window.confirm('Are you sure you want to delete this pickup?')) {
      return;
    }

    try {
      await deletePickup(pickupId);
      setPickups(pickups.filter(pickup => pickup.id !== pickupId));
    } catch (err: any) {
      console.error('Error deleting pickup:', err);
      setError('Failed to delete pickup. Please try again later.');
    }
  };

  // Using formatDate from pickupService.ts

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Pickup Management</h1>
        <div>
          <Link
            to="/transport/pickups/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Create Pickup
          </Link>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading pickups...</p>
        </div>
      ) : pickups.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <p className="text-gray-500 mb-4">No pickups found for this farm.</p>
          <p className="text-sm text-gray-400 mb-6">
            Create your first pickup to start tracking item collections.
          </p>
          <Link
            to="/transport/pickups/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Create Pickup
          </Link>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {pickups.map((pickup) => (
              <li key={pickup.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="ml-3">
                        <p className="text-sm font-medium text-primary-600 truncate">
                          Pickup from {pickup.location}
                        </p>
                        <p className="text-sm text-gray-500">
                          {pickup.referenceNumber && <span className="mr-2">Ref: {pickup.referenceNumber}</span>}
                          {pickup.contactName && <span className="mr-2">Contact: {pickup.contactName}</span>}
                        </p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Link
                        to={`/transport/pickups/${pickup.id}`}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        View
                      </Link>
                      <Link
                        to={`/transport/pickups/${pickup.id}/edit`}
                        className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Edit
                      </Link>
                      <button
                        onClick={() => handleDeletePickup(pickup.id)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                  <div className="mt-2 sm:flex sm:justify-between">
                    <div className="sm:flex">
                      {pickup.pickupDate && (
                        <p className="flex items-center text-sm text-gray-500 mr-6">
                          <span>Pickup Date: {formatDate(pickup.pickupDate)}</span>
                        </p>
                      )}
                      {pickup.estimatedCompletion && (
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 mr-6">
                          <span>ETA: {formatDate(pickup.estimatedCompletion)}</span>
                        </p>
                      )}
                      {pickup.driverName && (
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 mr-6">
                          <span>Driver: {pickup.driverName}</span>
                        </p>
                      )}
                    </div>
                    {pickup.status && (
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          pickup.status === 'scheduled' ? 'bg-blue-100 text-blue-800' : 
                          pickup.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' : 
                          pickup.status === 'completed' ? 'bg-green-100 text-green-800' : 
                          pickup.status === 'cancelled' ? 'bg-red-100 text-red-800' : 
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {pickup.status === 'scheduled' ? 'Scheduled' : 
                           pickup.status === 'in_progress' ? 'In Progress' : 
                           pickup.status === 'completed' ? 'Completed' : 
                           pickup.status === 'cancelled' ? 'Cancelled' : 
                           pickup.status}
                        </span>
                      </div>
                    )}
                  </div>
                  {pickup.items && pickup.items.length > 0 && (
                    <div className="mt-2 text-sm text-gray-500">
                      <p>Items: {pickup.items.map(item => `${item.name} (${item.quantity} ${item.unit})`).join(', ')}</p>
                    </div>
                  )}
                  {pickup.notes && (
                    <div className="mt-2 text-sm text-gray-500">
                      <p>Notes: {pickup.notes}</p>
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </Layout>
  );
};

export default PickupList;
