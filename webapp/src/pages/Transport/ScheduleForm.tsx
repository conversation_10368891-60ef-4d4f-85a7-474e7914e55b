import { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { getDriverScheduleById, createDriverSchedule, updateDriverSchedule } from '../../services/driverScheduleService';
import { getDrivers, Driver } from '../../services/driverService';

// Using Driver interface from driverService.ts

interface Stop {
  id: string;
  type: string;
  locationName: string;
  address: string;
  scheduledTime: string;
  notes: string;
}

interface ScheduleFormData {
  driverId: string;
  startDate: string;
  endDate: string;
  status: string;
  title: string;
  description: string;
  routeDetails: string;
  stops: Stop[];
}

const ScheduleForm = () => {
  const { scheduleId } = useParams<{ scheduleId: string }>();
  const navigate = useNavigate();
  const isEditMode = !!scheduleId;

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  const [formData, setFormData] = useState<ScheduleFormData>({
    driverId: '',
    startDate: '',
    endDate: '',
    status: 'scheduled',
    title: '',
    description: '',
    routeDetails: '',
    stops: []
  });

  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Fetch drivers for the selected farm
  useEffect(() => {
    const fetchDrivers = async () => {
      if (!currentFarm) return;

      try {
        const driversData = await getDrivers(currentFarm.id);
        setDrivers(driversData);
      } catch (err: any) {
        console.error('Error fetching drivers:', err);

        // Check if the error has structured error information
        if (err.structuredError) {
          setError(err.structuredError.message);
        } else {
          setError('Failed to load drivers. Please try again later.');
        }
      }
    };

    fetchDrivers();
  }, [currentFarm]);

  // Fetch schedule data if in edit mode
  useEffect(() => {
    if (isEditMode && scheduleId) {
      const fetchSchedule = async () => {
        setLoading(true);
        setError(null);

        try {
          const scheduleData = await getDriverScheduleById(scheduleId);

          setFormData({
            driverId: scheduleData.driverId || '',
            startDate: scheduleData.startDate || '',
            endDate: scheduleData.endDate || '',
            status: scheduleData.status || 'scheduled',
            title: scheduleData.title || '',
            description: scheduleData.description || '',
            routeDetails: scheduleData.routeDetails || '',
            stops: scheduleData.stops || []
          });
        } catch (err: any) {
          console.error('Error fetching schedule:', err);

          // Check if the error has structured error information
          if (err.structuredError) {
            setError(err.structuredError.message);
          } else {
            setError('Failed to load schedule data. Please try again later.');
          }
        } finally {
          setLoading(false);
        }
      };

      fetchSchedule();
    }
  }, [isEditMode, scheduleId]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle stop addition
  const handleAddStop = () => {
    const newStop: Stop = {
      id: '',
      type: 'pickup',
      locationName: '',
      address: '',
      scheduledTime: '',
      notes: ''
    };

    setFormData(prev => ({
      ...prev,
      stops: [...prev.stops, newStop]
    }));
  };

  // Handle stop removal
  const handleRemoveStop = (index: number) => {
    setFormData(prev => ({
      ...prev,
      stops: prev.stops.filter((_, i) => i !== index)
    }));
  };

  // Handle stop field changes
  const handleStopChange = (index: number, field: string, value: string) => {
    setFormData(prev => {
      const updatedStops = [...prev.stops];
      updatedStops[index] = {
        ...updatedStops[index],
        [field]: value
      };
      return { ...prev, stops: updatedStops };
    });
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentFarm) {
      setError('No farm selected. Please select a farm before creating a schedule.');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      // Map form data to the expected format for the API
      const mappedData = {
        driverId: formData.driverId,
        startTime: formData.startDate, // Map startDate to startTime
        endTime: formData.endDate,     // Map endDate to endTime
        status: formData.status,
        notes: `${formData.title}\n\n${formData.description}\n\nRoute: ${formData.routeDetails}\n\nStops: ${
          formData.stops.map(stop => 
            `${stop.type} at ${stop.locationName} (${stop.scheduledTime})`
          ).join(', ')
        }`,
        farmId: currentFarm.id
      };

      if (isEditMode && scheduleId) {
        await updateDriverSchedule(scheduleId, mappedData);
        setSuccessMessage('Schedule updated successfully!');
      } else {
        await createDriverSchedule(mappedData);
        setSuccessMessage('Schedule created successfully!');
      }

      // Redirect after a short delay
      setTimeout(() => {
        navigate('/transport/schedules');
      }, 1500);
    } catch (err: any) {
      console.error('Error saving schedule:', err);

      // Check if the error has structured error information
      if (err.structuredError) {
        setError(err.structuredError.message);
      } else {
        setError('Failed to save schedule. Please try again later.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Sort stops by scheduled time
  const sortStops = () => {
    setFormData(prev => {
      const sortedStops = [...prev.stops].sort((a, b) => {
        if (!a.scheduledTime) return 1;
        if (!b.scheduledTime) return -1;
        return new Date(a.scheduledTime).getTime() - new Date(b.scheduledTime).getTime();
      });
      return { ...prev, stops: sortedStops };
    });
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit Schedule' : 'Create New Schedule'}
        </h1>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {successMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{successMessage}</span>
        </div>
      )}

      <div className="bg-white shadow rounded-lg p-6">
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                Schedule Title
              </label>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label htmlFor="driverId" className="block text-sm font-medium text-gray-700 mb-1">
                Driver
              </label>
              <select
                id="driverId"
                name="driverId"
                value={formData.driverId}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                required
              >
                <option value="">Select a driver</option>
                {drivers.map(driver => (
                  <option key={driver.id} value={driver.id}>
                    {driver.firstName} {driver.lastName}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
                Start Date
              </label>
              <input
                type="date"
                id="startDate"
                name="startDate"
                value={formData.startDate}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
                End Date
              </label>
              <input
                type="date"
                id="endDate"
                name="endDate"
                value={formData.endDate}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                required
              >
                <option value="scheduled">Scheduled</option>
                <option value="in_progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
          </div>

          <div className="mb-6">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              rows={3}
              value={formData.description}
              onChange={handleInputChange}
              className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
            />
          </div>

          <div className="mb-6">
            <label htmlFor="routeDetails" className="block text-sm font-medium text-gray-700 mb-1">
              Route Details
            </label>
            <textarea
              id="routeDetails"
              name="routeDetails"
              rows={2}
              value={formData.routeDetails}
              onChange={handleInputChange}
              className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
            />
          </div>

          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <label className="block text-sm font-medium text-gray-700">Stops</label>
              <div className="flex space-x-2">
                <button
                  type="button"
                  onClick={sortStops}
                  className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Sort by Time
                </button>
                <button
                  type="button"
                  onClick={handleAddStop}
                  className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Add Stop
                </button>
              </div>
            </div>

            {formData.stops.length === 0 ? (
              <p className="text-sm text-gray-500">No stops added yet.</p>
            ) : (
              <div className="space-y-4">
                {formData.stops.map((stop, index) => (
                  <div key={index} className="border border-gray-200 rounded-md p-4">
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="text-sm font-medium text-gray-900">Stop #{index + 1}</h4>
                      <button
                        type="button"
                        onClick={() => handleRemoveStop(index)}
                        className="inline-flex items-center p-1 border border-transparent rounded-full text-red-600 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 000 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor={`stop-type-${index}`} className="block text-xs font-medium text-gray-700 mb-1">
                          Type
                        </label>
                        <select
                          id={`stop-type-${index}`}
                          value={stop.type}
                          onChange={(e) => handleStopChange(index, 'type', e.target.value)}
                          className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-xs border-gray-300 rounded-md"
                        >
                          <option value="pickup">Pickup</option>
                          <option value="delivery">Delivery</option>
                          <option value="rest">Rest Stop</option>
                          <option value="other">Other</option>
                        </select>
                      </div>

                      <div>
                        <label htmlFor={`stop-time-${index}`} className="block text-xs font-medium text-gray-700 mb-1">
                          Scheduled Time
                        </label>
                        <input
                          type="datetime-local"
                          id={`stop-time-${index}`}
                          value={stop.scheduledTime}
                          onChange={(e) => handleStopChange(index, 'scheduledTime', e.target.value)}
                          className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-xs border-gray-300 rounded-md"
                        />
                      </div>

                      <div>
                        <label htmlFor={`stop-location-${index}`} className="block text-xs font-medium text-gray-700 mb-1">
                          Location Name
                        </label>
                        <input
                          type="text"
                          id={`stop-location-${index}`}
                          value={stop.locationName}
                          onChange={(e) => handleStopChange(index, 'locationName', e.target.value)}
                          className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-xs border-gray-300 rounded-md"
                          required
                        />
                      </div>

                      <div>
                        <label htmlFor={`stop-address-${index}`} className="block text-xs font-medium text-gray-700 mb-1">
                          Address
                        </label>
                        <input
                          type="text"
                          id={`stop-address-${index}`}
                          value={stop.address}
                          onChange={(e) => handleStopChange(index, 'address', e.target.value)}
                          className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-xs border-gray-300 rounded-md"
                        />
                      </div>

                      <div className="md:col-span-2">
                        <label htmlFor={`stop-notes-${index}`} className="block text-xs font-medium text-gray-700 mb-1">
                          Notes
                        </label>
                        <textarea
                          id={`stop-notes-${index}`}
                          value={stop.notes}
                          onChange={(e) => handleStopChange(index, 'notes', e.target.value)}
                          rows={2}
                          className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-xs border-gray-300 rounded-md"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={() => navigate('/transport/schedules')}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {loading ? 'Saving...' : isEditMode ? 'Update Schedule' : 'Create Schedule'}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default ScheduleForm;
