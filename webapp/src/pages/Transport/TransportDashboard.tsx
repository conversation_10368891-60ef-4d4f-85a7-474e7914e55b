import { useContext } from 'react';
import { Link } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';

const TransportDashboard = () => {
  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  // Dashboard cards for different transport management features
  const dashboardCards = [
    {
      title: 'Drivers',
      description: 'Manage your drivers, their licenses, and vehicle information.',
      icon: '🚚',
      link: '/transport/drivers',
      color: 'bg-blue-100',
      textColor: 'text-blue-800',
      borderColor: 'border-blue-200'
    },
    {
      title: 'Deliveries',
      description: 'Track product deliveries to customers and other destinations.',
      icon: '📦',
      link: '/transport/deliveries',
      color: 'bg-green-100',
      textColor: 'text-green-800',
      borderColor: 'border-green-200'
    },
    {
      title: 'Pickups',
      description: 'Manage pickups from suppliers, customers, and other sources.',
      icon: '🔄',
      link: '/transport/pickups',
      color: 'bg-purple-100',
      textColor: 'text-purple-800',
      borderColor: 'border-purple-200'
    },
    {
      title: 'Driver Schedules',
      description: 'Schedule and manage driver assignments and routes.',
      icon: '📅',
      link: '/transport/schedules',
      color: 'bg-yellow-100',
      textColor: 'text-yellow-800',
      borderColor: 'border-yellow-200'
    },
    {
      title: 'Driver Locations',
      description: 'View real-time locations of your drivers on a map.',
      icon: '🗺️',
      link: '/transport/locations',
      color: 'bg-red-100',
      textColor: 'text-red-800',
      borderColor: 'border-red-200'
    }
  ];

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Transport Management</h1>
      </div>

      <div className="bg-white shadow rounded-lg p-6 mb-8">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Transport Overview</h2>
        <p className="text-gray-600 mb-4">
          Manage your farm's transportation needs, including drivers, deliveries, pickups, and scheduling.
          Keep track of driver locations, delivery statuses, and upcoming pickups to ensure your farm stays on schedule.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {dashboardCards.map((card, index) => (
          <Link 
            key={index} 
            to={card.link} 
            className={`block p-6 rounded-lg shadow-sm border ${card.borderColor} ${card.color} hover:shadow-md transition-shadow duration-200`}
          >
            <div className="flex items-center mb-3">
              <span className="text-3xl mr-3">{card.icon}</span>
              <h3 className={`text-lg font-medium ${card.textColor}`}>{card.title}</h3>
            </div>
            <p className="text-gray-600">{card.description}</p>
          </Link>
        ))}
      </div>

      <div className="mt-8 bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link 
            to="/transport/drivers/new" 
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add Driver
          </Link>
          <Link 
            to="/transport/deliveries/new" 
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            Create Delivery
          </Link>
          <Link 
            to="/transport/pickups/new" 
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            Create Pickup
          </Link>
          <Link 
            to="/transport/schedules/new" 
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
          >
            Schedule Driver
          </Link>
        </div>
      </div>
    </Layout>
  );
};

export default TransportDashboard;