import { useState, useEffect, useContext, useRef } from 'react';
import Layout from '../../components/Layout';
import { AuthContext } from '../../context/AuthContext';
import { useMenuPreferences } from '../../hooks/useMenuPreferences';
import { 
  getUserMenuPreferences,
  saveUserMenuPreferences,
  resetUserMenuPreferences
} from '../../services/menuPreferencesService';
import { MenuItem, MenuCategory, MenuPreferences } from '../../utils/menuUtils';
import SimpleHelpTip from '../../components/help/SimpleHelpTip';

// CSS for drag and drop
const dragStyles = `
  .dragging {
    opacity: 0.5;
    border: 2px dashed #ccc;
  }

  .drag-over {
    border: 2px dashed #4f46e5;
    background-color: rgba(79, 70, 229, 0.05);
  }
`;

const MenuCustomization = () => {
  const { user } = useContext(AuthContext);
  const { headerItems, sidebarCategories, quickLinksItems, loading, error, refreshPreferences, reorderSidebarCategories, isFeatureEnabled } = useMenuPreferences();
  const [localHeaderItems, setLocalHeaderItems] = useState<MenuItem[]>([]);
  const [localSidebarCategories, setLocalSidebarCategories] = useState<MenuCategory[]>([]);
  const [localQuickLinksItems, setLocalQuickLinksItems] = useState<MenuItem[]>([]);
  const [localLoading, setLocalLoading] = useState<boolean>(false);
  const [localError, setLocalError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'header' | 'sidebar' | 'quicklinks'>('header');
  const [draggedCategoryId, setDraggedCategoryId] = useState<string | null>(null);
  const [selectedPage, setSelectedPage] = useState<MenuItem | null>(null);

  // Initialize local state from context
  useEffect(() => {
    if (!loading) {
      // Only include visible header items
      setLocalHeaderItems([...headerItems].filter(item => item.isVisible));

      // Only include visible sidebar categories and items
      const visibleCategories = [...sidebarCategories].map(category => ({
        ...category,
        items: category.items.filter(item => item.isVisible)
      })).filter(category => category.items.length > 0);

      setLocalSidebarCategories(visibleCategories);

      // Only include visible quick links items
      setLocalQuickLinksItems([...quickLinksItems].filter(item => item.isVisible));
    }
  }, [headerItems, sidebarCategories, quickLinksItems, loading]);

  // Toggle visibility of a header item
  const toggleHeaderItemVisibility = (itemId: string) => {
    setLocalHeaderItems(prevItems => {
      // Get the current item
      const currentItem = prevItems.find(item => item.id === itemId);

      // If the item is required or doesn't exist, don't change anything
      if (!currentItem || currentItem.isRequired) {
        return prevItems;
      }

      // Count how many items are currently visible
      const visibleItemsCount = prevItems.filter(item => item.isVisible).length;

      // If trying to enable an item and we already have 5 visible items, show an error
      if (!currentItem.isVisible && visibleItemsCount >= 5) {
        setLocalError('You can have a maximum of 5 header menu items. Please disable another item first.');
        return prevItems;
      }

      // Clear any previous error
      if (localError) {
        setLocalError(null);
      }

      // Toggle the visibility
      return prevItems.map(item =>
        item.id === itemId ? { ...item, isVisible: !item.isVisible } : item
      );
    });
  };

  // Toggle visibility of a sidebar item
  const toggleSidebarItemVisibility = (categoryId: string, itemId: string) => {
    setLocalSidebarCategories(prevCategories =>
      prevCategories.map(category =>
        category.id === categoryId
          ? {
              ...category,
              items: category.items.map(item =>
                item.id === itemId && !item.isRequired
                  ? { ...item, isVisible: !item.isVisible }
                  : item
              )
            }
          : category
      )
    );
  };

  // Toggle visibility of a quick links item
  const toggleQuickLinksItemVisibility = (itemId: string) => {
    setLocalQuickLinksItems(prevItems =>
      prevItems.map(item =>
        item.id === itemId
          ? { ...item, isVisible: !item.isVisible }
          : item
      )
    );
  };

  // Add a new page to quick links
  const addPageToQuickLinks = (page: MenuItem) => {
    // Create a new quick link item with a unique ID
    const newQuickLinkItem: MenuItem = {
      id: `${page.id}-quick`,
      title: page.title,
      path: page.path,
      category: 'quick-links',
      isRequired: false,
      isVisible: true
    };

    // Check if this page is already in quick links
    const existingItem = localQuickLinksItems.find(item => item.path === page.path);
    if (existingItem) {
      // If it exists but is hidden, make it visible
      if (!existingItem.isVisible) {
        toggleQuickLinksItemVisibility(existingItem.id);
      }
      return;
    }

    // Add the new item to the quick links
    setLocalQuickLinksItems(prevItems => [...prevItems, newQuickLinkItem]);
  };

  // Save menu preferences
  const saveMenuPreferences = async () => {
    if (!user) return;

    setLocalLoading(true);
    setLocalError(null);
    setSuccess(null);

    try {
      // Send the user's preferences to the backend
      const preferences: MenuPreferences = {
        userId: user.id,
        headerItems: localHeaderItems,
        sidebarCategories: localSidebarCategories,
        quickLinksItems: localQuickLinksItems
      };
      await saveUserMenuPreferences(preferences);

      // Update the context with the reordered sidebar categories
      reorderSidebarCategories(localSidebarCategories);

      // Refresh the context with the new preferences
      await refreshPreferences();

      setSuccess('Menu preferences saved successfully.');
    } catch (err: any) {
      console.error('Error saving menu preferences:', err);
      setLocalError('Failed to save menu preferences. Please try again later.');
    } finally {
      setLocalLoading(false);
    }
  };

  // Reset menu preferences to defaults
  const resetMenuPreferences = async () => {
    if (!user) return;

    setLocalLoading(true);
    setLocalError(null);
    setSuccess(null);

    try {
      // Reset the user's preferences in the backend
      await resetUserMenuPreferences(user.id);

      // Refresh preferences from the context
      await refreshPreferences();

      // Re-initialize local state from the refreshed context
      // This ensures we only show installed plugins
      setLocalHeaderItems([...headerItems].filter(item => item.isVisible));

      const visibleCategories = [...sidebarCategories].map(category => ({
        ...category,
        items: category.items.filter(item => item.isVisible)
      })).filter(category => category.items.length > 0);

      setLocalSidebarCategories(visibleCategories);

      setLocalQuickLinksItems([...quickLinksItems].filter(item => item.isVisible));

      setSuccess('Menu preferences reset to defaults.');
    } catch (err: any) {
      console.error('Error resetting menu preferences:', err);
      setLocalError('Failed to reset menu preferences. Please try again later.');
    } finally {
      setLocalLoading(false);
    }
  };

  // Drag and drop handlers for sidebar categories
  const handleDragStart = (e: React.DragEvent<HTMLDivElement>, categoryId: string) => {
    setDraggedCategoryId(categoryId);
    e.dataTransfer.effectAllowed = 'move';
    // Add a custom class to the dragged element
    e.currentTarget.classList.add('dragging');
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.currentTarget.classList.add('drag-over');
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.currentTarget.classList.remove('drag-over');
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>, targetCategoryId: string) => {
    e.preventDefault();
    e.currentTarget.classList.remove('drag-over');

    if (draggedCategoryId && draggedCategoryId !== targetCategoryId) {
      const draggedIndex = localSidebarCategories.findIndex(category => category.id === draggedCategoryId);
      const targetIndex = localSidebarCategories.findIndex(category => category.id === targetCategoryId);

      if (draggedIndex !== -1 && targetIndex !== -1) {
        // Create a new array with the reordered categories
        const newCategories = [...localSidebarCategories];
        const [draggedCategory] = newCategories.splice(draggedIndex, 1);
        newCategories.splice(targetIndex, 0, draggedCategory);

        // Update local state
        setLocalSidebarCategories(newCategories);

        // Update context (this will be saved when the user clicks "Save Preferences")
        reorderSidebarCategories(newCategories);
      }
    }

    setDraggedCategoryId(null);
  };

  const handleDragEnd = (e: React.DragEvent<HTMLDivElement>) => {
    e.currentTarget.classList.remove('dragging');
    setDraggedCategoryId(null);
  };

  return (
    <Layout>
      {/* Add drag and drop styles */}
      <style dangerouslySetInnerHTML={{ __html: dragStyles }} />

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Menu Customization</h1>
      </div>

      {(error || localError) && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error || localError}</span>
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{success}</span>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('header')}
            className={`${
              activeTab === 'header'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
          >
            Header Menu
          </button>
          <button
            onClick={() => setActiveTab('sidebar')}
            className={`${
              activeTab === 'sidebar'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
          >
            Sidebar Menu
          </button>
          <button
            onClick={() => setActiveTab('quicklinks')}
            className={`${
              activeTab === 'quicklinks'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
          >
            Quick Links Menu
          </button>
        </nav>
      </div>

      {loading || localLoading ? (
        <div className="flex justify-center items-center h-64">
          <p className="text-gray-500">Loading menu preferences...</p>
        </div>
      ) : (
        <>
          {/* Header Menu Tab */}
          {activeTab === 'header' && (
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-4 py-5 sm:p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Customize Header Menu</h2>
                <p className="text-sm text-gray-500 mb-4">
                  Select which items you want to show in the header menu. Note that some items cannot be hidden.
                </p>

                <div className="space-y-4">
                  {localHeaderItems.map(item => {
                    const featureEnabled = isFeatureEnabled(item.id);
                    return (
                      <div key={item.id} className="flex items-center justify-between py-2 border-b border-gray-200">
                        <div className="flex items-center">
                          <span className={`text-sm font-medium ${featureEnabled ? 'text-gray-900' : 'text-gray-400'}`}>
                            {item.title}
                          </span>
                          {item.isRequired && (
                            <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                              Required
                            </span>
                          )}
                          {!featureEnabled && (
                            <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                              Unavailable
                            </span>
                          )}
                        </div>
                        <div className="flex items-center">
                          {!featureEnabled && (
                            <div className="mr-2">
                              <SimpleHelpTip
                                content="This feature is not available in your current subscription plan. Please upgrade your plan to access this feature."
                                position="top"
                              />
                            </div>
                          )}
                          <label className={`inline-flex items-center ${featureEnabled ? 'cursor-pointer' : 'cursor-not-allowed'}`}>
                            <input
                              type="checkbox"
                              className="sr-only peer"
                              checked={item.isVisible}
                              onChange={() => toggleHeaderItemVisibility(item.id)}
                              disabled={item.isRequired || !featureEnabled}
                            />
                            <div className={`relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer ${item.isVisible ? 'peer-checked:bg-primary-600' : ''} ${item.isRequired || !featureEnabled ? 'opacity-50' : ''}`}>
                              <div className={`absolute top-[2px] left-[2px] bg-white border-gray-300 border rounded-full h-5 w-5 transition-all ${item.isVisible ? 'translate-x-5' : ''}`}></div>
                            </div>
                            <span className="ml-3 text-sm font-medium text-gray-900">
                              {item.isVisible ? 'Visible' : 'Hidden'}
                            </span>
                          </label>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {/* Sidebar Menu Tab */}
          {activeTab === 'sidebar' && (
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-4 py-5 sm:p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Customize Sidebar Menu</h2>
                <p className="text-sm text-gray-500 mb-4">
                  Select which items you want to show in the sidebar menu. Note that some items cannot be hidden.
                </p>

                <div className="space-y-6">
                  <p className="text-sm text-gray-500 mb-2">
                    Drag and drop categories to reorder them. You can also toggle the visibility of individual items.
                  </p>
                  {localSidebarCategories.map(category => (
                    <div 
                      key={category.id} 
                      className="border border-gray-200 rounded-md overflow-hidden cursor-move"
                      draggable
                      onDragStart={(e) => handleDragStart(e, category.id)}
                      onDragOver={handleDragOver}
                      onDragEnter={handleDragEnter}
                      onDragLeave={handleDragLeave}
                      onDrop={(e) => handleDrop(e, category.id)}
                      onDragEnd={handleDragEnd}
                    >
                      <div className="bg-gray-50 px-4 py-2 border-b border-gray-200 flex items-center">
                        <svg className="h-4 w-4 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8h16M4 16h16" />
                        </svg>
                        <h3 className="text-sm font-medium text-gray-900">{category.title}</h3>
                      </div>
                      <div className="px-4 py-3">
                        <div className="space-y-3">
                          {category.items.map(item => {
                            const featureEnabled = isFeatureEnabled(item.id);
                            return (
                              <div key={item.id} className="flex items-center justify-between py-2 border-b border-gray-100">
                                <div className="flex items-center">
                                  <span className={`text-sm font-medium ${featureEnabled ? 'text-gray-900' : 'text-gray-400'}`}>
                                    {item.title}
                                  </span>
                                  {item.isRequired && (
                                    <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                      Required
                                    </span>
                                  )}
                                  {!featureEnabled && (
                                    <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                      Unavailable
                                    </span>
                                  )}
                                </div>
                                <div className="flex items-center">
                                  {!featureEnabled && (
                                    <div className="mr-2">
                                      <SimpleHelpTip
                                        content="This feature is not available in your current subscription plan. Please upgrade your plan to access this feature."
                                        position="top"
                                      />
                                    </div>
                                  )}
                                  <label className={`inline-flex items-center ${featureEnabled ? 'cursor-pointer' : 'cursor-not-allowed'}`}>
                                    <input
                                      type="checkbox"
                                      className="sr-only peer"
                                      checked={item.isVisible}
                                      onChange={() => toggleSidebarItemVisibility(category.id, item.id)}
                                      disabled={item.isRequired || !featureEnabled}
                                    />
                                    <div className={`relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer ${item.isVisible ? 'peer-checked:bg-primary-600' : ''} ${item.isRequired || !featureEnabled ? 'opacity-50' : ''}`}>
                                      <div className={`absolute top-[2px] left-[2px] bg-white border-gray-300 border rounded-full h-5 w-5 transition-all ${item.isVisible ? 'translate-x-5' : ''}`}></div>
                                    </div>
                                    <span className="ml-3 text-sm font-medium text-gray-900">
                                      {item.isVisible ? 'Visible' : 'Hidden'}
                                    </span>
                                  </label>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Quick Links Menu Tab */}
          {activeTab === 'quicklinks' && (
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-4 py-5 sm:p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Customize Quick Links Menu</h2>
                <p className="text-sm text-gray-500 mb-4">
                  Select which items you want to show in the quick links dropdown menu.
                </p>

                {/* Add new page to quick links */}
                <div className="mb-6 p-4 bg-gray-50 rounded-md border border-gray-200">
                  <h3 className="text-md font-medium text-gray-900 mb-2">Add Page to Quick Links</h3>
                  <p className="text-sm text-gray-500 mb-4">
                    Select a page from the list below to add it to your quick links menu.
                  </p>

                  <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
                    <div className="flex-grow">
                      <select
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        value={selectedPage?.id || ''}
                        onChange={(e) => {
                          // Find the selected page from all sidebar categories
                          let found = false;
                          for (const category of sidebarCategories) {
                            const page = category.items.find(item => item.id === e.target.value);
                            if (page) {
                              setSelectedPage(page);
                              found = true;
                              break;
                            }
                          }
                          if (!found) {
                            setSelectedPage(null);
                          }
                        }}
                      >
                        <option value="">Select a page</option>
                        {sidebarCategories.map(category => (
                          <optgroup key={category.id} label={category.title}>
                            {category.items.map(item => {
                              const featureEnabled = isFeatureEnabled(item.id);
                              return (
                                <option 
                                  key={item.id} 
                                  value={item.id}
                                  disabled={!featureEnabled}
                                  className={!featureEnabled ? 'text-gray-400' : ''}
                                >
                                  {item.title} {!featureEnabled ? '(Unavailable)' : ''}
                                </option>
                              );
                            })}
                          </optgroup>
                        ))}
                      </select>
                    </div>
                    <div>
                      <button
                        type="button"
                        disabled={!selectedPage || (selectedPage && !isFeatureEnabled(selectedPage.id))}
                        onClick={() => {
                          if (selectedPage && isFeatureEnabled(selectedPage.id)) {
                            addPageToQuickLinks(selectedPage);
                            setSelectedPage(null);
                          }
                        }}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Add to Quick Links
                      </button>
                      {selectedPage && !isFeatureEnabled(selectedPage.id) && (
                        <div className="mt-2 text-sm text-yellow-600">
                          This feature is not available in your current subscription plan.
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  {localQuickLinksItems.map(item => {
                    const featureEnabled = isFeatureEnabled(item.id);
                    return (
                      <div key={item.id} className="flex items-center justify-between py-2 border-b border-gray-200">
                        <div className="flex items-center">
                          <span className={`text-sm font-medium ${featureEnabled ? 'text-gray-900' : 'text-gray-400'}`}>
                            {item.title}
                          </span>
                          {!featureEnabled && (
                            <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                              Unavailable
                            </span>
                          )}
                        </div>
                        <div className="flex items-center">
                          {!featureEnabled && (
                            <div className="mr-2">
                              <SimpleHelpTip
                                content="This feature is not available in your current subscription plan. Please upgrade your plan to access this feature."
                                position="top"
                              />
                            </div>
                          )}
                          <label className={`inline-flex items-center ${featureEnabled ? 'cursor-pointer' : 'cursor-not-allowed'}`}>
                            <input
                              type="checkbox"
                              className="sr-only peer"
                              checked={item.isVisible}
                              onChange={() => toggleQuickLinksItemVisibility(item.id)}
                              disabled={!featureEnabled}
                            />
                            <div className={`relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer ${item.isVisible ? 'peer-checked:bg-primary-600' : ''} ${!featureEnabled ? 'opacity-50' : ''}`}>
                              <div className={`absolute top-[2px] left-[2px] bg-white border-gray-300 border rounded-full h-5 w-5 transition-all ${item.isVisible ? 'translate-x-5' : ''}`}></div>
                            </div>
                            <span className="ml-3 text-sm font-medium text-gray-900">
                              {item.isVisible ? 'Visible' : 'Hidden'}
                            </span>
                          </label>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {/* Action buttons */}
          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              onClick={resetMenuPreferences}
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Reset to Defaults
            </button>
            <button
              type="button"
              onClick={saveMenuPreferences}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Save Preferences
            </button>
          </div>
        </>
      )}
    </Layout>
  );
};

export default MenuCustomization;
