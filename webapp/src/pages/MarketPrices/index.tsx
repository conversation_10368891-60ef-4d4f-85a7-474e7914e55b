import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

interface CommodityPrice {
  commodity: string;
  price: number;
  unit: string;
  date: string;
  location: string;
  trend: 'up' | 'down' | 'stable';
  percentChange: number;
}

interface FuturePrice {
  commodity: string;
  price: number;
  unit: string;
  month: string;
  year: string;
  exchange: string;
}

interface MarketPriceData {
  currentPrices: CommodityPrice[];
  futurePrices: FuturePrice[];
  lastUpdated: string;
}

interface HistoricalPrice {
  commodity: string;
  price: number;
  unit: string;
  date: string;
  location: string;
  trend: 'up' | 'down';
  percentChange: number;
}

const MarketPricesPage: React.FC = () => {
  const { selectedFarm } = useFarm();
  const [marketData, setMarketData] = useState<MarketPriceData | null>(null);
  const [selectedCommodity, setSelectedCommodity] = useState<string>('');
  const [historicalData, setHistoricalData] = useState<HistoricalPrice[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMarketData = async () => {
      if (!selectedFarm) return;

      setLoading(true);
      setError(null);

      try {
        // Get farm location from selectedFarm
        const farmLocation = {
          latitude: selectedFarm.latitude || 37.7749, // Default to San Francisco if not available
          longitude: selectedFarm.longitude || -122.4194
        };

        // Fetch market price data with farm location
        const response = await axios.get(`/api/market-prices`, {
          params: {
            lat: farmLocation.latitude,
            lon: farmLocation.longitude,
            commodities: 'beef,corn,hay,wheat,soybeans' // Default commodities
          }
        });

        setMarketData(response.data);

        // Set the first commodity as selected by default
        const commodities = response.data.currentPrices.map(price => price.commodity);
        if (commodities.length > 0 && !selectedCommodity) {
          setSelectedCommodity(commodities[0]);
          fetchHistoricalData(commodities[0]);
        }
      } catch (err) {
        console.error('Error fetching market data:', err);
        setError('Failed to load market price data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchMarketData();
  }, [selectedFarm]);

  const fetchHistoricalData = async (commodity: string) => {
    if (!commodity) return;

    try {
      // Get date range for historical data (last 30 days)
      const endDate = new Date().toISOString().split('T')[0];
      const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      const response = await axios.get(`/api/market-prices/historical`, {
        params: {
          commodity,
          startDate,
          endDate
        }
      });

      setHistoricalData(response.data);
    } catch (err) {
      console.error(`Error fetching historical data for ${commodity}:`, err);
      // Don't set error state here to avoid disrupting the main view
    }
  };

  const handleCommodityChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const commodity = e.target.value;
    setSelectedCommodity(commodity);
    fetchHistoricalData(commodity);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(value);
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      </Layout>
    );
  }

  if (!marketData || !selectedCommodity) {
    return (
      <Layout>
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">No market data available. Please select a farm to view market prices.</span>
        </div>
      </Layout>
    );
  }

  // Find the current price data for the selected commodity
  const currentPriceData = marketData.currentPrices.find(price => price.commodity === selectedCommodity);

  // Find the future prices for the selected commodity
  const futurePrices = marketData.futurePrices.filter(price => price.commodity === selectedCommodity);

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Market Price Tracker</h1>

        <div className="mb-6">
          <label htmlFor="commodity" className="block text-sm font-medium text-gray-700 mb-2">
            Select Commodity
          </label>
          <select
            id="commodity"
            value={selectedCommodity}
            onChange={handleCommodityChange}
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
          >
            {marketData.currentPrices.map(price => (
              <option key={price.commodity} value={price.commodity}>
                {price.commodity.charAt(0).toUpperCase() + price.commodity.slice(1)}
              </option>
            ))}
          </select>
        </div>

        {currentPriceData && (
          <>
            <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
              <div className="px-4 py-5 sm:px-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  {selectedCommodity.charAt(0).toUpperCase() + selectedCommodity.slice(1)} Price Information
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                  Current market data for {selectedCommodity} in the {currentPriceData.location} region.
                </p>
              </div>
              <div className="border-t border-gray-200">
                <dl>
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Current Price</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      {formatCurrency(currentPriceData.price)} per {currentPriceData.unit}
                    </dd>
                  </div>
                  <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Price Trend</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      <span className={currentPriceData.trend === 'up' ? 'text-green-600' : currentPriceData.trend === 'down' ? 'text-red-600' : 'text-gray-600'}>
                        {currentPriceData.trend === 'up' ? '↑ Rising' : currentPriceData.trend === 'down' ? '↓ Falling' : '→ Stable'}
                        {' '}({currentPriceData.percentChange > 0 ? '+' : ''}{currentPriceData.percentChange.toFixed(2)}%)
                      </span>
                    </dd>
                  </div>
                  <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Location</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      {currentPriceData.location}
                    </dd>
                  </div>
                  <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      {formatDate(marketData.lastUpdated)}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>

            <div className="mb-8">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Price History (Last 30 Days)</h3>
              <div className="bg-white p-4 rounded-lg shadow" style={{ height: '400px' }}>
                {historicalData.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={historicalData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="date" 
                        tickFormatter={(date) => {
                          const d = new Date(date);
                          return `${d.getMonth() + 1}/${d.getDate()}`;
                        }}
                      />
                      <YAxis 
                        domain={['auto', 'auto']}
                        tickFormatter={(value) => `$${value}`}
                      />
                      <Tooltip 
                        formatter={(value) => [`$${value}`, 'Price']}
                        labelFormatter={(date) => formatDate(date as string)}
                      />
                      <Legend />
                      <Line 
                        type="monotone" 
                        dataKey="price" 
                        stroke="#4f46e5" 
                        activeDot={{ r: 8 }} 
                        name={`${selectedCommodity.charAt(0).toUpperCase() + selectedCommodity.slice(1)} Price`}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex justify-center items-center h-full">
                    <p className="text-gray-500">Loading historical data...</p>
                  </div>
                )}
              </div>
            </div>

            {futurePrices.length > 0 && (
              <div className="mb-8">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Futures Contracts</h3>
                <div className="bg-white shadow overflow-hidden sm:rounded-lg">
                  <div className="px-4 py-5 sm:px-6">
                    <h4 className="text-md font-medium text-gray-900">
                      {selectedCommodity.charAt(0).toUpperCase() + selectedCommodity.slice(1)} Futures
                    </h4>
                    <p className="mt-1 max-w-2xl text-sm text-gray-500">
                      Future contract prices for {selectedCommodity}.
                    </p>
                  </div>
                  <div className="border-t border-gray-200">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Month/Year
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Price
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Unit
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Exchange
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {futurePrices.map((future, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {future.month} {future.year}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {formatCurrency(future.price)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {future.unit}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {future.exchange}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </Layout>
  );
};

export default MarketPricesPage;
