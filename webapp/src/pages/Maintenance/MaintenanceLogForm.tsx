import { useState, useEffect } from 'react';
import { useParams, useNavigate, Link, useLocation } from 'react-router-dom';
import axios from 'axios';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface MaintenanceLog {
  id?: string;
  equipment_id: string;
  maintenance_schedule_id: string | null;
  service_date: string;
  service_hours: number | null;
  service_type: string;
  description: string;
  performed_by: string;
  parts_used: string;
  labor_hours: number | null;
  labor_cost: number | null;
  parts_cost: number | null;
  total_cost: number | null;
  notes: string;
}

interface Equipment {
  id: string;
  name: string;
}

interface MaintenanceSchedule {
  id: string;
  equipment_id: string;
  equipment_name: string;
  maintenance_type: string;
}

const MaintenanceLogForm = () => {
  const { logId } = useParams<{ logId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const scheduleId = queryParams.get('scheduleId');

  const isEditMode = !!logId;

  const [log, setLog] = useState<MaintenanceLog>({
    equipment_id: '',
    maintenance_schedule_id: scheduleId || null,
    service_date: new Date().toISOString().split('T')[0],
    service_hours: null,
    service_type: '',
    description: '',
    performed_by: '',
    parts_used: '',
    labor_hours: null,
    labor_cost: null,
    parts_cost: null,
    total_cost: null,
    notes: ''
  });

  const [equipment, setEquipment] = useState<Equipment[]>([]);
  const [schedules, setSchedules] = useState<MaintenanceSchedule[]>([]);
  const [selectedSchedule, setSelectedSchedule] = useState<MaintenanceSchedule | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch equipment
  useEffect(() => {
    const fetchEquipment = async () => {
      try {
        const response = await axios.get(`${API_URL}/equipment`);
        const equipmentList = Array.isArray(response.data) ? response.data : response.data.equipment || [];
        setEquipment(equipmentList);
      } catch (err: any) {
        console.error('Error fetching equipment:', err);
        setError('Failed to load equipment. Please try again later.');
      }
    };

    fetchEquipment();
  }, []);

  // Fetch maintenance schedules for selected equipment
  useEffect(() => {
    const fetchSchedules = async () => {
      if (!log.equipment_id) return;

      try {
        const response = await axios.get(`${API_URL}/maintenance/schedules/equipment/${log.equipment_id}`);
        const schedulesList = response.data.maintenanceSchedules || [];
        setSchedules(schedulesList);

        // If we have a scheduleId from query params, find and select that schedule
        if (scheduleId) {
          const schedule = schedulesList.find((s: MaintenanceSchedule) => s.id === scheduleId);
          if (schedule) {
            setSelectedSchedule(schedule);
          }
        }
      } catch (err: any) {
        console.error('Error fetching maintenance schedules:', err);
        setError('Failed to load maintenance schedules. Please try again later.');
      }
    };

    fetchSchedules();
  }, [log.equipment_id, scheduleId]);

  // Fetch maintenance log data if in edit mode
  useEffect(() => {
    const fetchMaintenanceLog = async () => {
      if (!logId) return;

      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/maintenance/logs/${logId}`);
        const logData = response.data.maintenanceLog;

        // Format dates for the input fields
        const formattedData = {
          ...logData,
          service_date: logData.service_date ? 
            new Date(logData.service_date).toISOString().split('T')[0] : 
            new Date().toISOString().split('T')[0]
        };

        setLog(formattedData);

        // Fetch schedules for this equipment
        if (formattedData.equipment_id) {
          const schedulesResponse = await axios.get(`${API_URL}/maintenance/schedules/equipment/${formattedData.equipment_id}`);
          const schedulesList = schedulesResponse.data.maintenanceSchedules || [];
          setSchedules(schedulesList);

          // Find and select the schedule if there is one
          if (formattedData.maintenance_schedule_id) {
            const schedule = schedulesList.find((s: MaintenanceSchedule) => s.id === formattedData.maintenance_schedule_id);
            if (schedule) {
              setSelectedSchedule(schedule);
            }
          }
        }
      } catch (err: any) {
        console.error('Error fetching maintenance log:', err);
        setError('Failed to load maintenance log data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (isEditMode) {
      fetchMaintenanceLog();
    } else if (scheduleId) {
      // If creating a new log with a scheduleId, fetch the schedule to get equipment_id
      const fetchSchedule = async () => {
        try {
          const response = await axios.get(`${API_URL}/maintenance/schedules/${scheduleId}`);
          const scheduleData = response.data.maintenanceSchedule;
          setLog(prev => ({ 
            ...prev, 
            equipment_id: scheduleData.equipment_id,
            maintenance_schedule_id: scheduleId
          }));
          setSelectedSchedule(scheduleData);
        } catch (err: any) {
          console.error('Error fetching schedule:', err);
          setError('Failed to load schedule data. Please try again later.');
        }
      };

      fetchSchedule();
    }
  }, [logId, isEditMode, scheduleId]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // Special handling for numeric fields
    if (['service_hours', 'labor_hours', 'labor_cost', 'parts_cost', 'total_cost'].includes(name)) {
      const numValue = value === '' ? null : parseFloat(value);
      setLog(prev => ({ ...prev, [name]: numValue }));
    } else if (name === 'equipment_id') {
      // When equipment changes, reset maintenance_schedule_id
      setLog(prev => ({ 
        ...prev, 
        equipment_id: value,
        maintenance_schedule_id: null
      }));
      setSelectedSchedule(null);
    } else if (name === 'maintenance_schedule_id') {
      // When schedule changes, update selectedSchedule
      setLog(prev => ({ ...prev, maintenance_schedule_id: value === '' ? null : value }));
      const schedule = schedules.find(s => s.id === value);
      setSelectedSchedule(schedule || null);
    } else {
      setLog(prev => ({ ...prev, [name]: value }));
    }
  };

  // Calculate total cost when labor_cost or parts_cost changes
  useEffect(() => {
    const laborCost = log.labor_cost || 0;
    const partsCost = log.parts_cost || 0;
    const totalCost = laborCost + partsCost;

    setLog(prev => ({ 
      ...prev, 
      total_cost: totalCost > 0 ? totalCost : null
    }));
  }, [log.labor_cost, log.parts_cost]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (!log.equipment_id || !log.service_date || !log.service_type || !log.description) {
        setError('Equipment, service date, service type, and description are required.');
        setLoading(false);
        return;
      }

      if (isEditMode) {
        // Update existing maintenance log
        await axios.put(`${API_URL}/maintenance/logs/${logId}`, log);
      } else {
        // Create new maintenance log
        await axios.post(`${API_URL}/maintenance/logs`, log);
      }

      // If this log is for a maintenance schedule, navigate back to that schedule
      if (log.maintenance_schedule_id) {
        navigate(`/maintenance/schedules/${log.maintenance_schedule_id}`);
      } else {
        // Otherwise, navigate to equipment detail
        navigate(`/equipment/${log.equipment_id}`);
      }
    } catch (err: any) {
      console.error('Error saving maintenance log:', err);
      setError('Failed to save maintenance log. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit Service Log' : 'Log New Service'}
        </h1>
        <Link
          to={log.maintenance_schedule_id ? `/maintenance/schedules/${log.maintenance_schedule_id}` : "/maintenance"}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Cancel
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {selectedSchedule && (
        <div className="bg-white shadow rounded-lg p-4 mb-6">
          <h2 className="text-lg font-medium text-gray-900">Maintenance Schedule</h2>
          <div className="mt-2 grid grid-cols-1 gap-2 sm:grid-cols-2">
            <div>
              <span className="text-sm font-medium text-gray-500">Equipment:</span>
              <span className="ml-2 text-sm text-gray-900">{selectedSchedule.equipment_name}</span>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">Maintenance Type:</span>
              <span className="ml-2 text-sm text-gray-900">{selectedSchedule.maintenance_type}</span>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {/* Equipment Selection */}
            <div>
              <label htmlFor="equipment_id" className="block text-sm font-medium text-gray-700 mb-1">
                Equipment <span className="text-red-500">*</span>
              </label>
              <select
                id="equipment_id"
                name="equipment_id"
                value={log.equipment_id}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
                disabled={!!scheduleId || isEditMode}
              >
                <option value="" disabled>Select equipment</option>
                {equipment.map(item => (
                  <option key={item.id} value={item.id}>{item.name}</option>
                ))}
              </select>
            </div>

            {/* Maintenance Schedule Selection */}
            <div>
              <label htmlFor="maintenance_schedule_id" className="block text-sm font-medium text-gray-700 mb-1">
                Maintenance Schedule
              </label>
              <select
                id="maintenance_schedule_id"
                name="maintenance_schedule_id"
                value={log.maintenance_schedule_id || ''}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                disabled={!!scheduleId || isEditMode}
              >
                <option value="">None (Ad-hoc service)</option>
                {schedules.map(schedule => (
                  <option key={schedule.id} value={schedule.id}>{schedule.maintenance_type}</option>
                ))}
              </select>
            </div>

            {/* Service Date */}
            <div>
              <label htmlFor="service_date" className="block text-sm font-medium text-gray-700 mb-1">
                Service Date <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                id="service_date"
                name="service_date"
                value={log.service_date}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            {/* Service Hours */}
            <div>
              <label htmlFor="service_hours" className="block text-sm font-medium text-gray-700 mb-1">
                Equipment Hours at Service
              </label>
              <input
                type="number"
                id="service_hours"
                name="service_hours"
                value={log.service_hours === null ? '' : log.service_hours}
                onChange={handleChange}
                min="0"
                step="0.1"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 1250.5"
              />
            </div>

            {/* Service Type */}
            <div>
              <label htmlFor="service_type" className="block text-sm font-medium text-gray-700 mb-1">
                Service Type <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="service_type"
                name="service_type"
                value={log.service_type}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., Scheduled Maintenance, Repair, Inspection"
                required
              />
            </div>

            {/* Performed By */}
            <div>
              <label htmlFor="performed_by" className="block text-sm font-medium text-gray-700 mb-1">
                Performed By
              </label>
              <input
                type="text"
                id="performed_by"
                name="performed_by"
                value={log.performed_by}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., John Smith, ABC Service Center"
              />
            </div>

            {/* Labor Hours */}
            <div>
              <label htmlFor="labor_hours" className="block text-sm font-medium text-gray-700 mb-1">
                Labor Hours
              </label>
              <input
                type="number"
                id="labor_hours"
                name="labor_hours"
                value={log.labor_hours === null ? '' : log.labor_hours}
                onChange={handleChange}
                min="0"
                step="0.1"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 2.5"
              />
            </div>

            {/* Labor Cost */}
            <div>
              <label htmlFor="labor_cost" className="block text-sm font-medium text-gray-700 mb-1">
                Labor Cost
              </label>
              <input
                type="number"
                id="labor_cost"
                name="labor_cost"
                value={log.labor_cost === null ? '' : log.labor_cost}
                onChange={handleChange}
                min="0"
                step="0.01"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 125.00"
              />
            </div>

            {/* Parts Cost */}
            <div>
              <label htmlFor="parts_cost" className="block text-sm font-medium text-gray-700 mb-1">
                Parts Cost
              </label>
              <input
                type="number"
                id="parts_cost"
                name="parts_cost"
                value={log.parts_cost === null ? '' : log.parts_cost}
                onChange={handleChange}
                min="0"
                step="0.01"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 75.50"
              />
            </div>

            {/* Total Cost (calculated, read-only) */}
            <div>
              <label htmlFor="total_cost" className="block text-sm font-medium text-gray-700 mb-1">
                Total Cost
              </label>
              <input
                type="number"
                id="total_cost"
                name="total_cost"
                value={log.total_cost === null ? '' : log.total_cost}
                readOnly
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500 sm:text-sm"
              />
              <p className="mt-1 text-xs text-gray-500">
                Calculated from labor and parts costs
              </p>
            </div>
          </div>

          {/* Description */}
          <div className="mt-6">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Description <span className="text-red-500">*</span>
            </label>
            <textarea
              id="description"
              name="description"
              value={log.description}
              onChange={handleChange}
              rows={2}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              placeholder="Brief description of the service performed"
              required
            ></textarea>
          </div>

          {/* Parts Used */}
          <div className="mt-6">
            <label htmlFor="parts_used" className="block text-sm font-medium text-gray-700 mb-1">
              Parts Used
            </label>
            <textarea
              id="parts_used"
              name="parts_used"
              value={log.parts_used}
              onChange={handleChange}
              rows={2}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              placeholder="List of parts used in the service"
            ></textarea>
          </div>

          {/* Notes */}
          <div className="mt-6">
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              id="notes"
              name="notes"
              value={log.notes}
              onChange={handleChange}
              rows={4}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              placeholder="Additional information about the service"
            ></textarea>
          </div>

          {/* Form Actions */}
          <div className="mt-6 flex justify-end space-x-3">
            <Link
              to={log.maintenance_schedule_id ? `/maintenance/schedules/${log.maintenance_schedule_id}` : "/maintenance"}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Saving...' : 'Save Service Log'}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default MaintenanceLogForm;
