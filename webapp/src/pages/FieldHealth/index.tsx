import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { toast } from 'react-toastify';

interface Field {
  id: string;
  name: string;
  size: number;
  size_unit: string;
  field_type: string;
  status: string;
}

interface FieldHealth {
  field_id: string;
  ndvi_average: number;
  ndvi_min: number;
  ndvi_max: number;
  ndvi_data: {
    timestamp: string;
    resolution: string;
    ndvi_average: number;
  };
  timestamp: string;
}

interface SoilAnalysis {
  status: string;
  nutrients: {
    nitrogen: { value: number; unit: string; status: string };
    phosphorus: { value: number; unit: string; status: string };
    potassium: { value: number; unit: string; status: string };
  };
  ph: { value: number; status: string };
  organic_matter: { value: number; status: string };
  last_sample_date: string | null;
}

interface FieldHealthAnalysis {
  field_id: string;
  ndvi_data: {
    timestamp: string;
    resolution: string;
    ndvi_average: number;
  };
  soil_analysis: SoilAnalysis;
  health_score: number;
  timestamp: string;
}

interface Recommendation {
  type: string;
  priority: string;
  description: string;
  details: string;
}

interface FieldRecommendations {
  field_id: string;
  soil_recommendations: Recommendation[];
  ndvi_recommendations: Recommendation[];
  timestamp: string;
}

interface VegetationIndexData {
  ndvi_assessment: string;
  overall_vegetation_health: string;
}

interface PestPressureData {
  current_pressure: string;
  risk_assessment: string;
}

interface DiseaseIndicators {
  current_presence: string;
  risk_assessment: string;
}

interface WeatherImpactData {
  current_impact: string;
  forecast_risk: string;
}

interface IdentifiedIssue {
  issue: string;
  description: string;
  priority: string;
}

interface AIRecommendation {
  action: string;
  details: string;
  expected_impact: string;
}

interface AIFieldHealthAnalysis {
  id: string;
  farm_id: string;
  field_id: string | null;
  crop_id: string | null;
  vegetation_index_data: VegetationIndexData;
  pest_pressure_data: PestPressureData;
  disease_indicators: DiseaseIndicators;
  weather_impact_data: WeatherImpactData;
  issues_identified: IdentifiedIssue[];
  recommendations: AIRecommendation[];
  priority_level: string;
  expected_impact: string;
  confidence_score: number;
  analysis_date: string;
  is_applied: boolean;
}

const FieldHealthPage: React.FC = () => {
  const { selectedFarm } = useFarm();
  const [fields, setFields] = useState<Field[]>([]);
  const [selectedField, setSelectedField] = useState<string>('');
  const [fieldHealth, setFieldHealth] = useState<FieldHealth | null>(null);
  const [fieldAnalysis, setFieldAnalysis] = useState<FieldHealthAnalysis | null>(null);
  const [recommendations, setRecommendations] = useState<FieldRecommendations | null>(null);
  const [aiAnalysis, setAiAnalysis] = useState<AIFieldHealthAnalysis | null>(null);
  const [loadingAI, setLoadingAI] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch fields when farm changes
  useEffect(() => {
    const fetchFields = async () => {
      if (!selectedFarm) return;

      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`/api/fields/farm/${selectedFarm.id}`);
        setFields(response.data);

        // Set the first field as selected by default
        if (response.data.length > 0 && !selectedField) {
          setSelectedField(response.data[0].id);
        }
      } catch (err) {
        console.error('Error fetching fields:', err);
        setError('Failed to load fields. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchFields();
  }, [selectedFarm]);

  // Fetch field health data when selected field changes
  useEffect(() => {
    const fetchFieldHealth = async () => {
      if (!selectedField || !selectedFarm) return;

      setLoading(true);
      setError(null);

      try {
        // Fetch field health data
        const healthResponse = await axios.get(`/api/field-health/${selectedField}`);
        setFieldHealth(healthResponse.data);

        // Fetch field health analysis
        const analysisResponse = await axios.get(`/api/field-health/${selectedField}/soil-analysis`);
        setFieldAnalysis(analysisResponse.data);

        // Fetch recommendations
        const recommendationsResponse = await axios.get(`/api/field-health/${selectedField}/recommendations`);
        setRecommendations(recommendationsResponse.data);

        // Fetch AI field health analysis
        const aiAnalysisResponse = await axios.get(`/api/ai-analysis/field-health/${selectedFarm.id}`, {
          params: { fieldId: selectedField }
        });

        if (aiAnalysisResponse.data.analysis) {
          setAiAnalysis(aiAnalysisResponse.data.analysis);
        } else {
          setAiAnalysis(null);
        }
      } catch (err) {
        console.error('Error fetching field health data:', err);
        setError('Failed to load field health data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchFieldHealth();
  }, [selectedField, selectedFarm]);

  // Function to generate AI field health analysis
  const generateAIAnalysis = async () => {
    if (!selectedFarm || !selectedField) return;

    setLoadingAI(true);

    try {
      const response = await axios.post('/api/ai-analysis/field-health', {
        farmId: selectedFarm.id,
        fieldId: selectedField
      });

      if (response.data.analysis) {
        setAiAnalysis(response.data.analysis);
        toast.success('AI field health analysis generated successfully');
      }
    } catch (err) {
      console.error('Error generating AI field health analysis:', err);
      toast.error('Failed to generate AI field health analysis. Please try again later.');
    } finally {
      setLoadingAI(false);
    }
  };

  // Function to apply AI field health analysis
  const applyAIAnalysis = async () => {
    if (!aiAnalysis) return;

    setLoadingAI(true);

    try {
      const response = await axios.put(`/api/ai-analysis/field-health/${aiAnalysis.id}`, {
        isApplied: true
      });

      if (response.data.analysis) {
        setAiAnalysis(response.data.analysis);
        toast.success('AI field health analysis applied successfully');
      }
    } catch (err) {
      console.error('Error applying AI field health analysis:', err);
      toast.error('Failed to apply AI field health analysis. Please try again later.');
    } finally {
      setLoadingAI(false);
    }
  };

  const handleFieldChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedField(e.target.value);
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const getNDVIColorClass = (ndvi: number) => {
    if (ndvi >= 0.7) return 'bg-green-500';
    if (ndvi >= 0.5) return 'bg-green-400';
    if (ndvi >= 0.3) return 'bg-yellow-400';
    if (ndvi >= 0.2) return 'bg-orange-400';
    return 'bg-red-500';
  };

  const getStatusColorClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'optimal':
      case 'good':
        return 'text-green-600';
      case 'adequate':
        return 'text-blue-600';
      case 'low':
        return 'text-yellow-600';
      case 'deficient':
        return 'text-red-600';
      case 'excessive':
        return 'text-purple-600';
      case 'neutral':
        return 'text-gray-600';
      default:
        return 'text-gray-600';
    }
  };

  const getPriorityColorClass = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (loading && !fields.length) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        </div>
      </Layout>
    );
  }

  if (error && !fields.length) {
    return (
      <Layout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      </Layout>
    );
  }

  if (!fields.length) {
    return (
      <Layout>
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">No fields available. Please add fields to your farm first.</span>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Field Health Analytics</h1>

        <div className="mb-6">
          <label htmlFor="field" className="block text-sm font-medium text-gray-700 mb-2">
            Select Field
          </label>
          <select
            id="field"
            value={selectedField}
            onChange={handleFieldChange}
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
          >
            {fields.map(field => (
              <option key={field.id} value={field.id}>
                {field.name} ({field.size} {field.size_unit})
              </option>
            ))}
          </select>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        ) : error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        ) : (
          <>
            {fieldHealth && (
              <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Vegetation Health Index (NDVI)
                  </h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    Normalized Difference Vegetation Index measures the health and density of vegetation.
                  </p>
                </div>
                <div className="border-t border-gray-200">
                  <dl>
                    <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Average NDVI</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <div className="flex items-center">
                          <span className="mr-2">{fieldHealth.ndvi_average.toFixed(2)}</span>
                          <div className={`h-4 w-4 rounded-full ${getNDVIColorClass(fieldHealth.ndvi_average)}`}></div>
                        </div>
                      </dd>
                    </div>
                    <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">NDVI Range</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        {fieldHealth.ndvi_min.toFixed(2)} - {fieldHealth.ndvi_max.toFixed(2)}
                      </dd>
                    </div>
                    <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        {formatDate(fieldHealth.timestamp)}
                      </dd>
                    </div>
                    <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Resolution</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        {fieldHealth.ndvi_data.resolution}
                      </dd>
                    </div>
                  </dl>
                </div>
              </div>
            )}

            {fieldAnalysis && fieldAnalysis.soil_analysis && (
              <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Soil Analysis
                  </h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    Analysis of soil health based on soil samples and amendments.
                  </p>
                </div>
                <div className="border-t border-gray-200">
                  <dl>
                    <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Overall Status</dt>
                      <dd className="mt-1 text-sm sm:mt-0 sm:col-span-2">
                        <span className={getStatusColorClass(fieldAnalysis.soil_analysis.status)}>
                          {fieldAnalysis.soil_analysis.status.charAt(0).toUpperCase() + fieldAnalysis.soil_analysis.status.slice(1)}
                        </span>
                      </dd>
                    </div>
                    <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Nitrogen</dt>
                      <dd className="mt-1 text-sm sm:mt-0 sm:col-span-2">
                        <span className={getStatusColorClass(fieldAnalysis.soil_analysis.nutrients.nitrogen.status)}>
                          {fieldAnalysis.soil_analysis.nutrients.nitrogen.value} {fieldAnalysis.soil_analysis.nutrients.nitrogen.unit} 
                          ({fieldAnalysis.soil_analysis.nutrients.nitrogen.status})
                        </span>
                      </dd>
                    </div>
                    <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Phosphorus</dt>
                      <dd className="mt-1 text-sm sm:mt-0 sm:col-span-2">
                        <span className={getStatusColorClass(fieldAnalysis.soil_analysis.nutrients.phosphorus.status)}>
                          {fieldAnalysis.soil_analysis.nutrients.phosphorus.value} {fieldAnalysis.soil_analysis.nutrients.phosphorus.unit}
                          ({fieldAnalysis.soil_analysis.nutrients.phosphorus.status})
                        </span>
                      </dd>
                    </div>
                    <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Potassium</dt>
                      <dd className="mt-1 text-sm sm:mt-0 sm:col-span-2">
                        <span className={getStatusColorClass(fieldAnalysis.soil_analysis.nutrients.potassium.status)}>
                          {fieldAnalysis.soil_analysis.nutrients.potassium.value} {fieldAnalysis.soil_analysis.nutrients.potassium.unit}
                          ({fieldAnalysis.soil_analysis.nutrients.potassium.status})
                        </span>
                      </dd>
                    </div>
                    <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">pH</dt>
                      <dd className="mt-1 text-sm sm:mt-0 sm:col-span-2">
                        <span className={getStatusColorClass(fieldAnalysis.soil_analysis.ph.status)}>
                          {fieldAnalysis.soil_analysis.ph.value} ({fieldAnalysis.soil_analysis.ph.status})
                        </span>
                      </dd>
                    </div>
                    <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Organic Matter</dt>
                      <dd className="mt-1 text-sm sm:mt-0 sm:col-span-2">
                        <span className={getStatusColorClass(fieldAnalysis.soil_analysis.organic_matter.status)}>
                          {fieldAnalysis.soil_analysis.organic_matter.value}% ({fieldAnalysis.soil_analysis.organic_matter.status})
                        </span>
                      </dd>
                    </div>
                    <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Last Sample Date</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        {formatDate(fieldAnalysis.soil_analysis.last_sample_date)}
                      </dd>
                    </div>
                  </dl>
                </div>
              </div>
            )}

            {recommendations && (
              <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Recommendations
                  </h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    Recommendations based on field health analysis and soil samples.
                  </p>
                </div>
                <div className="border-t border-gray-200 px-4 py-5">
                  <h4 className="text-md font-medium text-gray-900 mb-4">Soil Recommendations</h4>
                  <div className="space-y-4">
                    {recommendations.soil_recommendations.map((rec, index) => (
                      <div key={index} className={`p-4 border rounded-md ${getPriorityColorClass(rec.priority)}`}>
                        <div className="flex justify-between items-start">
                          <h5 className="text-sm font-medium">{rec.description}</h5>
                          <span className="text-xs font-medium uppercase">{rec.priority} Priority</span>
                        </div>
                        <p className="mt-2 text-sm">{rec.details}</p>
                      </div>
                    ))}
                  </div>

                  <h4 className="text-md font-medium text-gray-900 mt-6 mb-4">Vegetation Health Recommendations</h4>
                  <div className="space-y-4">
                    {recommendations.ndvi_recommendations.map((rec, index) => (
                      <div key={index} className={`p-4 border rounded-md ${getPriorityColorClass(rec.priority)}`}>
                        <div className="flex justify-between items-start">
                          <h5 className="text-sm font-medium">{rec.description}</h5>
                          <span className="text-xs font-medium uppercase">{rec.priority} Priority</span>
                        </div>
                        <p className="mt-2 text-sm">{rec.details}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {fieldAnalysis && (
              <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Overall Field Health Score
                  </h3>
                </div>
                <div className="border-t border-gray-200 px-4 py-5">
                  <div className="flex flex-col items-center">
                    <div className="relative pt-1 w-full max-w-md">
                      <div className="flex mb-2 items-center justify-between">
                        <div>
                          <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-primary-600 bg-primary-200">
                            Health Score
                          </span>
                        </div>
                        <div className="text-right">
                          <span className="text-xs font-semibold inline-block text-primary-600">
                            {fieldAnalysis.health_score}/100
                          </span>
                        </div>
                      </div>
                      <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-primary-200">
                        <div 
                          style={{ width: `${fieldAnalysis.health_score}%` }} 
                          className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-primary-500">
                        </div>
                      </div>
                    </div>
                    <p className="text-sm text-gray-500 mt-2">
                      Last updated: {formatDate(fieldAnalysis.timestamp)}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* AI Field Health Analysis */}
            <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
              <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
                <div>
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    AI Field Health Analysis
                  </h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    Advanced analysis of field health using artificial intelligence.
                  </p>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={generateAIAnalysis}
                    disabled={loadingAI}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                  >
                    {loadingAI ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                      </>
                    ) : (
                      'Generate Analysis'
                    )}
                  </button>
                  {aiAnalysis && !aiAnalysis.is_applied && (
                    <button
                      onClick={applyAIAnalysis}
                      disabled={loadingAI}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                    >
                      {loadingAI ? 'Applying...' : 'Apply Analysis'}
                    </button>
                  )}
                </div>
              </div>

              <div className="border-t border-gray-200">
                {loadingAI ? (
                  <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
                  </div>
                ) : !aiAnalysis ? (
                  <div className="px-4 py-5 sm:px-6">
                    <p className="text-sm text-gray-500">
                      No AI analysis available for this field. Click "Generate Analysis" to create one.
                    </p>
                  </div>
                ) : (
                  <dl>
                    <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Analysis Date</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        {formatDate(aiAnalysis.analysis_date)}
                      </dd>
                    </div>

                    <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Status</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${aiAnalysis.is_applied ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                          {aiAnalysis.is_applied ? 'Applied' : 'Not Applied'}
                        </span>
                      </dd>
                    </div>

                    <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Confidence Score</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <div className="flex items-center">
                          <span className="mr-2">{aiAnalysis.confidence_score.toFixed(1)}%</span>
                          <div className="relative w-24 h-2 bg-gray-200 rounded">
                            <div 
                              style={{ width: `${aiAnalysis.confidence_score}%` }} 
                              className="absolute top-0 h-2 rounded bg-blue-500">
                            </div>
                          </div>
                        </div>
                      </dd>
                    </div>

                    <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Vegetation Health</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <div className="flex flex-col space-y-1">
                          <div>
                            <span className="font-medium">NDVI Assessment:</span> {aiAnalysis.vegetation_index_data.ndvi_assessment}
                          </div>
                          <div>
                            <span className="font-medium">Overall Health:</span> {aiAnalysis.vegetation_index_data.overall_vegetation_health}
                          </div>
                        </div>
                      </dd>
                    </div>

                    <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Pest Pressure</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <div className="flex flex-col space-y-1">
                          <div>
                            <span className="font-medium">Current Pressure:</span> {aiAnalysis.pest_pressure_data.current_pressure}
                          </div>
                          <div>
                            <span className="font-medium">Risk Assessment:</span> {aiAnalysis.pest_pressure_data.risk_assessment}
                          </div>
                        </div>
                      </dd>
                    </div>

                    <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Disease Indicators</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <div className="flex flex-col space-y-1">
                          <div>
                            <span className="font-medium">Current Presence:</span> {aiAnalysis.disease_indicators.current_presence}
                          </div>
                          <div>
                            <span className="font-medium">Risk Assessment:</span> {aiAnalysis.disease_indicators.risk_assessment}
                          </div>
                        </div>
                      </dd>
                    </div>

                    <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Weather Impact</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <div className="flex flex-col space-y-1">
                          <div>
                            <span className="font-medium">Current Impact:</span> {aiAnalysis.weather_impact_data.current_impact}
                          </div>
                          <div>
                            <span className="font-medium">Forecast Risk:</span> {aiAnalysis.weather_impact_data.forecast_risk}
                          </div>
                        </div>
                      </dd>
                    </div>

                    <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Priority Level</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          aiAnalysis.priority_level === 'High' ? 'bg-red-100 text-red-800' :
                          aiAnalysis.priority_level === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {aiAnalysis.priority_level}
                        </span>
                      </dd>
                    </div>

                    <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                      <dt className="text-sm font-medium text-gray-500">Expected Impact</dt>
                      <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        {aiAnalysis.expected_impact}
                      </dd>
                    </div>
                  </dl>
                )}
              </div>
            </div>

            {/* AI Identified Issues */}
            {aiAnalysis && aiAnalysis.issues_identified.length > 0 && (
              <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    AI Identified Issues
                  </h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    Issues identified by AI analysis that may require attention.
                  </p>
                </div>
                <div className="border-t border-gray-200 px-4 py-5">
                  <div className="space-y-4">
                    {aiAnalysis.issues_identified.map((issue, index) => (
                      <div key={index} className={`p-4 border rounded-md ${
                        issue.priority === 'High' ? 'bg-red-100 text-red-800 border-red-200' :
                        issue.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                        'bg-green-100 text-green-800 border-green-200'
                      }`}>
                        <div className="flex justify-between items-start">
                          <h5 className="text-sm font-medium">{issue.issue}</h5>
                          <span className="text-xs font-medium uppercase">{issue.priority} Priority</span>
                        </div>
                        <p className="mt-2 text-sm">{issue.description}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* AI Recommendations */}
            {aiAnalysis && aiAnalysis.recommendations.length > 0 && (
              <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    AI Recommendations
                  </h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    Recommended actions based on AI analysis.
                  </p>
                </div>
                <div className="border-t border-gray-200 px-4 py-5">
                  <div className="space-y-4">
                    {aiAnalysis.recommendations.map((recommendation, index) => (
                      <div key={index} className="p-4 border rounded-md bg-blue-50 text-blue-800 border-blue-200">
                        <h5 className="text-sm font-medium">{recommendation.action}</h5>
                        <p className="mt-2 text-sm">{recommendation.details}</p>
                        <div className="mt-2 text-xs text-blue-600">
                          <span className="font-medium">Expected Impact:</span> {recommendation.expected_impact}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </Layout>
  );
};

export default FieldHealthPage;
