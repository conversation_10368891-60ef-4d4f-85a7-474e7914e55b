import React, { useState, useEffect, useContext } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import FormBuilder, { FormElement } from '../../components/FormBuilder/FormBuilder';
import { API_URL } from '../../config';

interface Signer {
  id: string;
  name: string;
  email: string;
  role?: string;
}

interface Template {
  id: string;
  title: string;
  description: string;
  document_type: string;
  elements: any[];
}

const CreateFromFormTemplate: React.FC = () => {
  const { templateId } = useParams<{ templateId: string }>();
  
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [documentType, setDocumentType] = useState('other');
  const [elements, setElements] = useState<FormElement[]>([]);
  const [signers, setSigners] = useState<Signer[]>([]);
  const [template, setTemplate] = useState<Template | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();
  const navigate = useNavigate();

  // Fetch template data
  useEffect(() => {
    const fetchTemplate = async () => {
      try {
        const response = await axios.get(`${API_URL}/document-signing/templates/${templateId}`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });

        const templateData = response.data;
        
        setTemplate(templateData);
        setTitle(templateData.title);
        setDescription(templateData.description || '');
        setDocumentType(templateData.document_type);
        
        // Map template elements to FormElement format
        if (templateData.elements && templateData.elements.length > 0) {
          const formElements: FormElement[] = templateData.elements.map((element: any) => ({
            id: element.id,
            element_type: element.element_type,
            content: element.content,
            image_path: element.image_path,
            field_type: element.field?.field_type,
            field_name: element.field?.field_name,
            is_required: element.field?.is_required,
            placeholder: element.field?.placeholder,
            options: element.field?.options,
            validation_rules: element.field?.validation_rules,
            style_properties: element.field?.style_properties,
            page_number: element.page_number,
            x_position: element.x_position,
            y_position: element.y_position,
            width: element.width,
            height: element.height,
            z_index: element.z_index,
            font_family: element.font_family,
            font_size: element.font_size,
            font_color: element.font_color,
            background_color: element.background_color,
            border_style: element.border_style,
            border_width: element.border_width,
            border_color: element.border_color
          }));
          
          setElements(formElements);
        }
      } catch (err: any) {
        console.error('Error fetching template:', err);
        setError(err.response?.data?.error || 'Failed to load template');
      } finally {
        setLoading(false);
      }
    };

    if (templateId) {
      fetchTemplate();
    } else {
      setLoading(false);
      setError('No template ID provided');
    }
  }, [templateId]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!currentFarm?.id && !user?.farm_id) {
      setError('No farm selected');
      return;
    }

    if (!templateId) {
      setError('No template ID provided');
      return;
    }

    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const payload = {
        title,
        description,
        signers: signers.map(signer => ({
          name: signer.name,
          email: signer.email,
          role: signer.role
        }))
      };

      // Create document from template
      const response = await axios.post(
        `${API_URL}/document-signing/form-built-templates/${templateId}/create-document`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        }
      );
      
      setSuccess('Document created successfully');
      
      // Navigate to the document list after a short delay
      setTimeout(() => {
        navigate('/documents/signing');
      }, 1500);
    } catch (err: any) {
      console.error('Error creating document:', err);
      setError(err.response?.data?.error || 'Failed to create document');
    } finally {
      setSaving(false);
    }
  };

  // Handle adding a signer
  const handleAddSigner = () => {
    const newSigner: Signer = {
      id: `temp-${Date.now()}`,
      name: '',
      email: '',
      role: ''
    };
    
    setSigners([...signers, newSigner]);
  };

  // Handle updating a signer
  const handleUpdateSigner = (index: number, field: string, value: string) => {
    const updatedSigners = [...signers];
    updatedSigners[index] = {
      ...updatedSigners[index],
      [field]: value
    };
    
    setSigners(updatedSigners);
  };

  // Handle removing a signer
  const handleRemoveSigner = (index: number) => {
    const updatedSigners = [...signers];
    updatedSigners.splice(index, 1);
    
    setSigners(updatedSigners);
  };

  return (
    <Layout>
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">
            Create Document from Template
          </h1>
          
          <button
            onClick={() => navigate('/documents/signing/templates')}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded hover:bg-gray-50"
          >
            Back to Templates
          </button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <p className="text-gray-500">Loading template...</p>
        </div>
      ) : (
        <div className="bg-white shadow-sm rounded-lg p-6">
          {error && (
            <div className="mb-4 p-4 bg-red-100 text-red-700 rounded">
              {error}
            </div>
          )}
          
          {success && (
            <div className="mb-4 p-4 bg-green-100 text-green-700 rounded">
              {success}
            </div>
          )}
          
          {template && (
            <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded">
              <h2 className="text-lg font-medium text-blue-800 mb-2">Template: {template.title}</h2>
              {template.description && (
                <p className="text-blue-700">{template.description}</p>
              )}
            </div>
          )}
          
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 gap-6 mb-6">
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                  Document Title *
                </label>
                <input
                  type="text"
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  required
                  className="w-full border border-gray-300 rounded px-3 py-2"
                />
              </div>
              
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={3}
                  className="w-full border border-gray-300 rounded px-3 py-2"
                />
              </div>
            </div>
            
            <div className="mb-6">
              <h2 className="text-lg font-medium text-gray-900 mb-2">Signers</h2>
              
              {signers.length === 0 ? (
                <p className="text-gray-500 mb-2">No signers added yet. Add signers to assign fields to them.</p>
              ) : (
                <div className="space-y-3 mb-3">
                  {signers.map((signer, index) => (
                    <div key={signer.id} className="flex items-center space-x-2 p-3 border border-gray-200 rounded">
                      <div className="flex-1 grid grid-cols-3 gap-2">
                        <input
                          type="text"
                          value={signer.name}
                          onChange={(e) => handleUpdateSigner(index, 'name', e.target.value)}
                          placeholder="Name"
                          className="border border-gray-300 rounded px-2 py-1"
                        />
                        <input
                          type="email"
                          value={signer.email}
                          onChange={(e) => handleUpdateSigner(index, 'email', e.target.value)}
                          placeholder="Email"
                          className="border border-gray-300 rounded px-2 py-1"
                        />
                        <input
                          type="text"
                          value={signer.role || ''}
                          onChange={(e) => handleUpdateSigner(index, 'role', e.target.value)}
                          placeholder="Role (optional)"
                          className="border border-gray-300 rounded px-2 py-1"
                        />
                      </div>
                      <button
                        type="button"
                        onClick={() => handleRemoveSigner(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                </div>
              )}
              
              <button
                type="button"
                onClick={handleAddSigner}
                className="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50"
              >
                Add Signer
              </button>
            </div>
            
            <div className="mb-6">
              <h2 className="text-lg font-medium text-gray-900 mb-2">Template Preview</h2>
              <div className="border border-gray-300 rounded" style={{ height: '600px' }}>
                <FormBuilder
                  initialElements={elements}
                  signers={signers}
                  readOnly={true}
                />
              </div>
            </div>
            
            <div className="flex justify-end">
              <button
                type="submit"
                disabled={saving}
                className="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 disabled:opacity-50"
              >
                {saving ? 'Creating...' : 'Create Document'}
              </button>
            </div>
          </form>
        </div>
      )}
    </Layout>
  );
};

export default CreateFromFormTemplate;