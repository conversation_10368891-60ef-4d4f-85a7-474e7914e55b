import { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface Farm {
  id: string;
  name: string;
}

interface Equipment {
  id: string;
  name: string;
  type: string;
  manufacturer: string;
  model: string;
  year: number;
}

interface EquipmentSharing {
  id: string;
  equipment_id: string;
  owner_farm_id: string;
  renter_farm_id: string;
  start_date: string;
  end_date: string;
  rental_cost: number | null;
  rental_cost_type: string | null;
  status: string;
  notes: string | null;
  created_at: string;
  updated_at: string;
  Equipment: Equipment;
  OwnerFarm: Farm;
  RenterFarm: Farm;
}

const EquipmentSharingDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [sharingRecord, setSharingRecord] = useState<EquipmentSharing | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userFarms, setUserFarms] = useState<string[]>([]);

  const { user } = useContext(AuthContext);

  // Fetch user farms and sharing record
  useEffect(() => {
    const fetchData = async () => {
      if (!id || !user) return;

      try {
        // Fetch user farms
        const farmsResponse = await axios.get(`${API_URL}/farms/user/${user.id}`);
        const farms = Array.isArray(farmsResponse.data) 
          ? farmsResponse.data 
          : farmsResponse.data.farms || [];

        setUserFarms(farms.map((farm: Farm) => farm.id));

        // Fetch sharing record
        const sharingResponse = await axios.get(`${API_URL}/equipment-sharing/${id}`);
        setSharingRecord(sharingResponse.data.sharingRecord);
      } catch (err: any) {
        console.error('Error fetching data:', err);
        setError('Failed to load equipment sharing record. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id, user]);

  // Handle status update
  const handleUpdateStatus = async (newStatus: string) => {
    if (!sharingRecord) return;

    try {
      await axios.patch(`${API_URL}/equipment-sharing/${id}/status`, { status: newStatus });

      // Update the status in the local state
      setSharingRecord({
        ...sharingRecord,
        status: newStatus
      });
    } catch (err: any) {
      console.error('Error updating equipment sharing status:', err);
      setError('Failed to update status. Please try again later.');
    }
  };

  // Handle deletion
  const handleDelete = async () => {
    if (!sharingRecord) return;

    if (!window.confirm('Are you sure you want to delete this equipment sharing record?')) {
      return;
    }

    try {
      await axios.delete(`${API_URL}/equipment-sharing/${id}`);
      navigate('/equipment-sharing');
    } catch (err: any) {
      console.error('Error deleting equipment sharing record:', err);
      setError('Failed to delete equipment sharing record. Please try again later.');
    }
  };

  // Format currency
  const formatCurrency = (amount: number | null) => {
    if (amount === null) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Get rental cost type display
  const getRentalCostTypeDisplay = (type: string | null) => {
    if (!type) return '';

    switch (type) {
      case 'flat':
        return 'Flat Rate';
      case 'daily':
        return 'Per Day';
      case 'hourly':
        return 'Per Hour';
      case 'per_acre':
        return 'Per Acre';
      default:
        return type;
    }
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-blue-100 text-blue-800';
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-purple-100 text-purple-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Check if user is the owner
  const isOwner = sharingRecord && userFarms.includes(sharingRecord.owner_farm_id);

  // Check if user is the renter
  const isRenter = sharingRecord && userFarms.includes(sharingRecord.renter_farm_id);

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading...</p>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
        <div className="flex justify-center">
          <button
            onClick={() => navigate('/equipment-sharing')}
            className="bg-primary-500 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          >
            Back to Equipment Sharing
          </button>
        </div>
      </Layout>
    );
  }

  if (!sharingRecord) {
    return (
      <Layout>
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">Equipment sharing record not found.</span>
        </div>
        <div className="flex justify-center">
          <button
            onClick={() => navigate('/equipment-sharing')}
            className="bg-primary-500 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          >
            Back to Equipment Sharing
          </button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Equipment Sharing Details</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => navigate('/equipment-sharing')}
            className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          >
            Back
          </button>

          {sharingRecord.status === 'pending' && (
            <Link
              to={`/equipment-sharing/${id}/edit`}
              className="bg-primary-500 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            >
              Edit
            </Link>
          )}
        </div>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              {sharingRecord.Equipment.name}
            </h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              {sharingRecord.Equipment.manufacturer && <span className="mr-2">{sharingRecord.Equipment.manufacturer}</span>}
              {sharingRecord.Equipment.model && <span className="mr-2">Model: {sharingRecord.Equipment.model}</span>}
              {sharingRecord.Equipment.year && <span>Year: {sharingRecord.Equipment.year}</span>}
            </p>
          </div>
          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeColor(sharingRecord.status)}`}>
            {sharingRecord.status.charAt(0).toUpperCase() + sharingRecord.status.slice(1)}
          </span>
        </div>
        <div className="border-t border-gray-200">
          <dl>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Owner Farm</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {sharingRecord.OwnerFarm.name}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Renter Farm</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {sharingRecord.RenterFarm.name}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Rental Period</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {formatDate(sharingRecord.start_date)} to {formatDate(sharingRecord.end_date)}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Rental Cost</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {sharingRecord.rental_cost ? (
                  `${formatCurrency(sharingRecord.rental_cost)} (${getRentalCostTypeDisplay(sharingRecord.rental_cost_type)})`
                ) : (
                  'Not specified'
                )}
              </dd>
            </div>
            {sharingRecord.notes && (
              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Notes</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  {sharingRecord.notes}
                </dd>
              </div>
            )}
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Created</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {formatDate(sharingRecord.created_at)}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {formatDate(sharingRecord.updated_at)}
              </dd>
            </div>
          </dl>
        </div>

        {/* Action buttons based on status and user role */}
        <div className="px-4 py-5 sm:px-6 border-t border-gray-200">
          <div className="flex flex-wrap gap-2">
            {isOwner && sharingRecord.status === 'pending' && (
              <>
                <button
                  onClick={() => handleUpdateStatus('approved')}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  Approve Request
                </button>
                <button
                  onClick={() => handleUpdateStatus('rejected')}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  Reject Request
                </button>
              </>
            )}

            {isOwner && sharingRecord.status === 'approved' && (
              <button
                onClick={() => handleUpdateStatus('active')}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                Start Rental
              </button>
            )}

            {isOwner && sharingRecord.status === 'active' && (
              <button
                onClick={() => handleUpdateStatus('completed')}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
              >
                Complete Rental
              </button>
            )}

            {isRenter && sharingRecord.status === 'pending' && (
              <button
                onClick={() => handleUpdateStatus('cancelled')}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Cancel Request
              </button>
            )}

            {(isOwner || isRenter) && ['pending', 'cancelled', 'rejected'].includes(sharingRecord.status) && (
              <button
                onClick={handleDelete}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Delete
              </button>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default EquipmentSharingDetail;
