import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface Farm {
  id: string;
  name: string;
}

interface Equipment {
  id: string;
  name: string;
  type: string;
  manufacturer: string;
  model: string;
  year: number;
}

interface EquipmentSharing {
  id: string;
  equipment_id: string;
  owner_farm_id: string;
  renter_farm_id: string;
  start_date: string;
  end_date: string;
  rental_cost: number;
  rental_cost_type: string;
  status: string;
  notes: string;
  Equipment: Equipment;
  OwnerFarm: Farm;
  RenterFarm: Farm;
}

const EquipmentSharingList = () => {
  const [sharingRecords, setSharingRecords] = useState<EquipmentSharing[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedFarm, setSelectedFarm] = useState<string | null>(null);
  const [farms, setFarms] = useState<Farm[]>([]);
  const [viewMode, setViewMode] = useState<'all' | 'owned' | 'rented'>('all');

  const { user } = useContext(AuthContext);

  // Fetch farms
  useEffect(() => {
    const fetchFarms = async () => {
      try {
        const response = await axios.get(`${API_URL}/farms/user/${user?.id}`);
        // Check if response.data is an array or has a farms property
        setFarms(Array.isArray(response.data) ? response.data : response.data.farms || []);

        // Set the first farm as selected by default
        if ((Array.isArray(response.data) ? response.data : response.data.farms || []).length > 0 && !selectedFarm) {
          setSelectedFarm((Array.isArray(response.data) ? response.data : response.data.farms || [])[0].id);
        }
      } catch (err: any) {
        console.error('Error fetching farms:', err);
        setError('Failed to load farms. Please try again later.');
      }
    };

    if (user) {
      fetchFarms();
    }
  }, [user, selectedFarm]);

  // Fetch equipment sharing records for the selected farm
  useEffect(() => {
    const fetchSharingRecords = async () => {
      if (!selectedFarm) return;

      setLoading(true);
      setError(null);

      try {
        let endpoint = `${API_URL}/equipment-sharing/farm/${selectedFarm}`;

        if (viewMode === 'owned') {
          endpoint = `${API_URL}/equipment-sharing/owned/${selectedFarm}`;
        } else if (viewMode === 'rented') {
          endpoint = `${API_URL}/equipment-sharing/rented/${selectedFarm}`;
        }

        const response = await axios.get(endpoint);
        setSharingRecords(Array.isArray(response.data) ? response.data : response.data.sharingRecords || []);
      } catch (err: any) {
        console.error('Error fetching equipment sharing records:', err);
        setError('Failed to load equipment sharing records. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchSharingRecords();
  }, [selectedFarm, viewMode]);

  // Handle farm change
  const handleFarmChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedFarm(e.target.value);
  };

  // Handle view mode change
  const handleViewModeChange = (mode: 'all' | 'owned' | 'rented') => {
    setViewMode(mode);
  };

  // Handle equipment sharing deletion
  const handleDeleteSharing = async (sharingId: string) => {
    if (!window.confirm('Are you sure you want to delete this equipment sharing record?')) {
      return;
    }

    try {
      await axios.delete(`${API_URL}/equipment-sharing/${sharingId}`);
      setSharingRecords(sharingRecords.filter(record => record.id !== sharingId));
    } catch (err: any) {
      console.error('Error deleting equipment sharing record:', err);
      setError('Failed to delete equipment sharing record. Please try again later.');
    }
  };

  // Handle status update
  const handleUpdateStatus = async (sharingId: string, newStatus: string) => {
    try {
      await axios.patch(`${API_URL}/equipment-sharing/${sharingId}/status`, { status: newStatus });

      // Update the status in the local state
      setSharingRecords(sharingRecords.map(record => 
        record.id === sharingId ? { ...record, status: newStatus } : record
      ));
    } catch (err: any) {
      console.error('Error updating equipment sharing status:', err);
      setError('Failed to update status. Please try again later.');
    }
  };

  // Format currency
  const formatCurrency = (amount: number | null) => {
    if (amount === null) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-blue-100 text-blue-800';
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-purple-100 text-purple-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get rental cost type display
  const getRentalCostTypeDisplay = (type: string) => {
    switch (type) {
      case 'flat':
        return 'Flat Rate';
      case 'daily':
        return 'Per Day';
      case 'hourly':
        return 'Per Hour';
      case 'per_acre':
        return 'Per Acre';
      default:
        return type;
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Equipment Sharing</h1>
        <div className="flex space-x-2">
          <select
            className="block w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            value={selectedFarm || ''}
            onChange={handleFarmChange}
          >
            <option value="" disabled>Select a farm</option>
            {farms.map(farm => (
              <option key={farm.id} value={farm.id}>{farm.name}</option>
            ))}
          </select>

          <Link
            to="/equipment-sharing/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Share Equipment
          </Link>
        </div>
      </div>

      <div className="mb-6">
        <div className="flex border-b border-gray-200">
          <button
            className={`py-2 px-4 text-sm font-medium ${viewMode === 'all' ? 'border-b-2 border-primary-500 text-primary-600' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => handleViewModeChange('all')}
          >
            All Sharing Records
          </button>
          <button
            className={`py-2 px-4 text-sm font-medium ${viewMode === 'owned' ? 'border-b-2 border-primary-500 text-primary-600' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => handleViewModeChange('owned')}
          >
            Equipment I'm Sharing
          </button>
          <button
            className={`py-2 px-4 text-sm font-medium ${viewMode === 'rented' ? 'border-b-2 border-primary-500 text-primary-600' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => handleViewModeChange('rented')}
          >
            Equipment I'm Renting
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading equipment sharing records...</p>
        </div>
      ) : sharingRecords.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <p className="text-gray-500 mb-4">No equipment sharing records found.</p>
          <p className="text-sm text-gray-400 mb-6">
            {viewMode === 'all' && 'Share your equipment with other farms or rent equipment from them.'}
            {viewMode === 'owned' && 'Share your equipment with other farms to generate additional income.'}
            {viewMode === 'rented' && 'Rent equipment from other farms when you need it.'}
          </p>
          <Link
            to="/equipment-sharing/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            {viewMode === 'rented' ? 'Rent Equipment' : 'Share Equipment'}
          </Link>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {sharingRecords.map((record) => (
              <li key={record.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="ml-3">
                        <p className="text-sm font-medium text-primary-600 truncate">
                          {record.Equipment.name}
                        </p>
                        <p className="text-sm text-gray-500">
                          {record.Equipment.manufacturer && <span className="mr-2">{record.Equipment.manufacturer}</span>}
                          {record.Equipment.model && <span className="mr-2">Model: {record.Equipment.model}</span>}
                          {record.Equipment.year && <span>Year: {record.Equipment.year}</span>}
                        </p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Link
                        to={`/equipment-sharing/${record.id}`}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        View
                      </Link>
                      {record.status === 'pending' && (
                        <Link
                          to={`/equipment-sharing/${record.id}/edit`}
                          className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                        >
                          Edit
                        </Link>
                      )}
                      {record.owner_farm_id === selectedFarm && record.status === 'pending' && (
                        <>
                          <button
                            onClick={() => handleUpdateStatus(record.id, 'approved')}
                            className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                          >
                            Approve
                          </button>
                          <button
                            onClick={() => handleUpdateStatus(record.id, 'rejected')}
                            className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                          >
                            Reject
                          </button>
                        </>
                      )}
                      {record.owner_farm_id === selectedFarm && record.status === 'approved' && (
                        <button
                          onClick={() => handleUpdateStatus(record.id, 'active')}
                          className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                        >
                          Start Rental
                        </button>
                      )}
                      {record.owner_farm_id === selectedFarm && record.status === 'active' && (
                        <button
                          onClick={() => handleUpdateStatus(record.id, 'completed')}
                          className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-purple-700 bg-purple-100 hover:bg-purple-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                        >
                          Complete Rental
                        </button>
                      )}
                      {['pending', 'cancelled', 'rejected'].includes(record.status) && (
                        <button
                          onClick={() => handleDeleteSharing(record.id)}
                          className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        >
                          Delete
                        </button>
                      )}
                    </div>
                  </div>
                  <div className="mt-2 sm:flex sm:justify-between">
                    <div className="sm:flex">
                      <p className="flex items-center text-sm text-gray-500 mr-6">
                        <span>Owner: {record.OwnerFarm.name}</span>
                      </p>
                      <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 mr-6">
                        <span>Renter: {record.RenterFarm.name}</span>
                      </p>
                    </div>
                    <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeColor(record.status)}`}>
                        {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                      </span>
                    </div>
                  </div>
                  <div className="mt-2 sm:flex">
                    <p className="flex items-center text-sm text-gray-500 mr-6">
                      <span>Period: {formatDate(record.start_date)} - {formatDate(record.end_date)}</span>
                    </p>
                    {record.rental_cost && (
                      <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <span>Cost: {formatCurrency(record.rental_cost)} ({getRentalCostTypeDisplay(record.rental_cost_type)})</span>
                      </p>
                    )}
                  </div>
                  {record.notes && (
                    <div className="mt-2 text-sm text-gray-500">
                      <p>{record.notes}</p>
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </Layout>
  );
};

export default EquipmentSharingList;
