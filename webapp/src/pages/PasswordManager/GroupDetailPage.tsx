import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import PageHeader from '../../components/ui/PageHeader';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { Alert } from '../../components/ui/Alert';
import { Spinner } from '../../components/ui/Spinner';
import { Input } from '../../components/ui/Input';
import { FaLock, FaPlus, FaEdit, FaTrash, FaEye, FaEyeSlash, FaCopy, FaSearch } from 'react-icons/fa';
import MasterPasswordModal from './components/MasterPasswordModal';
import PasswordGeneratorModal from './components/PasswordGeneratorModal';
import Layout from '../../components/Layout';

interface Password {
  id: string;
  name: string;
  username: string;
  password: string;
  url: string;
  notes: string;
  has_2fa: boolean;
  totp_secret?: string;
  created_at: string;
  updated_at: string;
  decryption_failed?: boolean;
}

interface PasswordGroup {
  id: string;
  name: string;
  description: string;
  farm_id: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  permissions: any[];
}

const GroupDetailPage: React.FC = () => {
  const { groupId } = useParams<{ groupId: string }>();
  const { user } = useAuth();
  const { currentFarm } = useFarm();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [group, setGroup] = useState<PasswordGroup | null>(null);
  const [passwords, setPasswords] = useState<Password[]>([]);
  const [showMasterPasswordModal, setShowMasterPasswordModal] = useState(false);
  const [showPasswordGeneratorModal, setShowPasswordGeneratorModal] = useState(false);
  const [visiblePasswords, setVisiblePasswords] = useState<Record<string, boolean>>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredPasswords, setFilteredPasswords] = useState<Password[]>([]);

  useEffect(() => {
    // Filter passwords based on search query
    if (searchQuery.trim() === '') {
      setFilteredPasswords(passwords);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = passwords.filter(password => 
        password.name.toLowerCase().includes(query) ||
        password.username.toLowerCase().includes(query) ||
        password.url.toLowerCase().includes(query) ||
        password.notes.toLowerCase().includes(query)
      );
      setFilteredPasswords(filtered);
    }
  }, [searchQuery, passwords]);

  const fetchGroup = async () => {
    if (!groupId) return;

    try {
      const response = await fetch(`/api/password-manager/password-groups/${groupId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch password group');
      }

      const data = await response.json();
      setGroup(data.data);
    } catch (error) {
      console.error('Error fetching password group:', error);
      setError('Failed to load password group. Please try again.');
    }
  };

  const fetchPasswords = async (masterPassword: string) => {
    if (!groupId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/password-manager/password-groups/${groupId}/passwords`, {
        method: 'POST', // Using POST to send master password in body
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ masterPassword }),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch passwords');
      }

      const data = await response.json();
      setPasswords(data.data);
      setFilteredPasswords(data.data);
    } catch (error) {
      console.error('Error fetching passwords:', error);
      setError('Failed to load passwords. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleMasterPasswordSubmit = (masterPassword: string) => {
    setShowMasterPasswordModal(false);
    fetchPasswords(masterPassword);

    // Store master password in session storage (not ideal for production)
    // In a real implementation, we would use a more secure approach
    sessionStorage.setItem('masterPassword', masterPassword);
  };

  const togglePasswordVisibility = (id: string) => {
    setVisiblePasswords(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // Could add a toast notification here
  };

  const handleCreatePassword = () => {
    // Navigate to create password page or show modal
    // For now, just show the password generator modal
    setShowPasswordGeneratorModal(true);
  };

  const handleEditPassword = (id: string) => {
    // Navigate to edit password page or show modal
    console.log('Edit password:', id);
  };

  const handleDeletePassword = (id: string) => {
    // Show confirmation dialog and delete password
    console.log('Delete password:', id);
  };

  const handleBackToGroups = () => {
    navigate('/password-manager');
  };

  useEffect(() => {
    fetchGroup();

    // Check if master password is already in session storage
    const masterPassword = sessionStorage.getItem('masterPassword');
    if (masterPassword) {
      fetchPasswords(masterPassword);
    } else {
      setShowMasterPasswordModal(true);
    }
  }, [groupId]);

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
      <PageHeader
        title={group ? group.name : 'Password Group'}
        description={group ? group.description : 'Loading...'}
        icon={<FaLock className="text-primary" />}
        backLink="/password-manager"
        onBack={handleBackToGroups}
      />

      {error && (
        <Alert
          type="error"
          title="Error"
          message={error}
          className="mb-6"
        />
      )}

      <div className="flex justify-between items-center mb-6">
        <div className="relative w-full md:w-1/3">
          <Input
            type="text"
            placeholder="Search passwords..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        </div>
        <Button
          variant="primary"
          onClick={handleCreatePassword}
        >
          <FaPlus className="mr-2" /> Add Password
        </Button>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Spinner size="lg" />
        </div>
      ) : filteredPasswords.length === 0 ? (
        <Card className="p-8 text-center">
          <div className="text-gray-500 mb-4">
            <FaLock className="mx-auto text-5xl mb-4" />
            <h3 className="text-xl font-semibold mb-2">No Passwords Found</h3>
            <p>
              {searchQuery.trim() !== '' 
                ? 'No passwords match your search criteria.' 
                : 'Add your first password to this group.'}
            </p>
          </div>
          <Button
            variant="primary"
            onClick={handleCreatePassword}
            className="mt-4"
          >
            <FaPlus className="mr-2" /> Add Password
          </Button>
        </Card>
      ) : (
        <div className="grid grid-cols-1 gap-4">
          {filteredPasswords.map((password) => (
            <Card key={password.id} className="p-4">
              <div className="flex flex-col md:flex-row md:items-center justify-between">
                <div className="mb-2 md:mb-0">
                  <h3 className="text-lg font-semibold">{password.name}</h3>
                  <p className="text-gray-600 text-sm">{password.url}</p>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => handleEditPassword(password.id)}
                    title="Edit"
                  >
                    <FaEdit />
                  </Button>
                  <Button
                    variant="danger"
                    size="sm"
                    onClick={() => handleDeletePassword(password.id)}
                    title="Delete"
                  >
                    <FaTrash />
                  </Button>
                </div>
              </div>

              <div className="mt-4 space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Username:</span>
                  <div className="flex items-center">
                    <span className="text-sm mr-2">{password.username}</span>
                    <Button
                      variant="ghost"
                      size="xs"
                      onClick={() => copyToClipboard(password.username)}
                      title="Copy username"
                    >
                      <FaCopy />
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Password:</span>
                  <div className="flex items-center">
                    {password.decryption_failed ? (
                      <span className="text-sm text-red-500">Decryption failed</span>
                    ) : (
                      <>
                        <span className="text-sm mr-2">
                          {visiblePasswords[password.id] ? password.password : '••••••••••••'}
                        </span>
                        <Button
                          variant="ghost"
                          size="xs"
                          onClick={() => togglePasswordVisibility(password.id)}
                          title={visiblePasswords[password.id] ? "Hide password" : "Show password"}
                          className="mr-1"
                        >
                          {visiblePasswords[password.id] ? <FaEyeSlash /> : <FaEye />}
                        </Button>
                        <Button
                          variant="ghost"
                          size="xs"
                          onClick={() => copyToClipboard(password.password)}
                          title="Copy password"
                        >
                          <FaCopy />
                        </Button>
                      </>
                    )}
                  </div>
                </div>

                {password.notes && (
                  <div className="mt-2">
                    <span className="text-sm font-medium text-gray-700">Notes:</span>
                    <p className="text-sm mt-1 text-gray-600">{password.notes}</p>
                  </div>
                )}

                {password.has_2fa && (
                  <div className="mt-2">
                    <span className="text-sm font-medium text-gray-700 bg-yellow-100 px-2 py-1 rounded">
                      2FA Enabled
                    </span>
                  </div>
                )}
              </div>
            </Card>
          ))}
        </div>
      )}

      <MasterPasswordModal
        isOpen={showMasterPasswordModal}
        onClose={() => setShowMasterPasswordModal(false)}
        onSubmit={handleMasterPasswordSubmit}
      />

      <PasswordGeneratorModal
        isOpen={showPasswordGeneratorModal}
        onClose={() => setShowPasswordGeneratorModal(false)}
        onGenerated={(password) => {
          console.log('Generated password:', password);
          setShowPasswordGeneratorModal(false);
        }}
      />
      </div>
    </Layout>
  );
};

export default GroupDetailPage;
