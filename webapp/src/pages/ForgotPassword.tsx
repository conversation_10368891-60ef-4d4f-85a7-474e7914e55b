import { useState, useContext, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [submitted, setSubmitted] = useState(false);
  const { forgotPassword, loading, error, clearError, user } = useContext(AuthContext);
  const navigate = useNavigate();

  // Check if user is already logged in
  useEffect(() => {
    if (user) {
      // User is already logged in, redirect to dashboard
      navigate('/dashboard');
    }
  }, [user, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();

    try {
      await forgotPassword(email);
      setSubmitted(true);
    } catch (err) {
      // Error is handled by the context
      console.error('Password reset request error:', err);
    }
  };

  if (submitted) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-card">
          <div className="flex flex-col items-center">
            <img 
              src="/logo.svg" 
              alt="nxtAcre Logo"
              className="h-12 w-auto mb-4"
              onError={(e) => {
                // Fallback if logo doesn't exist
                const target = e.currentTarget;
                target.onerror = null;
                target.style.display = 'none';
                const parent = target.parentElement;
                if (parent) {
                  const span = document.createElement('span');
                  span.className = 'text-3xl font-display font-bold text-primary-600';
                  span.textContent = 'nxtAcre';
                  parent.appendChild(span);
                }
              }}
            />
            <h2 className="text-center text-2xl font-display font-bold text-gray-900">
              Check Your Email
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              If an account exists with the email {email}, we've sent instructions to reset your password.
            </p>
          </div>

          <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded-md" role="alert">
            <p className="font-bold">Email Sent</p>
            <p>Please check your email inbox and spam folder for the password reset link.</p>
          </div>

          <div className="text-center">
            <Link to="/login" className="font-medium text-primary-600 hover:text-primary-700">
              Return to login
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-card">
        <div className="flex flex-col items-center">
          <img 
            src="/logo.svg" 
            alt="nxtAcre Logo"
            className="h-12 w-auto mb-4"
            onError={(e) => {
              // Fallback if logo doesn't exist
              const target = e.currentTarget;
              target.onerror = null;
              target.style.display = 'none';
              const parent = target.parentElement;
              if (parent) {
                const span = document.createElement('span');
                span.className = 'text-3xl font-display font-bold text-primary-600';
                span.textContent = 'nxtAcre';
                parent.appendChild(span);
              }
            }}
          />
          <h2 className="text-center text-2xl font-display font-bold text-gray-900">
            Reset Your Password
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Enter your email address and we'll send you a link to reset your password
          </p>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-md" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        )}

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label htmlFor="email-address" className="block text-sm font-medium text-gray-700 mb-1">Email address</label>
              <input
                id="email-address"
                name="email"
                type="email"
                autoComplete="email"
                required
                className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={loading || !email}
              className={`btn btn-primary w-full ${(loading || !email) ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Sending...' : 'Send Reset Link'}
            </button>
          </div>

          <div className="text-center pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-600">
              Remember your password?{' '}
              <Link to="/login" className="font-medium text-primary-600 hover:text-primary-700">
                Sign in
              </Link>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ForgotPassword;
