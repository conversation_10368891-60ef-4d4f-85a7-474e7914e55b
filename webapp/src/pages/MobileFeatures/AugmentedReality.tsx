import React, { useState } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { Link } from 'react-router-dom';

const AugmentedReality = () => {
  const { user } = useAuth();
  const { currentFarm } = useFarm();
  const [activeTab, setActiveTab] = useState<string>('crop-health');
  const [demoActive, setDemoActive] = useState<boolean>(false);

  const handleStartDemo = () => {
    setDemoActive(true);
    
    // Automatically end demo after 10 seconds
    setTimeout(() => {
      setDemoActive(false);
    }, 10000);
  };

  return (
    <Layout>
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900">Augmented Reality</h1>
            <p className="mt-2 text-sm text-gray-700">
              Visualize field data while in the field using augmented reality. See crop health, soil data, and more overlaid on your camera view.
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <Link
              to="/mobile-features"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              All Mobile Features
            </Link>
          </div>
        </div>

        {/* Mobile App Download Section */}
        <div className="mt-8 bg-white shadow sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Mobile App Required</h3>
            <div className="mt-2 max-w-xl text-sm text-gray-500">
              <p>
                To use augmented reality features, you need to download the NxtAcre mobile app. The app is available for iOS and Android devices.
              </p>
            </div>
            <div className="mt-5 flex space-x-4">
              <a
                href="#"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 0C4.477 0 0 4.477 0 10c0 5.523 4.477 10 10 10s10-4.477 10-10C20 4.477 15.523 0 10 0zm5.5 15.5l-1.414 1.414L10 12.828l-4.086 4.086L4.5 15.5l4.086-4.086L4.5 7.328l1.414-1.414L10 9.172l4.086-4.086L15.5 6.5l-4.086 4.086L15.5 14.672z"/>
                </svg>
                Download for iOS
              </a>
              <a
                href="#"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 0C4.477 0 0 4.477 0 10c0 5.523 4.477 10 10 10s10-4.477 10-10C20 4.477 15.523 0 10 0zm5.5 15.5l-1.414 1.414L10 12.828l-4.086 4.086L4.5 15.5l4.086-4.086L4.5 7.328l1.414-1.414L10 9.172l4.086-4.086L15.5 6.5l-4.086 4.086L15.5 14.672z"/>
                </svg>
                Download for Android
              </a>
            </div>
          </div>
        </div>

        {/* AR Demo Section */}
        <div className="mt-8 bg-white shadow sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Augmented Reality Demo</h3>
            <div className="mt-2 max-w-xl text-sm text-gray-500">
              <p>
                Experience a preview of the augmented reality features available in the mobile app. In the actual app, these overlays will appear on your camera view in real-time.
              </p>
            </div>
            <div className="mt-5">
              <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <div className="flex flex-col items-center">
                  {!demoActive ? (
                    <>
                      <div className="relative w-full max-w-2xl h-64 sm:h-96 bg-gray-200 rounded-lg overflow-hidden">
                        <img 
                          src="https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" 
                          alt="Field view" 
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="text-center p-4 bg-black bg-opacity-50 rounded-lg text-white">
                            <p className="text-lg font-medium">Tap to start AR demo</p>
                            <p className="text-sm">See how field data appears in augmented reality</p>
                          </div>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={handleStartDemo}
                        className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Start AR Demo
                      </button>
                    </>
                  ) : (
                    <div className="relative w-full max-w-2xl h-64 sm:h-96 bg-gray-200 rounded-lg overflow-hidden">
                      <img 
                        src="https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" 
                        alt="Field view" 
                        className="w-full h-full object-cover"
                      />
                      {/* AR Overlay Elements */}
                      <div className="absolute inset-0">
                        {/* Crop Health Indicators */}
                        {activeTab === 'crop-health' && (
                          <>
                            <div className="absolute top-1/4 left-1/4 bg-green-500 bg-opacity-30 rounded-full w-16 h-16 border-2 border-green-500 flex items-center justify-center">
                              <span className="text-white font-bold text-sm">95%</span>
                            </div>
                            <div className="absolute top-1/3 right-1/3 bg-yellow-500 bg-opacity-30 rounded-full w-16 h-16 border-2 border-yellow-500 flex items-center justify-center">
                              <span className="text-white font-bold text-sm">78%</span>
                            </div>
                            <div className="absolute bottom-1/4 right-1/4 bg-red-500 bg-opacity-30 rounded-full w-16 h-16 border-2 border-red-500 flex items-center justify-center">
                              <span className="text-white font-bold text-sm">45%</span>
                            </div>
                            <div className="absolute top-10 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-70 px-4 py-2 rounded-lg">
                              <span className="text-white text-sm">Crop Health View</span>
                            </div>
                          </>
                        )}
                        
                        {/* Soil Data Indicators */}
                        {activeTab === 'soil-data' && (
                          <>
                            <div className="absolute top-1/4 left-1/4 bg-blue-500 bg-opacity-30 rounded-lg p-2 border-2 border-blue-500">
                              <span className="text-white text-xs font-bold">pH: 6.8</span>
                            </div>
                            <div className="absolute top-1/3 right-1/3 bg-purple-500 bg-opacity-30 rounded-lg p-2 border-2 border-purple-500">
                              <span className="text-white text-xs font-bold">N: 45 ppm</span>
                            </div>
                            <div className="absolute bottom-1/4 right-1/4 bg-orange-500 bg-opacity-30 rounded-lg p-2 border-2 border-orange-500">
                              <span className="text-white text-xs font-bold">K: 120 ppm</span>
                            </div>
                            <div className="absolute top-10 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-70 px-4 py-2 rounded-lg">
                              <span className="text-white text-sm">Soil Data View</span>
                            </div>
                          </>
                        )}
                        
                        {/* Irrigation Indicators */}
                        {activeTab === 'irrigation' && (
                          <>
                            <div className="absolute top-1/4 left-1/4 bg-blue-500 bg-opacity-30 rounded-full w-20 h-20 border-2 border-blue-500 flex flex-col items-center justify-center">
                              <span className="text-white font-bold text-sm">Zone 1</span>
                              <span className="text-white text-xs">Active</span>
                            </div>
                            <div className="absolute top-1/3 right-1/3 bg-blue-300 bg-opacity-30 rounded-full w-20 h-20 border-2 border-blue-300 flex flex-col items-center justify-center">
                              <span className="text-white font-bold text-sm">Zone 2</span>
                              <span className="text-white text-xs">Scheduled</span>
                            </div>
                            <div className="absolute bottom-1/4 right-1/4 bg-gray-500 bg-opacity-30 rounded-full w-20 h-20 border-2 border-gray-500 flex flex-col items-center justify-center">
                              <span className="text-white font-bold text-sm">Zone 3</span>
                              <span className="text-white text-xs">Off</span>
                            </div>
                            <div className="absolute top-10 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-70 px-4 py-2 rounded-lg">
                              <span className="text-white text-sm">Irrigation View</span>
                            </div>
                          </>
                        )}
                        
                        {/* Field Boundaries */}
                        {activeTab === 'boundaries' && (
                          <>
                            <svg className="absolute inset-0 w-full h-full" xmlns="http://www.w3.org/2000/svg">
                              <path 
                                d="M100,50 L300,80 L400,200 L350,300 L200,350 L50,250 Z" 
                                fill="rgba(59, 130, 246, 0.2)" 
                                stroke="rgb(59, 130, 246)" 
                                strokeWidth="3"
                              />
                              <text x="200" y="200" fill="white" fontWeight="bold" fontSize="14" textAnchor="middle">North Field</text>
                            </svg>
                            <div className="absolute top-10 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-70 px-4 py-2 rounded-lg">
                              <span className="text-white text-sm">Field Boundaries View</span>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                
                {/* AR View Selector Tabs */}
                <div className="mt-6">
                  <div className="sm:hidden">
                    <label htmlFor="ar-view-tabs" className="sr-only">Select AR View</label>
                    <select
                      id="ar-view-tabs"
                      name="ar-view-tabs"
                      className="block w-full rounded-md border-gray-300 focus:border-primary-500 focus:ring-primary-500"
                      value={activeTab}
                      onChange={(e) => setActiveTab(e.target.value)}
                    >
                      <option value="crop-health">Crop Health</option>
                      <option value="soil-data">Soil Data</option>
                      <option value="irrigation">Irrigation</option>
                      <option value="boundaries">Field Boundaries</option>
                    </select>
                  </div>
                  <div className="hidden sm:block">
                    <div className="border-b border-gray-200">
                      <nav className="-mb-px flex space-x-8" aria-label="Tabs">
                        <button
                          onClick={() => setActiveTab('crop-health')}
                          className={`${
                            activeTab === 'crop-health'
                              ? 'border-primary-500 text-primary-600'
                              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                          } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                        >
                          Crop Health
                        </button>
                        <button
                          onClick={() => setActiveTab('soil-data')}
                          className={`${
                            activeTab === 'soil-data'
                              ? 'border-primary-500 text-primary-600'
                              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                          } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                        >
                          Soil Data
                        </button>
                        <button
                          onClick={() => setActiveTab('irrigation')}
                          className={`${
                            activeTab === 'irrigation'
                              ? 'border-primary-500 text-primary-600'
                              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                          } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                        >
                          Irrigation
                        </button>
                        <button
                          onClick={() => setActiveTab('boundaries')}
                          className={`${
                            activeTab === 'boundaries'
                              ? 'border-primary-500 text-primary-600'
                              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                          } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                        >
                          Field Boundaries
                        </button>
                      </nav>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* AR Features Section */}
        <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Augmented Reality Features</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Features available in the augmented reality view of the mobile app.
            </p>
          </div>
          <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-2">
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 bg-green-100 rounded-md p-3">
                      <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                      </svg>
                    </div>
                    <div className="ml-5">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">Crop Health Visualization</h3>
                      <div className="mt-2 text-sm text-gray-500">
                        See real-time crop health indicators overlaid on your camera view. Identify problem areas quickly.
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 bg-blue-100 rounded-md p-3">
                      <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                      </svg>
                    </div>
                    <div className="ml-5">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">Soil Data Overlay</h3>
                      <div className="mt-2 text-sm text-gray-500">
                        View soil test results, moisture levels, and nutrient information directly overlaid on the field.
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 bg-indigo-100 rounded-md p-3">
                      <svg className="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                      </svg>
                    </div>
                    <div className="ml-5">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">Field Boundary Visualization</h3>
                      <div className="mt-2 text-sm text-gray-500">
                        See field boundaries and zones overlaid on your camera view for better navigation and management.
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 bg-purple-100 rounded-md p-3">
                      <svg className="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div className="ml-5">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">Task Visualization</h3>
                      <div className="mt-2 text-sm text-gray-500">
                        See pending tasks and their locations overlaid on your camera view for efficient task management.
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default AugmentedReality;