import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface InventoryItem {
  id: string;
  farm_id: string;
  farm_name: string;
  name: string;
  category: string;
  quantity: number;
  unit: string;
  unit_price: number | null;
  supplier: string;
  purchase_date: string;
  expiration_date: string;
  location: string;
  minimum_quantity: number | null;
  created_at: string;
  updated_at: string;
}

const InventoryList = () => {
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  // Fetch inventory items
  useEffect(() => {
    const fetchInventoryItems = async () => {
      setLoading(true);
      setError(null);

      try {
        let url = `${API_URL}/api/inventory`;
        const params: Record<string, string> = {};

        // Use the global farm selector
        if (currentFarm) {
          params.farmId = currentFarm.id;
        }

        if (selectedCategory !== 'all') {
          params.category = selectedCategory;
        }

        // Add query parameters if any filters are applied
        if (Object.keys(params).length > 0) {
          const queryString = new URLSearchParams(params).toString();
          url = `${url}?${queryString}`;
        }

        const response = await axios.get(url);
        setInventoryItems(response.data);
      } catch (err: any) {
        console.error('Error fetching inventory items:', err);
        setError('Failed to load inventory items. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchInventoryItems();
  }, [currentFarm, selectedCategory]);

  // Farm filtering is now handled by the global farm selector in the header

  // Handle category filter change
  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCategory(e.target.value);
  };

  // Format category for display
  const formatCategory = (category: string) => {
    if (!category) return 'N/A';
    return category.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };

  // Format price for display
  const formatPrice = (price: number | null) => {
    if (price === null) return 'N/A';
    return `$${price.toFixed(2)}`;
  };

  // Check if item is low on stock
  const isLowStock = (item: InventoryItem) => {
    if (item.minimum_quantity === null) return false;
    return item.quantity <= item.minimum_quantity;
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Inventory</h1>
        <Link
          to="/inventory/new"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Add New Item
        </Link>
      </div>

      {/* Filters */}
      <div className="mb-6">
        {/* Note: Farm filter removed - using global farm selector in header */}

        {/* Category Filter */}
        <div>
          <label htmlFor="category-filter" className="block text-sm font-medium text-gray-700 mb-1">
            Filter by Category
          </label>
          <select
            id="category-filter"
            value={selectedCategory}
            onChange={handleCategoryChange}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          >
            <option value="all">All Categories</option>
            <option value="seed">Seeds</option>
            <option value="fertilizer">Fertilizers</option>
            <option value="pesticide">Pesticides</option>
            <option value="herbicide">Herbicides</option>
            <option value="feed">Animal Feed</option>
            <option value="medicine">Animal Medicine</option>
            <option value="fuel">Fuel</option>
            <option value="equipment_parts">Equipment Parts</option>
            <option value="supplies">General Supplies</option>
            <option value="other">Other</option>
          </select>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        </div>
      ) : inventoryItems.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-6 text-center">
          <p className="text-gray-500">No inventory items found. Add your first item to get started.</p>
          <Link
            to="/inventory/new"
            className="inline-flex items-center px-4 py-2 mt-4 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add New Item
          </Link>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {inventoryItems.map(item => (
              <li key={item.id}>
                <Link to={`/inventory/${item.id}`} className="block hover:bg-gray-50">
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <p className="text-sm font-medium text-primary-600 truncate">{item.name}</p>
                        {isLowStock(item) && (
                          <span className="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                            Low Stock
                          </span>
                        )}
                      </div>
                      <div className="ml-2 flex-shrink-0 flex">
                        <Link
                          to={`/inventory/${item.id}/edit`}
                          className="ml-2 inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                          onClick={(e) => e.stopPropagation()}
                        >
                          Edit
                        </Link>
                      </div>
                    </div>
                    <div className="mt-2 sm:flex sm:justify-between">
                      <div className="sm:flex">
                        <p className="flex items-center text-sm text-gray-500">
                          Farm: {item.farm_name}
                        </p>
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                          Category: {formatCategory(item.category)}
                        </p>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <p>
                          Quantity: {item.quantity} {item.unit} {item.unit_price !== null && `(${formatPrice(item.unit_price)}/${item.unit})`}
                        </p>
                      </div>
                    </div>
                  </div>
                </Link>
              </li>
            ))}
          </ul>
        </div>
      )}
    </Layout>
  );
};

export default InventoryList;
