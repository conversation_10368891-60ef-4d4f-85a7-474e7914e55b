import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate, useLocation } from 'react-router-dom';
import axios from 'axios';
import { API_URL } from '../../../config';
import { loadStripe } from '@stripe/stripe-js';

// Initialize Stripe with the publishable key from environment variables
const stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY || '');

interface Invoice {
  id: string;
  invoice_number: string;
  total_amount: number;
  status: string;
  farm_name: string;
}

const CustomerPaymentConfirmation: React.FC = () => {
  const { invoiceId } = useParams<{ invoiceId: string }>();
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [paymentStatus, setPaymentStatus] = useState<'processing' | 'succeeded' | 'failed'>('processing');
  const [transactionId, setTransactionId] = useState<string | null>(null);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const checkPaymentStatus = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Extract payment intent ID from URL query parameters
        const queryParams = new URLSearchParams(location.search);
        const paymentIntentId = queryParams.get('payment_intent');
        
        if (!paymentIntentId) {
          setError('Invalid or missing payment information');
          setPaymentStatus('failed');
          setLoading(false);
          return;
        }

        const token = localStorage.getItem('customerToken');
        if (!token) {
          navigate('/customer/login');
          return;
        }
        
        // Fetch invoice details
        const invoiceResponse = await axios.get(`${API_URL}/customer/invoices/${invoiceId}`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        
        setInvoice(invoiceResponse.data.invoice);
        
        // Check payment status
        const paymentStatusResponse = await axios.get(
          `${API_URL}/customer/payment-status/${paymentIntentId}`,
          {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        );
        
        const { status, transaction_id } = paymentStatusResponse.data;
        setPaymentStatus(status);
        setTransactionId(transaction_id);
        
        // If payment is still processing, poll for updates
        if (status === 'processing') {
          const pollInterval = setInterval(async () => {
            try {
              const statusResponse = await axios.get(
                `${API_URL}/customer/payment-status/${paymentIntentId}`,
                {
                  headers: {
                    Authorization: `Bearer ${token}`
                  }
                }
              );
              
              const { status: newStatus, transaction_id: newTransactionId } = statusResponse.data;
              setPaymentStatus(newStatus);
              setTransactionId(newTransactionId);
              
              if (newStatus !== 'processing') {
                clearInterval(pollInterval);
              }
            } catch (err) {
              console.error('Error polling payment status:', err);
              clearInterval(pollInterval);
            }
          }, 5000); // Poll every 5 seconds
          
          // Clean up interval on component unmount
          return () => clearInterval(pollInterval);
        }
      } catch (err: any) {
        console.error('Error checking payment status:', err);
        setError(err.response?.data?.error || 'Failed to check payment status. Please contact support.');
        setPaymentStatus('failed');
        
        // If unauthorized, redirect to login
        if (err.response?.status === 401) {
          localStorage.removeItem('customerToken');
          navigate('/customer/login');
        }
      } finally {
        setLoading(false);
      }
    };

    if (invoiceId) {
      checkPaymentStatus();
    }
  }, [invoiceId, navigate, location]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-6">
          <Link to="/customer/invoices" className="text-primary-600 hover:text-primary-900 flex items-center">
            <svg className="h-5 w-5 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            Back to Invoices
          </Link>
        </div>

        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Payment Confirmation
            </h3>
            {invoice && (
              <p className="mt-1 max-w-2xl text-sm text-gray-500">
                Invoice #{invoice.invoice_number} from {invoice.farm_name}
              </p>
            )}
          </div>

          <div className="px-4 py-5 sm:px-6">
            {error && (
              <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {loading ? (
              <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                <span className="ml-2 text-sm text-gray-500">Checking payment status...</span>
              </div>
            ) : (
              <div className="text-center py-8">
                {paymentStatus === 'processing' && (
                  <>
                    <div className="animate-pulse flex flex-col items-center justify-center">
                      <svg className="h-16 w-16 text-yellow-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <h2 className="mt-4 text-lg font-medium text-gray-900">Payment Processing</h2>
                    </div>
                    <p className="mt-2 text-sm text-gray-500">
                      Your payment is being processed. This may take a few minutes for ACH payments.
                    </p>
                    <p className="mt-1 text-sm text-gray-500">
                      You can leave this page and check your invoice status later.
                    </p>
                  </>
                )}

                {paymentStatus === 'succeeded' && (
                  <>
                    <div className="flex flex-col items-center justify-center">
                      <svg className="h-16 w-16 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <h2 className="mt-4 text-lg font-medium text-gray-900">Payment Successful</h2>
                    </div>
                    <p className="mt-2 text-sm text-gray-500">
                      Your payment has been processed successfully.
                    </p>
                    {invoice && (
                      <p className="mt-1 text-sm text-gray-500">
                        Amount: {formatCurrency(invoice.total_amount)}
                      </p>
                    )}
                    {transactionId && (
                      <p className="mt-1 text-sm text-gray-500">
                        Transaction ID: {transactionId}
                      </p>
                    )}
                    <div className="mt-6">
                      <Link
                        to={`/customer/invoices/${invoiceId}`}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        View Invoice
                      </Link>
                    </div>
                  </>
                )}

                {paymentStatus === 'failed' && (
                  <>
                    <div className="flex flex-col items-center justify-center">
                      <svg className="h-16 w-16 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <h2 className="mt-4 text-lg font-medium text-gray-900">Payment Failed</h2>
                    </div>
                    <p className="mt-2 text-sm text-gray-500">
                      There was an issue processing your payment. Please try again or contact support.
                    </p>
                    <div className="mt-6 space-y-2">
                      <Link
                        to={`/customer/invoices/${invoiceId}/pay`}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Try Again
                      </Link>
                      <div>
                        <Link
                          to={`/customer/invoices/${invoiceId}`}
                          className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                        >
                          Back to Invoice
                        </Link>
                      </div>
                    </div>
                  </>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerPaymentConfirmation;