import axios from 'axios';
import { API_URL } from '../config';

// Types for soil data
export interface SoilProperty {
  ph: number | null;
  organicMatter: number | null;
  cec: number | null;
  clayContent: number | null;
  sandContent: number | null;
  siltContent: number | null;
  drainageClass: string;
  erosionClass: string;
  floodFrequency: string;
  depthToWaterTable: number | null;
  availableWaterCapacity: number | null;
}

export interface SoilSuitability {
  cropProduction: string;
  grazing: string;
  irrigation: string;
}

export interface SoilHorizon {
  name: string;
  depth_upper: number;
  depth_lower: number;
  texture: string;
  ph: number | null;
  organic_matter: number | null;
}

export interface RainfallData {
  historical: {
    time: string;
    amount: number;
  }[];
  forecast: {
    date: string;
    precipitation: number;
    probability: number;
  }[];
  total: number;
  average: number;
}

export interface SoilData {
  soilType: string;
  soilSeries: string;
  soilComponents: any[];
  soilProperties: SoilProperty;
  soilHorizons: SoilHorizon[];
  soilLimitations: string[];
  soilSuitability: SoilSuitability;
  rainfall: RainfallData;
}

export interface SoilRecommendation {
  type: string;
  issue: string;
  recommendation: string;
  priority: 'High' | 'Medium' | 'Low';
}

export interface SoilDataResponse {
  soilData: SoilData;
  recommendations: SoilRecommendation[];
}

/**
 * Get detailed soil information for a field
 * @param fieldId The field ID
 * @returns Promise<SoilDataResponse> Soil data and recommendations for the field
 */
export const getFieldSoilData = async (fieldId: string): Promise<SoilDataResponse> => {
  try {
    const token = localStorage.getItem('token');
    const response = await axios.get(`${API_URL}/soil-data/field/${fieldId}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching field soil data:', error);
    // Return mock data as fallback
    return getMockSoilData();
  }
};

/**
 * Get detailed soil information for a farm
 * @param farmId The farm ID
 * @returns Promise<SoilDataResponse> Soil data and recommendations for the farm
 */
export const getFarmSoilData = async (farmId: string): Promise<SoilDataResponse> => {
  try {
    const token = localStorage.getItem('token');
    const response = await axios.get(`${API_URL}/soil-data/farm/${farmId}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching farm soil data:', error);
    // Return mock data as fallback
    return getMockSoilData();
  }
};

/**
 * Get detailed soil information for a soil sample
 * @param sampleId The soil sample ID
 * @returns Promise<SoilDataResponse> Soil data and recommendations for the soil sample
 */
export const getSoilSampleData = async (sampleId: string): Promise<SoilDataResponse> => {
  try {
    const token = localStorage.getItem('token');
    const response = await axios.get(`${API_URL}/soil-data/sample/${sampleId}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching soil sample data:', error);
    // Return mock data as fallback
    return getMockSoilData();
  }
};

/**
 * Get mock soil data for fallback
 * @returns SoilDataResponse Mock soil data and recommendations
 */
const getMockSoilData = (): SoilDataResponse => {
  return {
    soilData: {
      soilType: 'Fine-loamy, mixed, superactive, mesic Typic Hapludolls',
      soilSeries: 'Clarion',
      soilComponents: [],
      soilProperties: {
        ph: 6.5,
        organicMatter: 3.2,
        cec: 18.5,
        clayContent: 22,
        sandContent: 35,
        siltContent: 43,
        drainageClass: 'Well drained',
        erosionClass: 'Slight',
        floodFrequency: 'None',
        depthToWaterTable: 120,
        availableWaterCapacity: 0.18
      },
      soilHorizons: [
        {
          name: 'A',
          depth_upper: 0,
          depth_lower: 20,
          texture: 'Loam',
          ph: 6.5,
          organic_matter: 3.5
        },
        {
          name: 'B',
          depth_upper: 20,
          depth_lower: 60,
          texture: 'Clay loam',
          ph: 6.7,
          organic_matter: 1.8
        },
        {
          name: 'C',
          depth_upper: 60,
          depth_lower: 150,
          texture: 'Sandy clay loam',
          ph: 7.0,
          organic_matter: 0.5
        }
      ],
      soilLimitations: [],
      soilSuitability: {
        cropProduction: 'High',
        grazing: 'High',
        irrigation: 'High'
      },
      rainfall: {
        historical: Array.from({ length: 5 }, (_, i) => {
          const date = new Date();
          date.setDate(date.getDate() - (5 - i));
          return {
            time: date.toISOString(),
            amount: Math.random() * 0.5
          };
        }),
        forecast: Array.from({ length: 7 }, (_, i) => {
          const date = new Date();
          date.setDate(date.getDate() + i);
          return {
            date: date.toISOString().split('T')[0],
            precipitation: Math.random() * 0.7,
            probability: Math.random() * 100
          };
        }),
        total: 1.2,
        average: 0.24
      }
    },
    recommendations: [
      {
        type: 'Soil Type',
        issue: 'Loam soil',
        recommendation: 'Maintain organic matter levels to preserve good soil structure. Suitable for most crops.',
        priority: 'Low'
      },
      {
        type: 'Water',
        issue: 'Moderate rainfall',
        recommendation: 'Current rainfall levels are adequate for most crops. Monitor soil moisture during dry periods.',
        priority: 'Medium'
      }
    ]
  };
};

/**
 * Get soil health score based on soil properties
 * @param soilData The soil data
 * @returns number Soil health score (0-100)
 */
export const calculateSoilHealthScore = (soilData: SoilData): number => {
  let score = 0;
  let factors = 0;
  
  // pH factor (optimal range 6.0-7.0)
  if (soilData.soilProperties.ph !== null) {
    const ph = soilData.soilProperties.ph;
    if (ph >= 6.0 && ph <= 7.0) {
      score += 20;
    } else if (ph >= 5.5 && ph < 6.0) {
      score += 15;
    } else if (ph > 7.0 && ph <= 7.5) {
      score += 15;
    } else if (ph >= 5.0 && ph < 5.5) {
      score += 10;
    } else if (ph > 7.5 && ph <= 8.0) {
      score += 10;
    } else {
      score += 5;
    }
    factors++;
  }
  
  // Organic matter factor (higher is better, optimal > 3%)
  if (soilData.soilProperties.organicMatter !== null) {
    const om = soilData.soilProperties.organicMatter;
    if (om >= 4) {
      score += 20;
    } else if (om >= 3 && om < 4) {
      score += 15;
    } else if (om >= 2 && om < 3) {
      score += 10;
    } else if (om >= 1 && om < 2) {
      score += 5;
    } else {
      score += 0;
    }
    factors++;
  }
  
  // CEC factor (higher is better for nutrient retention)
  if (soilData.soilProperties.cec !== null) {
    const cec = soilData.soilProperties.cec;
    if (cec >= 20) {
      score += 20;
    } else if (cec >= 15 && cec < 20) {
      score += 15;
    } else if (cec >= 10 && cec < 15) {
      score += 10;
    } else if (cec >= 5 && cec < 10) {
      score += 5;
    } else {
      score += 0;
    }
    factors++;
  }
  
  // Texture factor (loam is ideal)
  if (soilData.soilProperties.clayContent !== null && 
      soilData.soilProperties.sandContent !== null && 
      soilData.soilProperties.siltContent !== null) {
    
    const clay = soilData.soilProperties.clayContent;
    const sand = soilData.soilProperties.sandContent;
    const silt = soilData.soilProperties.siltContent;
    
    // Check if it's close to loam texture (ideal: ~20% clay, ~40% silt, ~40% sand)
    const clayDiff = Math.abs(clay - 20);
    const siltDiff = Math.abs(silt - 40);
    const sandDiff = Math.abs(sand - 40);
    
    const totalDiff = clayDiff + siltDiff + sandDiff;
    
    if (totalDiff <= 15) {
      score += 20;
    } else if (totalDiff <= 30) {
      score += 15;
    } else if (totalDiff <= 45) {
      score += 10;
    } else if (totalDiff <= 60) {
      score += 5;
    } else {
      score += 0;
    }
    factors++;
  }
  
  // Drainage factor
  if (soilData.soilProperties.drainageClass) {
    const drainage = soilData.soilProperties.drainageClass.toLowerCase();
    if (drainage.includes('well drained')) {
      score += 20;
    } else if (drainage.includes('moderately well drained')) {
      score += 15;
    } else if (drainage.includes('somewhat poorly drained')) {
      score += 10;
    } else if (drainage.includes('poorly drained')) {
      score += 5;
    } else if (drainage.includes('very poorly drained')) {
      score += 0;
    } else {
      score += 10; // Default for unknown
    }
    factors++;
  }
  
  // Calculate final score (average of all factors, scaled to 0-100)
  return factors > 0 ? Math.round((score / (factors * 20)) * 100) : 50;
};

/**
 * Get crop recommendations based on soil data
 * @param soilData The soil data
 * @returns Array of recommended crops with suitability scores
 */
export const getCropRecommendations = (soilData: SoilData): { crop: string; suitability: number; notes: string }[] => {
  const recommendations = [];
  
  // Get soil properties
  const ph = soilData.soilProperties.ph || 7.0;
  const drainage = soilData.soilProperties.drainageClass.toLowerCase();
  const organicMatter = soilData.soilProperties.organicMatter || 2.0;
  const texture = soilData.soilType.toLowerCase();
  
  // Corn
  let cornSuitability = 0;
  if (ph >= 5.8 && ph <= 7.0) cornSuitability += 30;
  else if (ph >= 5.5 && ph < 5.8) cornSuitability += 20;
  else if (ph > 7.0 && ph <= 7.5) cornSuitability += 20;
  else cornSuitability += 10;
  
  if (drainage.includes('well drained')) cornSuitability += 30;
  else if (drainage.includes('moderately')) cornSuitability += 25;
  else cornSuitability += 15;
  
  if (organicMatter >= 3) cornSuitability += 20;
  else if (organicMatter >= 2) cornSuitability += 15;
  else cornSuitability += 10;
  
  if (texture.includes('loam')) cornSuitability += 20;
  else if (texture.includes('silt')) cornSuitability += 15;
  else cornSuitability += 10;
  
  recommendations.push({
    crop: 'Corn',
    suitability: cornSuitability,
    notes: cornSuitability >= 70 ? 'Excellent soil conditions for corn' : 'Consider soil amendments to improve corn yield'
  });
  
  // Soybeans
  let soybeanSuitability = 0;
  if (ph >= 6.0 && ph <= 7.0) soybeanSuitability += 30;
  else if (ph >= 5.5 && ph < 6.0) soybeanSuitability += 25;
  else if (ph > 7.0 && ph <= 7.5) soybeanSuitability += 20;
  else soybeanSuitability += 10;
  
  if (drainage.includes('well drained')) soybeanSuitability += 30;
  else if (drainage.includes('moderately')) soybeanSuitability += 25;
  else soybeanSuitability += 15;
  
  if (organicMatter >= 3) soybeanSuitability += 20;
  else if (organicMatter >= 2) soybeanSuitability += 15;
  else soybeanSuitability += 10;
  
  if (texture.includes('loam')) soybeanSuitability += 20;
  else if (texture.includes('clay')) soybeanSuitability += 15;
  else soybeanSuitability += 10;
  
  recommendations.push({
    crop: 'Soybeans',
    suitability: soybeanSuitability,
    notes: soybeanSuitability >= 70 ? 'Excellent soil conditions for soybeans' : 'Consider soil amendments to improve soybean yield'
  });
  
  // Wheat
  let wheatSuitability = 0;
  if (ph >= 6.0 && ph <= 7.5) wheatSuitability += 30;
  else if (ph >= 5.5 && ph < 6.0) wheatSuitability += 25;
  else if (ph > 7.5 && ph <= 8.0) wheatSuitability += 20;
  else wheatSuitability += 10;
  
  if (drainage.includes('well drained')) wheatSuitability += 25;
  else if (drainage.includes('moderately')) wheatSuitability += 30; // Wheat can handle slightly wetter soils
  else wheatSuitability += 20;
  
  if (organicMatter >= 2) wheatSuitability += 20;
  else if (organicMatter >= 1) wheatSuitability += 15;
  else wheatSuitability += 10;
  
  if (texture.includes('loam')) wheatSuitability += 20;
  else if (texture.includes('clay')) wheatSuitability += 15;
  else wheatSuitability += 10;
  
  recommendations.push({
    crop: 'Wheat',
    suitability: wheatSuitability,
    notes: wheatSuitability >= 70 ? 'Excellent soil conditions for wheat' : 'Consider soil amendments to improve wheat yield'
  });
  
  // Sort by suitability (highest first)
  return recommendations.sort((a, b) => b.suitability - a.suitability);
};