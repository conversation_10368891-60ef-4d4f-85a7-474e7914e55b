import axios from 'axios';
import { API_URL } from '../config';

export interface FarmSwitchResponse {
  success: boolean;
  farmId: string;
  farmName: string;
  farmSubdomain: string;
  message: string;
}

/**
 * Switch the active farm for the authenticated user
 * @param farmId The ID of the farm to switch to
 * @returns Promise<FarmSwitchResponse> The farm switch response
 */
export const switchActiveFarm = async (farmId: string): Promise<FarmSwitchResponse> => {
  try {
    const response = await axios.post(`${API_URL}/auth/switch-farm`, { farmId });
    return response.data;
  } catch (error) {
    console.error('Error switching farm:', error);
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data?.error || 'Failed to switch farm');
    }
    throw new Error('Failed to switch farm');
  }
};

/**
 * Get the current subdomain from the window location
 * @returns string | null The current subdomain or null if not on a subdomain
 */
export const getCurrentSubdomain = (): string | null => {
  const hostname = window.location.hostname;
  const parts = hostname.split('.');
  
  if (parts.length > 2) {
    return parts[0];
  }
  
  return null;
};

/**
 * Get the main domain from environment variables
 * @returns string The main domain
 */
export const getMainDomain = (): string => {
  return import.meta.env.VITE_MAIN_DOMAIN || 'nxtacre.com';
};

/**
 * Redirect to a specific subdomain while preserving the current path
 * @param subdomain The subdomain to redirect to
 * @param path Optional path to redirect to (defaults to current path)
 */
export const redirectToSubdomain = (subdomain: string, path?: string): void => {
  const mainDomain = getMainDomain();
  const currentPath = path || window.location.pathname;
  const protocol = window.location.protocol;
  
  window.location.href = `${protocol}//${subdomain}.${mainDomain}${currentPath}`;
};

/**
 * Check if the user should be redirected to a different subdomain
 * @param targetSubdomain The subdomain the user should be on
 * @param isGlobalAdmin Whether the user is a global admin
 * @returns boolean True if a redirect is needed
 */
export const shouldRedirectToSubdomain = (
  targetSubdomain: string,
  isGlobalAdmin: boolean = false
): boolean => {
  const currentSubdomain = getCurrentSubdomain();

  // Don't redirect if already on the correct subdomain
  if (currentSubdomain === targetSubdomain) {
    return false;
  }

  // Always allow redirects for farm switching
  return true;
};

/**
 * Handle farm switching with proper subdomain redirection
 * @param farmId The ID of the farm to switch to
 * @param currentUser The current user object
 * @param switchFarmFn The switchFarm function from AuthContext
 * @returns Promise<void>
 */
export const handleFarmSwitch = async (
  farmId: string,
  currentUser: any,
  switchFarmFn: (farmId: string) => Promise<{ farmSubdomain: string, farmId: string, farmName: string }>
): Promise<void> => {
  try {
    // Call the farm switching function from AuthContext
    const response = await switchFarmFn(farmId);

    // Check if we need to redirect to a different subdomain
    const currentSubdomain = getCurrentSubdomain();
    const targetSubdomain = response.farmSubdomain;

    if (shouldRedirectToSubdomain(targetSubdomain, currentUser.is_global_admin)) {
      redirectToSubdomain(targetSubdomain);
    } else {
      // If no redirect is needed, reload the page to update the context
      window.location.reload();
    }
  } catch (error) {
    console.error('Error handling farm switch:', error);
    throw error;
  }
};
