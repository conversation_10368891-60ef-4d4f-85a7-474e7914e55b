import axios from 'axios';
import { API_URL } from '../config';
import cache from '../utils/browserCache';

/**
 * Create a Financial Connections Session
 * @param farmId Farm ID
 * @param userId User ID
 * @param returnUrl Optional return URL after linking account
 * @returns Promise with session data
 */
export const createFinancialConnectionsSession = async (
  farmId: string, 
  userId: string, 
  returnUrl?: string
): Promise<{ client_secret: string; session_id: string }> => {
  try {
    const response = await axios.post(`${API_URL}/financial-connections/create-session`, {
      farmId,
      userId,
      returnUrl
    });
    return response.data;
  } catch (error) {
    console.error('Error creating Financial Connections session:', error);
    throw error;
  }
};

/**
 * Handle Financial Connections Session completion
 * @param sessionId Session ID
 * @param farmId Farm ID
 * @returns Promise with success message and connection ID
 */
export const handleFinancialConnectionsCallback = async (
  sessionId: string,
  farmId: string
): Promise<{ success: boolean; message: string; connectionId: string }> => {
  try {
    const response = await axios.post(`${API_URL}/financial-connections/callback`, {
      sessionId,
      farmId
    });
    return response.data;
  } catch (error) {
    console.error('Error handling Financial Connections callback:', error);
    throw error;
  }
};

/**
 * Get financial connections for a farm
 * @param farmId Farm ID
 * @returns Promise with financial connections
 */
export const getFinancialConnections = async (farmId: string): Promise<any[]> => {
  try {
    // Create a cache key based on the farm ID
    const cacheKey = `financial_connections_${farmId}`;

    // Check if data exists in cache
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached financial connections for farm ${farmId}`);
      return cachedData as any[];
    }

    const response = await axios.get(`${API_URL}/financial-connections/connections/${farmId}`);

    // Extract connections from the response
    const connections = response.data.connections || [];

    // Store in cache
    cache.set(cacheKey, connections);
    console.log(`Cached financial connections for farm ${farmId}`);

    return connections;
  } catch (error) {
    console.error(`Error fetching financial connections for farm ${farmId}:`, error);

    // Return empty array as fallback
    return [];
  }
};

/**
 * Get transactions for a financial connection
 * @param connectionId Connection ID
 * @param farmId Farm ID
 * @param startDate Start date (YYYY-MM-DD)
 * @param endDate End date (YYYY-MM-DD)
 * @returns Promise with transactions
 */
export const getTransactions = async (
  connectionId: string,
  farmId: string,
  startDate: string,
  endDate: string
): Promise<any[]> => {
  try {
    // Create a cache key based on the query parameters
    const cacheKey = `transactions_${connectionId}_${startDate}_${endDate}`;

    // Check if data exists in cache
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached transactions for connection ${connectionId}`);
      return cachedData as any[];
    }

    const response = await axios.get(`${API_URL}/financial-connections/transactions`, {
      params: {
        connectionId,
        farmId,
        startDate,
        endDate
      }
    });

    // Store in cache
    cache.set(cacheKey, response.data.transactions || []);
    console.log(`Cached transactions for connection ${connectionId}`);

    return response.data.transactions || [];
  } catch (error) {
    console.error('Error fetching transactions:', error);

    // Return empty array as fallback
    return [];
  }
};

/**
 * Get account balances for a financial connection
 * @param connectionId Connection ID
 * @returns Promise with account balances
 */
export const getAccountBalances = async (connectionId: string): Promise<any[]> => {
  try {
    // Create a cache key based on the connection ID
    const cacheKey = `account_balances_${connectionId}`;

    // Check if data exists in cache
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached account balances for connection ${connectionId}`);
      return cachedData as any[];
    }

    const response = await axios.get(`${API_URL}/financial-connections/accounts/${connectionId}`);

    // Store in cache
    cache.set(cacheKey, response.data.accounts || []);
    console.log(`Cached account balances for connection ${connectionId}`);

    return response.data.accounts || [];
  } catch (error) {
    console.error('Error fetching account balances:', error);

    // Return empty array as fallback
    return [];
  }
};

/**
 * Sync transactions for all financial connections for a farm
 * @param farmId Farm ID
 * @returns Promise with sync results
 */
export const syncAllTransactions = async (farmId: string): Promise<any> => {
  try {
    const response = await axios.post(`${API_URL}/financial-connections/sync/${farmId}`);

    // Clear cache for this farm's connections and transactions
    const connectionsCacheKey = `financial_connections_${farmId}`;
    cache.del(connectionsCacheKey);

    // Note: We would need to clear transaction caches for each connection,
    // but we don't have the connection IDs here. This will be handled when
    // transactions are fetched again.

    return response.data;
  } catch (error) {
    console.error('Error syncing transactions:', error);
    throw error;
  }
};
