import axios from 'axios';
import { API_URL } from '../config';
import { getIntegrations } from './integrationService';
import { Widget } from '../context/DashboardContext';

/**
 * Interface for widget definitions from plugins
 */
interface PluginWidgetDefinition {
  id: string;
  title: string;
  component: string;
  defaultPosition: {
    x: number;
    y: number;
    w: number;
    h: number;
  };
  permissions?: string[];
}

/**
 * Get all widgets from enabled plugins
 * @param farmId Optional farm ID to filter integrations
 * @returns Promise with array of widgets from enabled plugins
 */
export async function getPluginWidgets(farmId?: string): Promise<Widget[]> {
  try {
    // Get all enabled integrations
    const integrations = await getIntegrations(farmId);
    const enabledIntegrations = integrations.filter(integration => integration.enabled);

    const widgets: Widget[] = [];

    // For each enabled integration, load its widgets
    for (const integration of enabledIntegrations) {
      try {
        // Use Vite's glob import pattern instead of dynamic string template
        const pluginPath = `../../plugins/${integration.entry_point.replace(/\.js$/, '')}`;
        const pluginModule = await import(/* @vite-ignore */ pluginPath);

        // Create an instance of the plugin
        const PluginClass = pluginModule.default;
        const plugin = new PluginClass(integration.settings);

        // Mock context with UI registration capabilities
        const registeredWidgets: PluginWidgetDefinition[] = [];
        const mockContext = {
          ui: {
            registerDashboardWidget: (widget: PluginWidgetDefinition) => {
              registeredWidgets.push(widget);
            }
          }
        };

        // Initialize the plugin with the mock context
        await plugin.initialize(mockContext);

        // Convert plugin widget definitions to dashboard widgets
        for (const widgetDef of registeredWidgets) {
          // Map component names to widget types if needed
          let widgetType = widgetDef.component;

          // Create the widget with the appropriate type
          widgets.push({
            id: widgetDef.id,
            type: widgetType,
            title: widgetDef.title,
            position: {
              x: widgetDef.defaultPosition.x,
              y: widgetDef.defaultPosition.y,
              w: widgetDef.defaultPosition.w,
              h: widgetDef.defaultPosition.h
            }
          });
        }
      } catch (error) {
        console.error(`Error loading widgets from plugin ${integration.name}:`, error);
      }
    }

    return widgets;
  } catch (error) {
    console.error('Error fetching plugin widgets:', error);
    return [];
  }
}
