import axios from 'axios';
import { API_URL } from '../config';

export interface ProductInventoryLink {
  id: string;
  product_id: string;
  inventory_item_id: string;
  quantity_per_unit: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  Product?: {
    id: string;
    name: string;
    description: string;
    price: number;
    unit: string;
  };
  InventoryItem?: {
    id: string;
    name: string;
    description: string;
    quantity: number;
    unit: string;
  };
}

/**
 * Get all inventory items linked to a product
 * @param productId The ID of the product
 * @returns Promise<ProductInventoryLink[]> Array of product-inventory links
 */
export const getProductInventory = async (productId: string): Promise<ProductInventoryLink[]> => {
  try {
    const response = await axios.get(`${API_URL}/product-inventory/product/${productId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching product inventory:', error);
    throw error;
  }
};

/**
 * Get all products linked to an inventory item
 * @param inventoryItemId The ID of the inventory item
 * @returns Promise<ProductInventoryLink[]> Array of product-inventory links
 */
export const getInventoryProducts = async (inventoryItemId: string): Promise<ProductInventoryLink[]> => {
  try {
    const response = await axios.get(`${API_URL}/product-inventory/inventory/${inventoryItemId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching inventory products:', error);
    throw error;
  }
};

/**
 * Link a product to an inventory item
 * @param productId The ID of the product
 * @param inventoryItemId The ID of the inventory item
 * @param quantityPerUnit The quantity of inventory used per unit of product
 * @returns Promise<ProductInventoryLink> The created product-inventory link
 */
export const linkProductToInventory = async (
  productId: string,
  inventoryItemId: string,
  quantityPerUnit: number = 1.0
): Promise<ProductInventoryLink> => {
  try {
    const response = await axios.post(`${API_URL}/product-inventory`, {
      product_id: productId,
      inventory_item_id: inventoryItemId,
      quantity_per_unit: quantityPerUnit
    });
    return response.data;
  } catch (error) {
    console.error('Error linking product to inventory:', error);
    throw error;
  }
};

/**
 * Update a product-inventory link
 * @param linkId The ID of the product-inventory link
 * @param quantityPerUnit The new quantity per unit
 * @param isActive Whether the link is active
 * @returns Promise<ProductInventoryLink> The updated product-inventory link
 */
export const updateProductInventoryLink = async (
  linkId: string,
  quantityPerUnit?: number,
  isActive?: boolean
): Promise<ProductInventoryLink> => {
  try {
    const response = await axios.put(`${API_URL}/product-inventory/${linkId}`, {
      quantity_per_unit: quantityPerUnit,
      is_active: isActive
    });
    return response.data;
  } catch (error) {
    console.error('Error updating product-inventory link:', error);
    throw error;
  }
};

/**
 * Delete a product-inventory link
 * @param linkId The ID of the product-inventory link
 * @returns Promise<{ message: string }> Success message
 */
export const deleteProductInventoryLink = async (linkId: string): Promise<{ message: string }> => {
  try {
    const response = await axios.delete(`${API_URL}/product-inventory/${linkId}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting product-inventory link:', error);
    throw error;
  }
};

/**
 * Calculate the available inventory for a product
 * @param productId The ID of the product
 * @returns Promise<number | null> The available inventory or null if not linked to inventory
 */
export const getAvailableProductInventory = async (productId: string): Promise<number | null> => {
  try {
    // Get all inventory items linked to this product
    const links = await getProductInventory(productId);

    if (links.length === 0) {
      return null; // Product is not linked to any inventory items
    }

    // Calculate available inventory based on the limiting inventory item
    const availableUnits = links.map(link => {
      if (!link.InventoryItem) return Infinity;

      // How many product units can be made with this inventory item
      return Math.floor(link.InventoryItem.quantity / link.quantity_per_unit);
    });

    // The available inventory is limited by the most constrained inventory item
    return Math.min(...availableUnits);
  } catch (error) {
    console.error('Error calculating available product inventory:', error);
    throw error;
  }
};
