import axios from 'axios';
import { API_URL } from '../config';

// Types
export interface CarbonFootprintData {
  id?: number;
  farmId: number;
  calculationDate: string;
  totalEmissions: number;
  fuelEmissions: number;
  electricityEmissions: number;
  fertilizerEmissions: number;
  livestockEmissions: number;
  wasteEmissions: number;
  notes: string;
}

export interface SustainablePractice {
  id?: number;
  farmId: number;
  name: string;
  description: string;
  category: string;
  implementationDate: string;
  status: 'planned' | 'in-progress' | 'implemented';
  impact: string;
  notes: string;
}

export interface Certification {
  id?: number;
  farmId: number;
  name: string;
  certifyingBody: string;
  issueDate: string;
  expirationDate: string;
  status: 'pending' | 'active' | 'expired';
  documentUrl?: string;
  notes: string;
}

export interface EnvironmentalImpactReport {
  id?: number;
  farmId: number;
  reportDate: string;
  reportType: string;
  metrics: {
    waterUsage: number;
    soilHealth: number;
    biodiversity: number;
    wasteReduction: number;
    energyEfficiency: number;
  };
  summary: string;
  recommendations: string;
}

// Carbon Footprint Calculator
export const calculateCarbonFootprint = async (data: CarbonFootprintData) => {
  const response = await axios.post(`${API_URL}/api/sustainability/carbon-footprint`, data);
  return response.data;
};

export const getCarbonFootprints = async (farmId: number) => {
  const response = await axios.get(`${API_URL}/api/sustainability/carbon-footprint?farmId=${farmId}`);
  return response.data;
};

export const getCarbonFootprint = async (id: number) => {
  const response = await axios.get(`${API_URL}/api/sustainability/carbon-footprint/${id}`);
  return response.data;
};

export const updateCarbonFootprint = async (id: number, data: CarbonFootprintData) => {
  const response = await axios.put(`${API_URL}/api/sustainability/carbon-footprint/${id}`, data);
  return response.data;
};

export const deleteCarbonFootprint = async (id: number) => {
  const response = await axios.delete(`${API_URL}/api/sustainability/carbon-footprint/${id}`);
  return response.data;
};

// Sustainable Practices
export const createSustainablePractice = async (data: SustainablePractice) => {
  const response = await axios.post(`${API_URL}/api/sustainability/practices`, data);
  return response.data;
};

export const getSustainablePractices = async (farmId: number) => {
  const response = await axios.get(`${API_URL}/api/sustainability/practices?farmId=${farmId}`);
  return response.data;
};

export const getSustainablePractice = async (id: number) => {
  const response = await axios.get(`${API_URL}/api/sustainability/practices/${id}`);
  return response.data;
};

export const updateSustainablePractice = async (id: number, data: SustainablePractice) => {
  const response = await axios.put(`${API_URL}/api/sustainability/practices/${id}`, data);
  return response.data;
};

export const deleteSustainablePractice = async (id: number) => {
  const response = await axios.delete(`${API_URL}/api/sustainability/practices/${id}`);
  return response.data;
};

// Certifications
export const createCertification = async (data: Certification) => {
  const response = await axios.post(`${API_URL}/api/sustainability/certifications`, data);
  return response.data;
};

export const getCertifications = async (farmId: number) => {
  const response = await axios.get(`${API_URL}/api/sustainability/certifications?farmId=${farmId}`);
  return response.data;
};

export const getCertification = async (id: number) => {
  const response = await axios.get(`${API_URL}/api/sustainability/certifications/${id}`);
  return response.data;
};

export const updateCertification = async (id: number, data: Certification) => {
  const response = await axios.put(`${API_URL}/api/sustainability/certifications/${id}`, data);
  return response.data;
};

export const deleteCertification = async (id: number) => {
  const response = await axios.delete(`${API_URL}/api/sustainability/certifications/${id}`);
  return response.data;
};

// Environmental Impact Reports
export const generateEnvironmentalImpactReport = async (data: EnvironmentalImpactReport) => {
  const response = await axios.post(`${API_URL}/api/sustainability/impact-reports`, data);
  return response.data;
};

export const getEnvironmentalImpactReports = async (farmId: number) => {
  const response = await axios.get(`${API_URL}/api/sustainability/impact-reports?farmId=${farmId}`);
  return response.data;
};

export const getEnvironmentalImpactReport = async (id: number) => {
  const response = await axios.get(`${API_URL}/api/sustainability/impact-reports/${id}`);
  return response.data;
};

export const updateEnvironmentalImpactReport = async (id: number, data: EnvironmentalImpactReport) => {
  const response = await axios.put(`${API_URL}/api/sustainability/impact-reports/${id}`, data);
  return response.data;
};

export const deleteEnvironmentalImpactReport = async (id: number) => {
  const response = await axios.delete(`${API_URL}/api/sustainability/impact-reports/${id}`);
  return response.data;
};