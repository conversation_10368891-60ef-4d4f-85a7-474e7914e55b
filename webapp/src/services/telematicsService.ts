import axios from 'axios';
import { API_URL } from '../config';

export interface TelematicsData {
  id: string;
  equipment_id: string;
  timestamp: string;
  latitude: number | null;
  longitude: number | null;
  altitude: number | null;
  engine_hours: number | null;
  odometer: number | null;
  fuel_level: number | null;
  fuel_consumption_rate: number | null;
  engine_rpm: number | null;
  engine_load: number | null;
  engine_temperature: number | null;
  diagnostic_codes: any | null;
  operational_status: string | null;
  raw_data: any | null;
  created_at: string;
  updated_at: string;
}

export interface AggregatedTelematicsData {
  time_period: string;
  avg_engine_hours: number;
  engine_hours_change: number;
  avg_odometer: number;
  odometer_change: number;
  avg_fuel_level: number;
  avg_fuel_consumption: number;
  avg_engine_rpm: number;
  avg_engine_load: number;
  avg_engine_temp: number;
  data_points: number;
}

/**
 * Get telematics data for a specific equipment item
 */
export const getEquipmentTelematics = async (
  equipmentId: string,
  startDate?: string,
  endDate?: string,
  limit: number = 100,
  offset: number = 0
): Promise<{ telematics: TelematicsData[]; total: number; limit: number; offset: number }> => {
  try {
    let url = `${API_URL}/telematics/equipment/${equipmentId}?limit=${limit}&offset=${offset}`;

    if (startDate) {
      url += `&startDate=${startDate}`;
    }

    if (endDate) {
      url += `&endDate=${endDate}`;
    }

    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching equipment telematics:', error);
    throw error;
  }
};

/**
 * Get the latest telematics data for an equipment item
 */
export const getLatestTelematics = async (equipmentId: string): Promise<TelematicsData> => {
  try {
    const response = await axios.get(`${API_URL}/telematics/equipment/${equipmentId}/latest`);
    return response.data.telematics;
  } catch (error) {
    console.error('Error fetching latest telematics:', error);
    throw error;
  }
};

/**
 * Get aggregated telematics data for reporting
 */
export const getAggregatedTelematics = async (
  equipmentId: string,
  startDate: string,
  endDate: string,
  interval: 'hour' | 'day' | 'week' | 'month' = 'day'
): Promise<AggregatedTelematicsData[]> => {
  try {
    const response = await axios.get(
      `${API_URL}/telematics/equipment/${equipmentId}/aggregated?startDate=${startDate}&endDate=${endDate}&interval=${interval}`
    );
    return response.data.aggregatedData;
  } catch (error) {
    console.error('Error fetching aggregated telematics:', error);
    throw error;
  }
};

/**
 * Create new telematics data record
 */
export const createTelematics = async (data: {
  equipmentId: string;
  timestamp?: string;
  latitude?: number;
  longitude?: number;
  altitude?: number;
  engineHours?: number;
  odometer?: number;
  fuelLevel?: number;
  fuelConsumptionRate?: number;
  engineRpm?: number;
  engineLoad?: number;
  engineTemperature?: number;
  diagnosticCodes?: any;
  operationalStatus?: string;
  rawData?: any;
}): Promise<TelematicsData> => {
  try {
    const response = await axios.post(`${API_URL}/telematics`, data);
    return response.data.telematics;
  } catch (error) {
    console.error('Error creating telematics data:', error);
    throw error;
  }
};

/**
 * Send telematics data from external systems
 */
export const sendExternalTelematics = async (data: {
  apiKey: string;
  equipmentIdentifier: string;
  telematicsData: any;
}): Promise<{ telematicsId: string }> => {
  try {
    const response = await axios.post(`${API_URL}/telematics/external`, data);
    return response.data;
  } catch (error) {
    console.error('Error sending external telematics data:', error);
    throw error;
  }
};
