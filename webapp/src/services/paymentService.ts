import axios from 'axios';
import { API_URL } from '../config';

// Types for payment methods
export interface PaymentMethod {
  id: string;
  type: 'card' | 'bank_account';
  last4?: string;
  brand?: string;
  expMonth?: number;
  expYear?: number;
  isDefault: boolean;
}

// Types for payment providers
export type PaymentProvider = 'stripe';

// Types for subscription operations
export type SubscriptionOperation = 'subscribe' | 'upgrade' | 'downgrade' | 'cancel';

// Interface for payment details
export interface PaymentDetails {
  paymentMethodId?: string;
  paymentProvider: PaymentProvider;
  billingCycle: 'monthly' | 'yearly';
}

// Interface for invoice
export interface Invoice {
  id: string;
  amount: number;
  currency: string;
  status: string;
  date: string;
  pdfUrl?: string;
  items: {
    description: string;
    amount: number;
    quantity: number;
  }[];
}

/**
 * Initialize a payment session with Stripe
 * @param planId The ID of the subscription plan
 * @param farmId The ID of the farm
 * @param operation The subscription operation (subscribe, upgrade, downgrade)
 * @param billingCycle The billing cycle (monthly or yearly)
 * @param promoCode Optional promo code to apply to the subscription
 * @returns The Stripe session ID and URL
 */
export const initStripeSession = async (
  planId: string,
  farmId: string,
  operation: SubscriptionOperation,
  billingCycle: 'monthly' | 'yearly',
  promoCode?: string | null
) => {
  try {
    const response = await axios.post(
      `${API_URL}/payments/stripe/create-session`,
      {
        planId,
        farmId,
        operation,
        billingCycle,
        promoCode
      },
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      }
    );

    return response.data;
  } catch (error) {
    console.error('Error initializing Stripe session:', error);
    throw error;
  }
};


/**
 * Get payment methods for a farm
 * @param farmId The ID of the farm
 * @returns Array of payment methods
 */
export const getPaymentMethods = async (farmId: string): Promise<PaymentMethod[]> => {
  try {
    const response = await axios.get(`${API_URL}/payments/methods/${farmId}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`
      }
    });

    return response.data.paymentMethods;
  } catch (error) {
    console.error('Error fetching payment methods:', error);
    throw error;
  }
};

/**
 * Get a specific payment method by ID
 * @param paymentMethodId The ID of the payment method
 * @returns The payment method details
 */
export const getPaymentMethod = async (paymentMethodId: string): Promise<PaymentMethod> => {
  try {
    const response = await axios.get(`${API_URL}/payments/methods/method/${paymentMethodId}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`
      }
    });

    return response.data.paymentMethod;
  } catch (error) {
    console.error('Error fetching payment method:', error);
    throw error;
  }
};

/**
 * Add a new payment method
 * @param farmId The ID of the farm
 * @param paymentMethodId The ID of the payment method from Stripe
 * @param provider The payment provider (stripe)
 * @param setDefault Whether to set this as the default payment method
 * @returns The added payment method
 */
export const addPaymentMethod = async (
  farmId: string,
  paymentMethodId: string,
  provider: PaymentProvider,
  setDefault: boolean = false
): Promise<PaymentMethod> => {
  try {
    const response = await axios.post(
      `${API_URL}/payments/methods/${farmId}`,
      {
        paymentMethodId,
        provider,
        setDefault
      },
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      }
    );

    return response.data.paymentMethod;
  } catch (error) {
    console.error('Error adding payment method:', error);
    throw error;
  }
};

/**
 * Remove a payment method
 * @param farmId The ID of the farm
 * @param paymentMethodId The ID of the payment method
 * @returns Success message
 */
export const removePaymentMethod = async (farmId: string, paymentMethodId: string) => {
  try {
    const response = await axios.delete(`${API_URL}/payments/methods/${farmId}/${paymentMethodId}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`
      }
    });

    return response.data;
  } catch (error) {
    console.error('Error removing payment method:', error);
    throw error;
  }
};

/**
 * Set a payment method as default
 * @param farmId The ID of the farm
 * @param paymentMethodId The ID of the payment method
 * @returns Success message
 */
export const setDefaultPaymentMethod = async (farmId: string, paymentMethodId: string) => {
  try {
    const response = await axios.put(
      `${API_URL}/payments/methods/${farmId}/${paymentMethodId}/default`,
      {},
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      }
    );

    return response.data;
  } catch (error) {
    console.error('Error setting default payment method:', error);
    throw error;
  }
};

/**
 * Get invoices for a farm
 * @param farmId The ID of the farm
 * @returns Array of invoices
 */
export const getInvoices = async (farmId: string): Promise<Invoice[]> => {
  try {
    const response = await axios.get(`${API_URL}/payments/invoices/${farmId}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`
      }
    });

    return response.data.invoices;
  } catch (error) {
    console.error('Error fetching invoices:', error);
    throw error;
  }
};

/**
 * Get a specific invoice
 * @param farmId The ID of the farm
 * @param invoiceId The ID of the invoice
 * @returns The invoice details
 */
export const getInvoice = async (farmId: string, invoiceId: string): Promise<Invoice> => {
  try {
    const response = await axios.get(`${API_URL}/payments/invoices/${farmId}/${invoiceId}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`
      }
    });

    return response.data.invoice;
  } catch (error) {
    console.error('Error fetching invoice:', error);
    throw error;
  }
};

/**
 * Cancel a subscription
 * @param farmId The ID of the farm
 * @param atPeriodEnd Whether to cancel at the end of the billing period
 * @returns Success message
 */
export const cancelSubscription = async (farmId: string, atPeriodEnd: boolean = true) => {
  try {
    const response = await axios.post(
      `${API_URL}/subscriptions/cancel/${farmId}`,
      {
        atPeriodEnd
      },
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      }
    );

    return response.data;
  } catch (error) {
    console.error('Error canceling subscription:', error);
    throw error;
  }
};

/**
 * Update a subscription
 * @param farmId The ID of the farm
 * @param planId The ID of the new subscription plan
 * @param billingCycle The billing cycle (monthly or yearly)
 * @param paymentDetails Optional payment details for the update
 * @param promoCode Optional promo code to apply to the subscription
 * @returns Success message
 */
export const updateSubscription = async (
  farmId: string,
  planId: string,
  billingCycle: 'monthly' | 'yearly',
  paymentDetails?: PaymentDetails,
  promoCode?: string | null
) => {
  try {
    const response = await axios.put(
      `${API_URL}/subscriptions/update/${farmId}`,
      {
        planId,
        billingCycle,
        paymentDetails,
        promoCode
      },
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      }
    );

    return response.data;
  } catch (error) {
    console.error('Error updating subscription:', error);
    throw error;
  }
};
