import axios from 'axios';
import { API_URL } from '../config';

// Types for Ambrook data
export interface AmbrookGrant {
  id: string;
  name: string;
  description: string;
  amount: number;
  deadline: string;
  eligibility: string;
  status: 'open' | 'closed' | 'coming_soon';
  category: string;
  url: string;
}

export interface AmbrookLoan {
  id: string;
  name: string;
  description: string;
  provider: string;
  interestRate: string;
  term: string;
  eligibility: string;
  status: 'available' | 'unavailable';
  category: string;
  url: string;
}

export interface AmbrookFinancialReport {
  id: string;
  name: string;
  description: string;
  date: string;
  type: 'income_statement' | 'balance_sheet' | 'cash_flow';
  url: string;
}

/**
 * Get available grants from Ambrook
 * @returns Promise<AmbrookGrant[]> List of available grants
 */
export const getAvailableGrants = async (): Promise<AmbrookGrant[]> => {
  try {
    const response = await axios.get(`${API_URL}/ambrook/grants`);
    return response.data.grants;
  } catch (error) {
    console.error('Error fetching Ambrook grants:', error);
    throw new Error('Failed to fetch grants from Ambrook');
  }
};

/**
 * Get available loans from Ambrook
 * @returns Promise<AmbrookLoan[]> List of available loans
 */
export const getAvailableLoans = async (): Promise<AmbrookLoan[]> => {
  try {
    const response = await axios.get(`${API_URL}/ambrook/loans`);
    return response.data.loans;
  } catch (error) {
    console.error('Error fetching Ambrook loans:', error);
    throw new Error('Failed to fetch loans from Ambrook');
  }
};

/**
 * Get financial reports from Ambrook
 * @returns Promise<AmbrookFinancialReport[]> List of financial reports
 */
export const getFinancialReports = async (): Promise<AmbrookFinancialReport[]> => {
  try {
    const response = await axios.get(`${API_URL}/ambrook/reports`);
    return response.data.reports;
  } catch (error) {
    console.error('Error fetching Ambrook financial reports:', error);
    throw new Error('Failed to fetch financial reports from Ambrook');
  }
};

/**
 * Apply for a grant through Ambrook
 * @param grantId The ID of the grant to apply for
 * @param applicationData The application data
 * @returns Promise<{ message: string; applicationId: string }> Success message and application ID
 */
export const applyForGrant = async (
  grantId: string,
  applicationData: any
): Promise<{ message: string; applicationId: string }> => {
  try {
    const response = await axios.post(`${API_URL}/ambrook/grants/${grantId}/apply`, applicationData);
    return response.data;
  } catch (error) {
    console.error('Error applying for Ambrook grant:', error);
    throw new Error('Failed to apply for grant through Ambrook');
  }
};

/**
 * Apply for a loan through Ambrook
 * @param loanId The ID of the loan to apply for
 * @param applicationData The application data
 * @returns Promise<{ message: string; applicationId: string }> Success message and application ID
 */
export const applyForLoan = async (
  loanId: string,
  applicationData: any
): Promise<{ message: string; applicationId: string }> => {
  try {
    const response = await axios.post(`${API_URL}/ambrook/loans/${loanId}/apply`, applicationData);
    return response.data;
  } catch (error) {
    console.error('Error applying for Ambrook loan:', error);
    throw new Error('Failed to apply for loan through Ambrook');
  }
};

/**
 * Sync farm financial data with Ambrook
 * @param farmId The ID of the farm to sync data for
 * @returns Promise<{ message: string }> Success message
 */
export const syncFarmData = async (farmId: string): Promise<{ message: string }> => {
  try {
    const response = await axios.post(`${API_URL}/ambrook/sync`, { farmId });
    return response.data;
  } catch (error) {
    console.error('Error syncing farm data with Ambrook:', error);
    throw new Error('Failed to sync farm data with Ambrook');
  }
};

/**
 * Sync data from Ambrook back to the app
 * @param farmId The ID of the farm to sync data for
 * @returns Promise<{ message: string }> Success message
 */
export const syncDataFromAmbrook = async (farmId: string): Promise<{ message: string }> => {
  try {
    const response = await axios.post(`${API_URL}/ambrook/sync-from-ambrook`, { farmId });
    return response.data;
  } catch (error) {
    console.error('Error syncing data from Ambrook:', error);
    throw new Error('Failed to sync data from Ambrook');
  }
};
