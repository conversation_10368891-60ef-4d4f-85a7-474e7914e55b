import axios from 'axios';
import { API_URL } from '../config';

export interface CropType {
  id?: string;
  farm_id: string;
  name: string;
  description?: string;
  growing_season?: string;
  days_to_maturity?: number;
  planting_depth?: number;
  row_spacing?: number;
  plant_spacing?: number;
  ideal_soil_ph?: number;
  ideal_temperature?: number;
  created_at?: string;
  updated_at?: string;
}

/**
 * Get all crop types for a farm
 * @param farmId The farm ID
 * @returns Promise<CropType[]> Array of crop types
 */
export const getFarmCropTypes = async (farmId: string): Promise<CropType[]> => {
  try {
    const token = localStorage.getItem('token');
    const response = await axios.get(`${API_URL}/crop-types/farm/${farmId}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    return response.data.cropTypes;
  } catch (error) {
    console.error('Error fetching farm crop types:', error);
    return [];
  }
};

/**
 * Get a single crop type by ID
 * @param cropTypeId The crop type ID
 * @returns Promise<CropType | null> The crop type or null if not found
 */
export const getCropTypeById = async (cropTypeId: string): Promise<CropType | null> => {
  try {
    const token = localStorage.getItem('token');
    const response = await axios.get(`${API_URL}/crop-types/${cropTypeId}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    return response.data.cropType;
  } catch (error) {
    console.error('Error fetching crop type:', error);
    return null;
  }
};

/**
 * Create a new crop type
 * @param cropType The crop type data
 * @returns Promise<CropType | null> The created crop type or null if creation failed
 */
export const createCropType = async (cropType: CropType): Promise<CropType | null> => {
  try {
    const token = localStorage.getItem('token');
    
    // Convert snake_case to camelCase for the API
    const payload = {
      farmId: cropType.farm_id,
      name: cropType.name,
      description: cropType.description,
      growingSeason: cropType.growing_season,
      daysToMaturity: cropType.days_to_maturity,
      plantingDepth: cropType.planting_depth,
      rowSpacing: cropType.row_spacing,
      plantSpacing: cropType.plant_spacing,
      idealSoilPh: cropType.ideal_soil_ph,
      idealTemperature: cropType.ideal_temperature
    };
    
    const response = await axios.post(`${API_URL}/crop-types`, payload, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    return response.data.cropType;
  } catch (error) {
    console.error('Error creating crop type:', error);
    return null;
  }
};

/**
 * Update an existing crop type
 * @param cropTypeId The crop type ID
 * @param cropType The updated crop type data
 * @returns Promise<CropType | null> The updated crop type or null if update failed
 */
export const updateCropType = async (cropTypeId: string, cropType: Partial<CropType>): Promise<CropType | null> => {
  try {
    const token = localStorage.getItem('token');
    
    // Convert snake_case to camelCase for the API
    const payload = {
      name: cropType.name,
      description: cropType.description,
      growingSeason: cropType.growing_season,
      daysToMaturity: cropType.days_to_maturity,
      plantingDepth: cropType.planting_depth,
      rowSpacing: cropType.row_spacing,
      plantSpacing: cropType.plant_spacing,
      idealSoilPh: cropType.ideal_soil_ph,
      idealTemperature: cropType.ideal_temperature
    };
    
    const response = await axios.put(`${API_URL}/crop-types/${cropTypeId}`, payload, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    return response.data.cropType;
  } catch (error) {
    console.error('Error updating crop type:', error);
    return null;
  }
};

/**
 * Delete a crop type
 * @param cropTypeId The crop type ID
 * @returns Promise<boolean> True if deletion was successful, false otherwise
 */
export const deleteCropType = async (cropTypeId: string): Promise<boolean> => {
  try {
    const token = localStorage.getItem('token');
    await axios.delete(`${API_URL}/crop-types/${cropTypeId}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    return true;
  } catch (error) {
    console.error('Error deleting crop type:', error);
    return false;
  }
};