import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import axios from 'axios';
import { useAuth } from './AuthContext';

// Define types for help guides and help tips
export interface HelpGuide {
  id: string;
  title: string;
  slug: string;
  content: string;
  category: string;
  subcategory?: string;
  tags: string[];
  isPublished: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface HelpTip {
  id: string;
  title: string;
  content: string;
  pagePath: string;
  elementSelector?: string;
  position: 'top' | 'right' | 'bottom' | 'left';
  order: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface DismissedHelpTip {
  id: string;
  tipId: string;
  dismissedAt: string;
  tip?: {
    id: string;
    title: string;
    pagePath: string;
  };
}



// Define the context type
interface HelpContextType {
  guides: HelpGuide[];
  tips: HelpTip[];
  dismissedTips: DismissedHelpTip[];
  loading: boolean;
  error: string | null;
  searchResults: HelpGuide[];
  searchTerm: string;
  helpTipsDisabled: boolean;
  setSearchTerm: (term: string) => void;
  searchGuides: (term: string) => Promise<void>;
  getGuideBySlug: (slug: string) => Promise<HelpGuide | null>;
  getTipsByPath: (path: string) => HelpTip[];
  dismissTip: (tipId: string) => Promise<void>;
  resetDismissedTips: () => Promise<void>;
  isTipDismissed: (tipId: string) => boolean;
  toggleHelpTipsDisabled: () => Promise<void>;
}

// Create the context
const HelpContext = createContext<HelpContextType | undefined>(undefined);

// Create a provider component
export const HelpProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [guides, setGuides] = useState<HelpGuide[]>([]);
  const [tips, setTips] = useState<HelpTip[]>([]);
  const [dismissedTips, setDismissedTips] = useState<DismissedHelpTip[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchResults, setSearchResults] = useState<HelpGuide[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [helpTipsDisabled, setHelpTipsDisabled] = useState<boolean>(false);

  // Fetch help guides and tips when the component mounts
  useEffect(() => {
    const fetchHelpData = async () => {
      try {
        setLoading(true);

        // Fetch help guides only if user is logged in
        if (user) {
          try {
            const guidesResponse = await axios.get('/api/help/guides', {
              params: { isPublished: true }
            });

            // Add null/undefined check before destructuring
            if (guidesResponse.data && Array.isArray(guidesResponse.data.guides)) {
              setGuides(guidesResponse.data.guides);
            } else {
              console.warn('API returned unexpected format for guides:', guidesResponse.data);
              setGuides([]);
            }
          } catch (guidesErr) {
            console.error('Error fetching guides:', guidesErr);
            setGuides([]);
          }
        }

        // Fetch help tips only if user is logged in
        if (user) {
          try {
            const tipsResponse = await axios.get('/api/help/tips', {
              params: { isActive: true }
            });

            // Add null/undefined check before destructuring
            if (tipsResponse.data && Array.isArray(tipsResponse.data.tips)) {
              setTips(tipsResponse.data.tips);
            } else {
              console.warn('API returned unexpected format for tips:', tipsResponse.data);
              setTips([]);
            }
          } catch (tipsErr) {
            console.error('Error fetching tips:', tipsErr);
            setTips([]);
          }
        }

        // Fetch dismissed tips only if user is logged in
        if (user) {
          try {
            const dismissedResponse = await axios.get('/api/help/tips/dismissed');

            // Add null/undefined check before destructuring
            if (dismissedResponse.data && Array.isArray(dismissedResponse.data.dismissals)) {
              setDismissedTips(dismissedResponse.data.dismissals);
            } else {
              console.warn('API returned unexpected format for dismissed tips:', dismissedResponse.data);
              setDismissedTips([]);
            }

            // Fetch user preferences
            try {
              const userResponse = await axios.get('/api/users/me');
              if (userResponse.data && userResponse.data.user) {
                setHelpTipsDisabled(userResponse.data.user.help_tips_disabled || false);
              }
            } catch (userErr) {
              console.error('Error fetching user preferences:', userErr);
            }
          } catch (dismissErr) {
            console.error('Error fetching dismissed tips:', dismissErr);
            setDismissedTips([]);
            // Don't set the error state here to avoid affecting the whole help system
          }
        }

        setLoading(false);
      } catch (err) {
        console.error('Error fetching help data:', err);
        setError('Failed to load help content. Please try again later.');
        setLoading(false);
      }
    };

    fetchHelpData();
  }, [user]);



  // Search guides by term
  const searchGuides = async (term: string) => {
    if (!term.trim()) {
      setSearchResults([]);
      return;
    }

    // Don't search guides if user is not logged in
    if (!user) {
      setSearchResults([]);
      return;
    }

    try {
      setLoading(true);
      const response = await axios.get('/api/help/guides', {
        params: { search: term, isPublished: true }
      });

      // Add null/undefined check before destructuring
      if (response.data && Array.isArray(response.data.guides)) {
        setSearchResults(response.data.guides);
      } else {
        console.warn('API returned unexpected format for search results:', response.data);
        setSearchResults([]);
      }

      setLoading(false);
    } catch (err) {
      console.error('Error searching help guides:', err);
      setError('Failed to search help content. Please try again later.');
      setSearchResults([]);
      setLoading(false);
    }
  };

  // Get a guide by slug
  const getGuideBySlug = async (slug: string): Promise<HelpGuide | null> => {
    // Don't fetch guide if user is not logged in
    if (!user) {
      return null;
    }

    try {
      const response = await axios.get(`/api/help/guides/${slug}`);

      // Add null/undefined check before destructuring
      if (response.data && response.data.guide) {
        return response.data.guide;
      } else {
        console.warn('API returned unexpected format for guide:', response.data);
        return null;
      }
    } catch (err) {
      console.error('Error fetching help guide:', err);
      setError('Failed to load help guide. Please try again later.');
      return null;
    }
  };

  // Get tips for a specific page path
  const getTipsByPath = (path: string): HelpTip[] => {
    // If help tips are disabled, return an empty array
    if (helpTipsDisabled) {
      return [];
    }

    return tips.filter(tip =>
      tip.pagePath === path &&
      !dismissedTips.some(dismissed => dismissed.tipId === tip.id)
    ).sort((a, b) => a.order - b.order);
  };

  // Dismiss a tip
  const dismissTip = async (tipId: string) => {
    // Don't attempt to dismiss tips if user is not logged in
    if (!user) {
      // Just update local state without making the API call
      const fakeDismissal = {
        id: `temp-${Date.now()}`,
        tipId,
        dismissedAt: new Date().toISOString()
      };
      setDismissedTips([...dismissedTips, fakeDismissal]);
      return;
    }

    try {
      const response = await axios.post(`/api/help/tips/${tipId}/dismiss`);
      setDismissedTips([...dismissedTips, response.data.dismissal]);
    } catch (err) {
      console.error('Error dismissing help tip:', err);
      setError('Failed to dismiss help tip. Please try again later.');
    }
  };

  // Reset all dismissed tips
  const resetDismissedTips = async () => {
    // Don't attempt to reset tips if user is not logged in
    if (!user) {
      // Just update local state without making the API call
      setDismissedTips([]);
      return;
    }

    try {
      await axios.post('/api/help/tips/reset-dismissed');
      setDismissedTips([]);
    } catch (err) {
      console.error('Error resetting dismissed help tips:', err);
      setError('Failed to reset dismissed help tips. Please try again later.');
    }
  };

  // Check if a tip is dismissed
  const isTipDismissed = (tipId: string): boolean => {
    return dismissedTips.some(dismissed => dismissed.tipId === tipId);
  };

  // Toggle help tips disabled preference
  const toggleHelpTipsDisabled = async () => {
    // Don't attempt to toggle if user is not logged in
    if (!user) {
      return;
    }

    try {
      const newValue = !helpTipsDisabled;
      await axios.patch('/api/users/me', { help_tips_disabled: newValue });
      setHelpTipsDisabled(newValue);
    } catch (err) {
      console.error('Error toggling help tips disabled preference:', err);
      setError('Failed to update help tips preference. Please try again later.');
    }
  };







  // Create the context value
  const contextValue: HelpContextType = {
    guides,
    tips,
    dismissedTips,
    loading,
    error,
    searchResults,
    searchTerm,
    helpTipsDisabled,
    setSearchTerm,
    searchGuides,
    getGuideBySlug,
    getTipsByPath,
    dismissTip,
    resetDismissedTips,
    isTipDismissed,
    toggleHelpTipsDisabled
  };

  return (
    <HelpContext.Provider value={contextValue}>
      {children}
    </HelpContext.Provider>
  );
};

// Create a custom hook to use the help context
export const useHelp = () => {
  const context = useContext(HelpContext);
  if (context === undefined) {
    throw new Error('useHelp must be used within a HelpProvider');
  }
  return context;
};
