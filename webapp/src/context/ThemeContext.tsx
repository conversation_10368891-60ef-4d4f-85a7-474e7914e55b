import React, { createContext, useContext, useEffect, useState } from 'react';

// Define theme types
export type ThemeMode = 'light' | 'dark' | 'system';

// Define the context type
interface ThemeContextType {
  theme: ThemeMode;
  currentTheme: 'light' | 'dark'; // The actual applied theme (after system preference is resolved)
  setTheme: (theme: ThemeMode) => void;
}

// Create the context with a default value
const ThemeContext = createContext<ThemeContextType>({
  theme: 'system',
  currentTheme: 'light',
  setTheme: () => {},
});

// Custom hook to use the theme context
export const useTheme = () => useContext(ThemeContext);

// Theme provider component
export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Get the saved theme from localStorage or default to 'system'
  const [theme, setThemeState] = useState<ThemeMode>(() => {
    const savedTheme = localStorage.getItem('theme') as ThemeMode;
    return savedTheme || 'system';
  });

  // State to track the actual applied theme (after system preference is resolved)
  const [currentTheme, setCurrentTheme] = useState<'light' | 'dark'>('light');

  // Function to set the theme and save it to localStorage
  const setTheme = (newTheme: ThemeMode) => {
    setThemeState(newTheme);
    localStorage.setItem('theme', newTheme);
  };

  // Effect to apply the theme to the document
  useEffect(() => {
    const applyTheme = (isDark: boolean) => {
      if (isDark) {
        document.documentElement.classList.add('dark');
        setCurrentTheme('dark');
      } else {
        document.documentElement.classList.remove('dark');
        setCurrentTheme('light');
      }
    };

    // Handle system preference
    if (theme === 'system') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      // Apply initial theme based on system preference
      applyTheme(mediaQuery.matches);
      
      // Add listener for system preference changes
      const listener = (e: MediaQueryListEvent) => applyTheme(e.matches);
      mediaQuery.addEventListener('change', listener);
      
      // Clean up listener
      return () => mediaQuery.removeEventListener('change', listener);
    } else {
      // Apply user selected theme
      applyTheme(theme === 'dark');
    }
  }, [theme]);

  return (
    <ThemeContext.Provider value={{ theme, currentTheme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};