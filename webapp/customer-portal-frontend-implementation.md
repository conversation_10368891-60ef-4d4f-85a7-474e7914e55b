# Customer Portal Frontend Implementation Guide

## Overview
This document provides guidance for implementing the frontend components of the customer portal. The backend API endpoints have been implemented, and now the frontend needs to be created to provide a user interface for customers to view and pay invoices, as well as ask questions about them.

## Pages to Create

### Authentication Pages
1. **Login Page**
   - Form with email and password fields
   - "Forgot Password" link
   - "Register" link for new customers

2. **Registration Page**
   - Form with name, email, password fields
   - Terms and conditions acceptance
   - Email verification notice

3. **Password Reset Page**
   - Email input for password reset request
   - New password form (when accessed with token)

4. **Email Verification Page**
   - Success/failure message when verifying email with token

### Invoice Management Pages
1. **Invoice List Page**
   - Table of all invoices with status, date, amount
   - Filtering by status (paid, unpaid, all)
   - Sorting by date, amount, etc.
   - Search functionality

2. **Invoice Detail Page**
   - Invoice header with number, date, status
   - Line items with descriptions, quantities, prices
   - Total amount and tax breakdown
   - Payment button for unpaid invoices
   - Questions section

### Payment Processing
1. **Payment Modal/Page**
   - Stripe Elements integration for card input
   - ACH payment option
   - Display of total amount
   - Display of Stripe fees (if applicable)
   - Payment confirmation

2. **Payment Confirmation Page**
   - Receipt details
   - Transaction ID
   - Option to download/print receipt

### Communication
1. **Invoice Questions Section**
   - List of existing questions and responses
   - Form to submit new questions
   - Status indicators for resolved/unresolved questions

## API Integration

### Authentication
```javascript
// Login
const login = async (email, password) => {
  const response = await fetch('/api/customer/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email, password })
  });
  return response.json();
};

// Register
const register = async (userData) => {
  const response = await fetch('/api/customer/auth/register', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(userData)
  });
  return response.json();
};
```

### Invoice Management
```javascript
// Get all invoices
const getInvoices = async () => {
  const response = await fetch('/api/customer/invoices', {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  return response.json();
};

// Get invoice details
const getInvoiceDetails = async (invoiceId) => {
  const response = await fetch(`/api/customer/invoices/${invoiceId}`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  return response.json();
};
```

### Payment Processing
```javascript
// Create payment intent
const createPaymentIntent = async (invoiceId, paymentMethodType) => {
  const response = await fetch(`/api/customer/invoices/${invoiceId}/payment-intent`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ paymentMethodType })
  });
  return response.json();
};

// Process payment
const payInvoice = async (invoiceId, paymentIntentId, paymentMethodId) => {
  const response = await fetch(`/api/customer/invoices/${invoiceId}/pay`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ paymentIntentId, paymentMethodId })
  });
  return response.json();
};
```

### Invoice Questions
```javascript
// Get questions for an invoice
const getInvoiceQuestions = async (invoiceId) => {
  const response = await fetch(`/api/customer/invoices/${invoiceId}/questions`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  return response.json();
};

// Add a question to an invoice
const addInvoiceQuestion = async (invoiceId, question) => {
  const response = await fetch(`/api/customer/invoices/${invoiceId}/questions`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ question })
  });
  return response.json();
};
```

## Stripe Integration

### Card Payments
```javascript
import { loadStripe } from '@stripe/stripe-js';
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js';

// Initialize Stripe
const stripePromise = loadStripe('your_publishable_key');

// Payment component
const PaymentForm = ({ invoice, clientSecret }) => {
  const stripe = useStripe();
  const elements = useElements();

  const handleSubmit = async (event) => {
    event.preventDefault();
    
    const result = await stripe.confirmCardPayment(clientSecret, {
      payment_method: {
        card: elements.getElement(CardElement),
        billing_details: {
          name: customer.name,
          email: customer.email
        }
      }
    });
    
    if (result.error) {
      // Show error to customer
    } else {
      // Payment succeeded, call payInvoice API
      await payInvoice(invoice.id, result.paymentIntent.id, result.paymentIntent.payment_method);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <CardElement />
      <button type="submit" disabled={!stripe}>Pay</button>
    </form>
  );
};

// Wrap with Elements provider
const StripePaymentForm = ({ invoice }) => {
  const [clientSecret, setClientSecret] = useState('');
  
  useEffect(() => {
    // Fetch payment intent when component mounts
    const getPaymentIntent = async () => {
      const { clientSecret } = await createPaymentIntent(invoice.id, 'card');
      setClientSecret(clientSecret);
    };
    
    getPaymentIntent();
  }, [invoice.id]);
  
  return (
    <Elements stripe={stripePromise}>
      <PaymentForm invoice={invoice} clientSecret={clientSecret} />
    </Elements>
  );
};
```

### ACH Payments
For ACH payments, you'll need to use Stripe's bank account elements and the `confirmAchDebitPayment` method instead of `confirmCardPayment`.

## Design Considerations
1. Use responsive design to ensure the portal works on all devices
2. Implement clear error messages for payment failures
3. Show loading indicators during API calls
4. Implement proper form validation
5. Use clear visual indicators for invoice status
6. Ensure accessibility compliance

## Testing
1. Test authentication flow (login, registration, password reset)
2. Test invoice viewing and filtering
3. Test payment processing with both card and ACH
4. Test adding and viewing questions
5. Test on different devices and browsers

## Deployment
Once the frontend implementation is complete, deploy alongside the backend API to make the customer portal available to customers.