# Server Configuration
NODE_ENV=development
PORT=3001

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=farmbooks
DB_USER=postgres
DB_PASSWORD=postgres
DB_SCHEMA=site
DB_MIGRATION_TOKEN=your_secure_migration_token

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=1d
JWT_REFRESH_SECRET=your_refresh_token_secret
JWT_REFRESH_EXPIRES_IN=7d

# Stripe API Configuration
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key

# Email Configuration (for password reset)
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password
EMAIL_FROM=<EMAIL>

# Frontend URL (for CORS and email links)
FRONTEND_URL=http://localhost:5173

# QuickBooks API Configuration
QUICKBOOKS_CLIENT_ID=your_quickbooks_client_id
QUICKBOOKS_CLIENT_SECRET=your_quickbooks_client_secret
QUICKBOOKS_REDIRECT_URI=http://localhost:5173/quickbooks/callback

# Main domain for subdomains
VITE_MAIN_DOMAIN=example.com
API_URL=http://localhost:3002/api

# Weather API Configuration
# Using National Weather Service API (no API key required)
# OpenWeatherMap API for rainfall data
OPENWEATHER_API_KEY=your_openweathermap_api_key
TOMORROW_API_KEY=your_tomorrow_api_key
# Open-Meteo API (no API key required)
OPEN_METEO_API_URL=https://api.open-meteo.com/v1

# Agricultural Grants API Configuration
GRANTS_GOV_API_KEY=your_grants_gov_api_key
GRANTS_GOV_API_URL=https://www.grants.gov/grantsws/rest
FARMERS_GOV_API_KEY=your_farmers_gov_api_key
FARMERS_GOV_API_URL=https://www.farmers.gov/api
USDA_ARMS_API_KEY=your_usda_arms_api_key
USDA_ARMS_API_URL=https://api.ers.usda.gov/arms
FARM_SERVICE_AGENCY_API_KEY=your_fsa_api_key
FARM_SERVICE_AGENCY_API_URL=https://api.fsa.usda.gov
RURAL_DEVELOPMENT_API_KEY=your_rural_development_api_key
RURAL_DEVELOPMENT_API_URL=https://api.rd.usda.gov/v1
NRCS_API_KEY=your_nrcs_api_key
NRCS_API_URL=https://api.nrcs.usda.gov/v1
NIFA_API_KEY=your_nifa_api_key
NIFA_API_URL=https://api.nifa.usda.gov/v1
RMA_API_KEY=your_rma_api_key
RMA_API_URL=https://api.rma.usda.gov/v1
AMS_API_KEY=your_ams_api_key
AMS_API_URL=https://api.ams.usda.gov/v1

# Data.gov API Configuration
DATA_GOV_API_KEY=your_data_gov_api_key
DATA_GOV_API_URL=https://api.data.gov
USDA_NRCS_SOIL_API_URL=https://sdmdataaccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx

# NCEI API Configuration
NCEI_DATA_SERVICE_URL=https://www.ncei.noaa.gov/access/services/data/v1
NCEI_SEARCH_SERVICE_URL=https://www.ncei.noaa.gov/access/services/search/v1
NCEI_TOKEN=your_ncei_token

# Google Maps API Configuration
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# Google Drive API Configuration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_REDIRECT_URI=http://localhost:5173/external-storage-auth/google/callback

# Dropbox API Configuration
DROPBOX_APP_KEY=your_dropbox_app_key
DROPBOX_APP_SECRET=your_dropbox_app_secret
DROPBOX_REDIRECT_URI=http://localhost:5173/external-storage-auth/dropbox/callback

# Cron Job Configuration
CRON_SECRET_KEY=your_cron_secret_key

# Digital Ocean Configuration
DIGITAL_OCEAN_API_KEY=your_digital_ocean_api_key

# Digital Ocean Spaces Configuration
SPACES_ENDPOINT=https://nyc3.digitaloceanspaces.com
SPACES_REGION=nyc3
SPACES_NAME=nxtacre
SPACES_KEY=your_spaces_key
SPACES_SECRET=your_spaces_secret

# Sentry Configuration
SENTRY_AUTH_TOKEN=your_sentry_auth_token
SENTRY_ORG=o4509238970286080
SENTRY_PROJECT=****************
SENTRY_DSN=your_sentry_dsn
VITE_SENTRY_DSN=your_sentry_dsn

# Add a secure encryption key for camera credentials
ENCRYPTION_KEY=32_character_encryption_key

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key

# Twilio API Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number
