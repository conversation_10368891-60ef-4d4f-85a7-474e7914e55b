/*
 * <PERSON><PERSON> Handler for Heltec Wireless Tracker (ESP32S3)
 * 
 * This file implements the Bluetooth Low Energy functionality for
 * communicating with the mobile app.
 */

#ifndef BLE_HANDLER_H
#define BLE_HANDLER_H

#include <Arduino.h>
// Use the official ESP32 BLE libraries
#include <BLEDevice.h>
#include <BLEServer.h>
#include <BLEUtils.h>
#include <BLE2902.h>
#include "config.h"

// Callback function type for handling incoming BLE messages
typedef std::function<void(const String&)> BLEMessageCallback;

// Class to handle BLE server callbacks
class ServerCallbacks: public BLEServerCallbacks {
private:
  bool* deviceConnected;

public:
  ServerCallbacks(bool* connected) : deviceConnected(connected) {}

  void onConnect(BLEServer* server) {
    *deviceConnected = true;
    if (DEBUG_ENABLED) {
      DEBUG_SERIAL.println("BLE Client connected");
    }
  }

  void onDisconnect(BLEServer* server) {
    *deviceConnected = false;
    if (DEBUG_ENABLED) {
      DEBUG_SERIAL.println("BLE Client disconnected");
    }
    // Start advertising again
    server->getAdvertising()->start();
  }
};

// Class to handle BLE characteristic callbacks
class CharacteristicCallbacks: public BLECharacteristicCallbacks {
private:
  BLEMessageCallback messageCallback;

public:
  CharacteristicCallbacks(BLEMessageCallback callback) : messageCallback(callback) {}

  void onWrite(BLECharacteristic* characteristic) {
    // Fix: Use String directly instead of std::string
    String value = characteristic->getValue().c_str();
    if (value.length() > 0) {
      if (messageCallback) {
        messageCallback(value);
      }
    }
  }
};

  // Main BLE handler class
class BLEHandler {
private:
  BLEServer* server;
  BLECharacteristic* characteristic;
  bool deviceConnected;
  bool oldDeviceConnected;
  BLEMessageCallback messageCallback;
  unsigned long lastNotifyTime;
  unsigned long lastAdvertisingRefresh; // Time of last advertising refresh

public:
  BLEHandler() : 
    server(nullptr), 
    characteristic(nullptr), 
    deviceConnected(false), 
    oldDeviceConnected(false),
    lastNotifyTime(0),
    lastAdvertisingRefresh(0) {}

  // Initialize BLE
  void init() {
    // Initialize BLE device
    BLEDevice::init(BLE_DEVICE_NAME);

    // Create BLE server
    server = BLEDevice::createServer();
    server->setCallbacks(new ServerCallbacks(&deviceConnected));

    // Create BLE service
    BLEService* service = server->createService(SERVICE_UUID);

    // Create BLE characteristic
    characteristic = service->createCharacteristic(
      CHARACTERISTIC_UUID,
      BLECharacteristic::PROPERTY_READ |
      BLECharacteristic::PROPERTY_WRITE |
      BLECharacteristic::PROPERTY_NOTIFY
    );

    // Add descriptor for notifications
    characteristic->addDescriptor(new BLE2902());

    // Start the service
    service->start();

    // Configure advertising
    BLEAdvertising* advertising = server->getAdvertising();

    // Set advertising parameters for better discovery
    advertising->setMinPreferred(0x06);  // functions that help with iPhone connections issue
    advertising->setMaxPreferred(0x12);

    // Add the service UUID to the advertisement data
    advertising->addServiceUUID(SERVICE_UUID);

    // Set advertising interval using the configured value
    // Parameters are in units of 0.625ms
    uint16_t advIntervalMin = (BLE_ADVERTISING_INTERVAL * 8) / 5; // Convert ms to 0.625ms units
    uint16_t advIntervalMax = advIntervalMin * 2;
    advertising->setMinInterval(advIntervalMin);
    advertising->setMaxInterval(advIntervalMax);

    // Set advertising power to maximum for better range
    BLEDevice::setPower(ESP_PWR_LVL_P9); // Maximum power level

    // Add device name to the advertising data for better discovery
    advertising->setName(BLE_DEVICE_NAME);

    // Make the device more discoverable
    advertising->setScanResponse(true);

    // Start advertising
    advertising->start();

    // Initialize the advertising refresh timestamp
    lastAdvertisingRefresh = millis();

    if (DEBUG_ENABLED) {
      DEBUG_SERIAL.println("BLE initialized, advertising started with enhanced parameters");
    }
  }

  // Set callback for incoming messages
  void setMessageCallback(BLEMessageCallback callback) {
    messageCallback = callback;
    characteristic->setCallbacks(new CharacteristicCallbacks(callback));
  }

  // Send a message via BLE
  bool sendMessage(const String& message) {
    if (deviceConnected) {
      characteristic->setValue(message.c_str());
      characteristic->notify();
      lastNotifyTime = millis();

      if (DEBUG_ENABLED) {
        DEBUG_SERIAL.println("BLE message sent: " + message);
      }
      return true;
    }

    if (DEBUG_ENABLED && message.length() > 0) {
      DEBUG_SERIAL.println("BLE message not sent (no connection): " + message);
    }
    return false;
  }

  // Update BLE state - call this in the main loop
  void update() {
    // Handle connection state changes
    if (deviceConnected && !oldDeviceConnected) {
      oldDeviceConnected = deviceConnected;
      if (DEBUG_ENABLED) {
        DEBUG_SERIAL.println("BLE connection established");
      }
    }

    if (!deviceConnected && oldDeviceConnected) {
      delay(500); // Give the Bluetooth stack time to get ready

      // Restart advertising with enhanced parameters
      BLEAdvertising* advertising = server->getAdvertising();
      advertising->setMinPreferred(0x06);
      advertising->setMaxPreferred(0x12);
      advertising->addServiceUUID(SERVICE_UUID);

      // Use the configured advertising interval
      uint16_t advIntervalMin = (BLE_ADVERTISING_INTERVAL * 8) / 5; // Convert ms to 0.625ms units
      uint16_t advIntervalMax = advIntervalMin * 2;
      advertising->setMinInterval(advIntervalMin);
      advertising->setMaxInterval(advIntervalMax);

      // Set advertising power to maximum for better range
      BLEDevice::setPower(ESP_PWR_LVL_P9);

      // Add device name to the advertising data
      advertising->setName(BLE_DEVICE_NAME);

      // Make the device more discoverable
      advertising->setScanResponse(true);

      advertising->start();

      lastAdvertisingRefresh = millis(); // Update refresh timestamp

      oldDeviceConnected = deviceConnected;
      if (DEBUG_ENABLED) {
        DEBUG_SERIAL.println("BLE advertising restarted with enhanced parameters");
      }
    }

    // Periodically refresh advertising to ensure continuous broadcasting
    unsigned long currentTime = millis();
    if (!deviceConnected && (currentTime - lastAdvertisingRefresh >= BLE_ADVERTISING_REFRESH)) {
      // Refresh advertising with enhanced parameters
      BLEAdvertising* advertising = server->getAdvertising();

      // Stop advertising first to ensure clean restart
      advertising->stop();
      delay(100); // Short delay to allow the stack to process

      // Configure advertising with enhanced parameters
      advertising->setMinPreferred(0x06);
      advertising->setMaxPreferred(0x12);
      advertising->addServiceUUID(SERVICE_UUID);

      // Use the configured advertising interval
      uint16_t advIntervalMin = (BLE_ADVERTISING_INTERVAL * 8) / 5;
      uint16_t advIntervalMax = advIntervalMin * 2;
      advertising->setMinInterval(advIntervalMin);
      advertising->setMaxInterval(advIntervalMax);

      // Set advertising power to maximum for better range
      BLEDevice::setPower(ESP_PWR_LVL_P9);

      // Add device name to the advertising data
      advertising->setName(BLE_DEVICE_NAME);

      // Make the device more discoverable
      advertising->setScanResponse(true);

      // Restart advertising
      advertising->start();

      lastAdvertisingRefresh = currentTime;

      if (DEBUG_ENABLED) {
        DEBUG_SERIAL.println("BLE advertising refreshed periodically");
      }
    }
  }

  // Check if a device is connected
  bool isConnected() {
    return deviceConnected;
  }
};

#endif // BLE_HANDLER_H
