/*
 * LoRaWA<PERSON> Handler for Heltec Wireless Tracker (ESP32S3)
 * 
 * This file implements the LoRaWAN functionality for device-to-device
 * communication between wireless trackers.
 */

#ifndef LORAWAN_HANDLER_H
#define LORAWAN_HANDLER_H

#include <Arduino.h>
#include <SPI.h>
#include <RadioLib.h>
#include "config.h"

// Callback function type for handling incoming LoRaWAN messages
typedef std::function<void(const String&, int)> LoRaWANMessageCallback;

// Global flag for interrupt handling
volatile bool loraInterruptOccurred = false;

// Global interrupt handler function
void IRAM_ATTR loraInterruptHandler() {
  loraInterruptOccurred = true;
}

// Main LoRaWAN handler class
class LoRaWANHandler {
private:
  LoRaWANMessageCallback messageCallback;
  unsigned long lastSendTime;
  uint8_t txBuffer[MAX_MESSAGE_SIZE];
  uint8_t rxBuffer[MAX_MESSAGE_SIZE];
  int lastRssi;
  String lastMessage;
  
  // RadioLib SX1276 module instance
  SX1276 radio = new Module(LORA_CS_PIN, LORA_DIO0_PIN, LORA_RST_PIN, LORA_DIO1_PIN);
  
public:
  LoRaWANHandler() : 
    lastSendTime(0),
    lastRssi(0) {
    memset(txBuffer, 0, MAX_MESSAGE_SIZE);
    memset(rxBuffer, 0, MAX_MESSAGE_SIZE);
  }
  
  // Initialize LoRaWAN
  void init() {
    // Initialize SPI
    SPI.begin();
    
    // Initialize LoRa radio
    int state = radio.begin(LORAWAN_FREQUENCY / 1000000.0,
                           LORAWAN_BANDWIDTH / 1000.0,
                           LORAWAN_SPREADING_FACTOR,
                           LORAWAN_CODING_RATE,
                           LORAWAN_SYNC_WORD,
                           LORAWAN_TX_POWER);
                           
    if (state != RADIOLIB_ERR_NONE) {
      if (DEBUG_ENABLED) {
        DEBUG_SERIAL.print("LoRa initialization failed with error code: ");
        DEBUG_SERIAL.println(state);
      }
      while (true); // Don't proceed if initialization failed
    }
    
    // Set preamble length
    radio.setPreambleLength(LORAWAN_PREAMBLE_LENGTH);
    
    // Set up interrupt for DIO0 pin
    pinMode(LORA_DIO0_PIN, INPUT);
    attachInterrupt(digitalPinToInterrupt(LORA_DIO0_PIN), loraInterruptHandler, RISING);
    
    // Enable receive mode
    radio.startReceive();
    
    if (DEBUG_ENABLED) {
      DEBUG_SERIAL.println("LoRaWAN initialized");
    }
  }
  
  // Process received packet
  void processReceivedPacket() {
    // Read packet
    int packetSize = radio.getPacketLength();
    if (packetSize == 0) {
      return;
    }
    
    // Read packet data
    int state = radio.readData(rxBuffer, packetSize);
    
    if (state == RADIOLIB_ERR_NONE) {
      // Add null terminator
      if (packetSize < MAX_MESSAGE_SIZE) {
        rxBuffer[packetSize] = '\0';
      } else {
        rxBuffer[MAX_MESSAGE_SIZE - 1] = '\0';
      }
      
      // Store message and RSSI
      lastMessage = String((char*)rxBuffer);
      lastRssi = radio.getRSSI();
      
      if (DEBUG_ENABLED) {
        DEBUG_SERIAL.print("LoRaWAN message received: ");
        DEBUG_SERIAL.print(lastMessage);
        DEBUG_SERIAL.print(" (RSSI: ");
        DEBUG_SERIAL.print(lastRssi);
        DEBUG_SERIAL.println(")");
      }
      
      // Call the callback if set
      if (messageCallback) {
        messageCallback(lastMessage, lastRssi);
      }
    } else if (DEBUG_ENABLED) {
      DEBUG_SERIAL.print("LoRaWAN receive error: ");
      DEBUG_SERIAL.println(state);
    }
    
    // Resume receiving
    radio.startReceive();
  }
  
  // Set callback for incoming messages
  void setMessageCallback(LoRaWANMessageCallback callback) {
    messageCallback = callback;
  }
  
  // Send a message via LoRaWAN
  bool sendMessage(const String& message) {
    unsigned long currentTime = millis();
    
    // Check if enough time has passed since the last transmission
    if (currentTime - lastSendTime < LORAWAN_TX_INTERVAL) {
      if (DEBUG_ENABLED) {
        DEBUG_SERIAL.println("LoRaWAN send skipped (rate limit)");
      }
      return false;
    }
    
    // Check message size
    if (message.length() >= MAX_MESSAGE_SIZE) {
      if (DEBUG_ENABLED) {
        DEBUG_SERIAL.println("LoRaWAN message too large");
      }
      return false;
    }
    
    // Copy message to buffer
    memset(txBuffer, 0, MAX_MESSAGE_SIZE);
    message.getBytes(txBuffer, message.length() + 1);
    
    // Send packet
    int state = radio.transmit(txBuffer, message.length());
    bool success = (state == RADIOLIB_ERR_NONE);
    
    if (success) {
      lastSendTime = currentTime;
      
      if (DEBUG_ENABLED) {
        DEBUG_SERIAL.println("LoRaWAN message sent: " + message);
      }
    } else if (DEBUG_ENABLED) {
      DEBUG_SERIAL.print("LoRaWAN send failed with error code: ");
      DEBUG_SERIAL.println(state);
    }
    
    // Switch back to receive mode
    radio.startReceive();
    
    return success;
  }
  
  // Update LoRaWAN state - call this in the main loop
  void update() {
    // Process any received messages
    if (loraInterruptOccurred) {
      processReceivedPacket();
      loraInterruptOccurred = false;
    }
  }
  
  // Get the last received RSSI
  int getLastRssi() {
    return lastRssi;
  }
};

#endif // LORAWAN_HANDLER_H