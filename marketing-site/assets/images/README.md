# nxtAcre Marketing Website Images

This directory should contain the following images for the marketing website:

## Logo Images
- `logo.svg` - Main logo for the navbar (height: 40px)
- `logo.svg` - White version of the logo for the footer (height: 40px)
- `favicon.ico` - Favicon for the website

## Hero Section
- `hero-image.jpg` - Main hero image showing a farm management dashboard (recommended size: 800x600px)

## Testimonials
- `testimonial-1.jpg` - Photo of <PERSON>, wheat farmer (60x60px, rounded)
- `testimonial-2.jpg` - Photo of <PERSON>, dairy farmer (60x60px, rounded)
- `testimonial-3.jpg` - Photo of <PERSON>, vineyard owner (60x60px, rounded)

## Features Page
- `field-management.jpg` - Image showing field mapping feature (800x500px)
- `equipment-tracking.jpg` - Image showing equipment tracking feature (800x500px)
- `inventory-management.jpg` - Image showing inventory management feature (800x500px)
- `financial-tools.jpg` - Image showing financial management tools (800x500px)
- `weather-integration.jpg` - Image showing weather integration feature (800x500px)
- `reporting-analytics.jpg` - Image showing reporting and analytics feature (800x500px)

## About Page
- `team-photo.jpg` - Team photo (1200x800px)
- `office-photo.jpg` - Office photo (1200x800px)
- `farm-photo.jpg` - Photo of a farm using nxtAcre (1200x800px)

## Placeholder Images
Until you have actual images, you can use placeholder services like:
- https://dummyimage.com/
- https://picsum.photos/

Example usage:
```html
<!-- Placeholder for hero image -->
<img src="https://picsum.photos/800/600" alt="Farm Management Dashboard" class="img-fluid rounded shadow-lg">

<!-- Placeholder for testimonial image -->
<img src="https://picsum.photos/60/60" alt="Farmer" class="rounded-circle me-3" width="60" height="60">
```

## Image Optimization
Remember to optimize all images for web use to ensure fast loading times:
1. Use appropriate image formats (JPEG for photos, PNG for logos with transparency)
2. Compress images to reduce file size
3. Specify width and height attributes in HTML
4. Use responsive image techniques when appropriate
