import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ActivityIndicator, Dimensions, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Camera } from 'expo-camera';
import * as Location from 'expo-location';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../hooks/useAuth';
import { useFarm } from '../../hooks/useFarm';
import { StackNavigationProp } from '@react-navigation/stack';
import { MainStackParamList } from '../../navigation/types';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import Animated, { useSharedValue, useAnimatedStyle, withTiming } from 'react-native-reanimated';

type AugmentedRealityScreenNavigationProp = StackNavigationProp<MainStackParamList, 'AugmentedReality'>;

type AROverlay = {
  id: string;
  type: 'crop' | 'soil' | 'weather' | 'equipment';
  title: string;
  value: string;
  unit?: string;
  color: string;
  position: { x: number; y: number };
};

const { width, height } = Dimensions.get('window');

const AugmentedRealityScreen = () => {
  const navigation = useNavigation<AugmentedRealityScreenNavigationProp>();
  const { user } = useAuth();
  const { currentFarm } = useFarm();
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeMode, setActiveMode] = useState<'crop' | 'soil' | 'weather' | 'equipment'>('crop');
  const [overlays, setOverlays] = useState<AROverlay[]>([]);
  const [isARActive, setIsARActive] = useState(false);
  const cameraRef = useRef<Camera>(null);
  
  // Animation for overlay appearance
  const overlayOpacity = useSharedValue(0);
  
  const overlayAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: overlayOpacity.value,
    };
  });

  useEffect(() => {
    const requestPermissions = async () => {
      try {
        const { status: cameraStatus } = await Camera.requestCameraPermissionsAsync();
        const { status: locationStatus } = await Location.requestForegroundPermissionsAsync();
        
        setHasPermission(cameraStatus === 'granted' && locationStatus === 'granted');
      } catch (error) {
        console.error('Error requesting permissions:', error);
        setHasPermission(false);
      } finally {
        setLoading(false);
      }
    };

    requestPermissions();
  }, []);

  useEffect(() => {
    if (isARActive) {
      generateMockOverlays();
      overlayOpacity.value = withTiming(1, { duration: 500 });
    } else {
      overlayOpacity.value = withTiming(0, { duration: 300 });
    }
  }, [isARActive, activeMode]);

  const generateMockOverlays = () => {
    // Clear existing overlays
    setOverlays([]);
    
    // Generate new random overlays based on active mode
    const newOverlays: AROverlay[] = [];
    
    // Number of overlays to generate
    const count = Math.floor(Math.random() * 3) + 2;
    
    for (let i = 0; i < count; i++) {
      const overlay: AROverlay = {
        id: `${activeMode}-${i}`,
        type: activeMode,
        title: '',
        value: '',
        color: '',
        position: {
          x: Math.random() * (width - 150) + 75,
          y: Math.random() * (height / 2) + (height / 4),
        },
      };
      
      // Set mode-specific data
      switch (activeMode) {
        case 'crop':
          overlay.title = ['Crop Health', 'Growth Stage', 'Yield Estimate'][Math.floor(Math.random() * 3)];
          if (overlay.title === 'Crop Health') {
            const healthValue = Math.floor(Math.random() * 30) + 70;
            overlay.value = `${healthValue}%`;
            overlay.color = healthValue > 85 ? '#10b981' : healthValue > 75 ? '#f59e0b' : '#ef4444';
          } else if (overlay.title === 'Growth Stage') {
            overlay.value = ['Seedling', 'Vegetative', 'Flowering', 'Ripening'][Math.floor(Math.random() * 4)];
            overlay.color = '#3b82f6';
          } else {
            overlay.value = `${Math.floor(Math.random() * 50) + 100}`;
            overlay.unit = 'bu/acre';
            overlay.color = '#8b5cf6';
          }
          break;
          
        case 'soil':
          overlay.title = ['Moisture', 'Temperature', 'Nitrogen', 'pH'][Math.floor(Math.random() * 4)];
          if (overlay.title === 'Moisture') {
            overlay.value = `${Math.floor(Math.random() * 30) + 20}%`;
            overlay.color = '#0ea5e9';
          } else if (overlay.title === 'Temperature') {
            overlay.value = `${Math.floor(Math.random() * 15) + 15}`;
            overlay.unit = '°C';
            overlay.color = '#f97316';
          } else if (overlay.title === 'Nitrogen') {
            overlay.value = `${Math.floor(Math.random() * 50) + 50}`;
            overlay.unit = 'ppm';
            overlay.color = '#84cc16';
          } else {
            overlay.value = `${(Math.random() * 2 + 5).toFixed(1)}`;
            overlay.color = '#8b5cf6';
          }
          break;
          
        case 'weather':
          overlay.title = ['Temperature', 'Humidity', 'Wind', 'Precipitation'][Math.floor(Math.random() * 4)];
          if (overlay.title === 'Temperature') {
            overlay.value = `${Math.floor(Math.random() * 25) + 10}`;
            overlay.unit = '°C';
            overlay.color = '#f97316';
          } else if (overlay.title === 'Humidity') {
            overlay.value = `${Math.floor(Math.random() * 40) + 40}%`;
            overlay.color = '#0ea5e9';
          } else if (overlay.title === 'Wind') {
            overlay.value = `${Math.floor(Math.random() * 20) + 5}`;
            overlay.unit = 'km/h';
            overlay.color = '#64748b';
          } else {
            overlay.value = `${Math.floor(Math.random() * 70)}%`;
            overlay.color = '#0ea5e9';
          }
          break;
          
        case 'equipment':
          overlay.title = ['Tractor', 'Sprayer', 'Harvester', 'Irrigation'][Math.floor(Math.random() * 4)];
          if (overlay.title === 'Tractor') {
            overlay.value = `${Math.floor(Math.random() * 500) + 500}m`;
            overlay.color = '#f97316';
          } else if (overlay.title === 'Sprayer') {
            overlay.value = 'Active';
            overlay.color = '#10b981';
          } else if (overlay.title === 'Harvester') {
            overlay.value = 'Idle';
            overlay.color = '#f59e0b';
          } else {
            overlay.value = `${Math.floor(Math.random() * 80) + 20}%`;
            overlay.color = '#0ea5e9';
          }
          break;
      }
      
      newOverlays.push(overlay);
    }
    
    setOverlays(newOverlays);
  };

  const toggleAR = () => {
    setIsARActive(!isARActive);
  };

  const changeMode = (mode: 'crop' | 'soil' | 'weather' | 'equipment') => {
    setActiveMode(mode);
    if (isARActive) {
      // Briefly hide overlays then show new ones
      overlayOpacity.value = withTiming(0, { duration: 200 }, () => {
        generateMockOverlays();
        overlayOpacity.value = withTiming(1, { duration: 300 });
      });
    }
  };

  const takePicture = async () => {
    if (cameraRef.current) {
      try {
        const photo = await cameraRef.current.takePictureAsync();
        Alert.alert(
          'Photo Captured',
          'AR snapshot saved to your gallery.',
          [{ text: 'OK' }]
        );
      } catch (error) {
        console.error('Error taking picture:', error);
        Alert.alert('Error', 'Failed to take picture. Please try again.');
      }
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4f46e5" />
        <Text style={styles.loadingText}>Loading AR capabilities...</Text>
      </SafeAreaView>
    );
  }

  if (hasPermission === false) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="dark" />
        
        <View style={styles.header}>
          <Text style={styles.title}>Augmented Reality</Text>
        </View>
        
        <View style={styles.permissionContainer}>
          <Ionicons name="warning" size={48} color="#f59e0b" />
          <Text style={styles.permissionTitle}>Camera and Location Access Required</Text>
          <Text style={styles.permissionText}>
            To use the augmented reality features, please grant camera and location permissions in your device settings.
          </Text>
          <TouchableOpacity
            style={styles.permissionButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.permissionButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      
      {/* Camera View */}
      <Camera
        ref={cameraRef}
        style={styles.camera}
        type={Camera.Constants.Type.back}
      >
        {/* AR Overlays */}
        {isARActive && (
          <Animated.View style={[styles.overlayContainer, overlayAnimatedStyle]}>
            {overlays.map((overlay) => (
              <View
                key={overlay.id}
                style={[
                  styles.dataOverlay,
                  {
                    left: overlay.position.x,
                    top: overlay.position.y,
                    borderColor: overlay.color,
                  },
                ]}
              >
                <Text style={styles.overlayTitle}>{overlay.title}</Text>
                <Text style={[styles.overlayValue, { color: overlay.color }]}>
                  {overlay.value}{overlay.unit}
                </Text>
              </View>
            ))}
          </Animated.View>
        )}
        
        {/* Top Controls */}
        <SafeAreaView style={styles.controls}>
          <View style={styles.topControls}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="chevron-back" size={24} color="#ffffff" />
            </TouchableOpacity>
            
            <View style={styles.modeSelector}>
              <TouchableOpacity
                style={[
                  styles.modeButton,
                  activeMode === 'crop' && styles.modeButtonActive,
                ]}
                onPress={() => changeMode('crop')}
              >
                <Ionicons name="leaf-outline" size={20} color={activeMode === 'crop' ? '#ffffff' : '#e5e7eb'} />
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.modeButton,
                  activeMode === 'soil' && styles.modeButtonActive,
                ]}
                onPress={() => changeMode('soil')}
              >
                <Ionicons name="water-outline" size={20} color={activeMode === 'soil' ? '#ffffff' : '#e5e7eb'} />
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.modeButton,
                  activeMode === 'weather' && styles.modeButtonActive,
                ]}
                onPress={() => changeMode('weather')}
              >
                <Ionicons name="partly-sunny-outline" size={20} color={activeMode === 'weather' ? '#ffffff' : '#e5e7eb'} />
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.modeButton,
                  activeMode === 'equipment' && styles.modeButtonActive,
                ]}
                onPress={() => changeMode('equipment')}
              >
                <Ionicons name="construct-outline" size={20} color={activeMode === 'equipment' ? '#ffffff' : '#e5e7eb'} />
              </TouchableOpacity>
            </View>
          </View>
          
          {/* Bottom Controls */}
          <View style={styles.bottomControls}>
            <TouchableOpacity
              style={styles.arButton}
              onPress={toggleAR}
            >
              <Ionicons 
                name={isARActive ? "eye" : "eye-outline"} 
                size={24} 
                color={isARActive ? "#4f46e5" : "#ffffff"} 
              />
              <Text style={[styles.arButtonText, isARActive && styles.arButtonTextActive]}>
                {isARActive ? 'AR Active' : 'Start AR'}
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.captureButton}
              onPress={takePicture}
            >
              <View style={styles.captureButtonInner} />
            </TouchableOpacity>
            
            <View style={styles.placeholderButton} />
          </View>
        </SafeAreaView>
        
        {/* Mode Info */}
        {isARActive && (
          <View style={styles.modeInfo}>
            <Text style={styles.modeTitle}>
              {activeMode === 'crop' && 'Crop Health'}
              {activeMode === 'soil' && 'Soil Data'}
              {activeMode === 'weather' && 'Weather Info'}
              {activeMode === 'equipment' && 'Equipment Status'}
            </Text>
            <Text style={styles.modeDescription}>
              {activeMode === 'crop' && 'Visualizing crop health, growth stage, and yield estimates'}
              {activeMode === 'soil' && 'Displaying soil moisture, temperature, and nutrient levels'}
              {activeMode === 'weather' && 'Showing current weather conditions and forecasts'}
              {activeMode === 'equipment' && 'Tracking nearby equipment and their status'}
            </Text>
          </View>
        )}
        
        {/* AR Inactive Overlay */}
        {!isARActive && (
          <View style={styles.inactiveOverlay}>
            <View style={styles.inactiveContent}>
              <Ionicons name="scan-outline" size={48} color="#ffffff" />
              <Text style={styles.inactiveTitle}>AR View Inactive</Text>
              <Text style={styles.inactiveText}>
                Tap the "Start AR" button to visualize field data in augmented reality.
              </Text>
            </View>
          </View>
        )}
      </Camera>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000000',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#ffffff',
  },
  header: {
    padding: 16,
    backgroundColor: '#000000',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  permissionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ffffff',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  permissionText: {
    fontSize: 16,
    color: '#d1d5db',
    textAlign: 'center',
    marginBottom: 24,
  },
  permissionButton: {
    backgroundColor: '#4f46e5',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  camera: {
    flex: 1,
  },
  controls: {
    flex: 1,
    justifyContent: 'space-between',
  },
  topControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modeSelector: {
    flexDirection: 'row',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    padding: 4,
  },
  modeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 2,
  },
  modeButtonActive: {
    backgroundColor: '#4f46e5',
  },
  bottomControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    paddingBottom: 32,
  },
  arButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  arButtonText: {
    color: '#ffffff',
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  arButtonTextActive: {
    color: '#4f46e5',
  },
  captureButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButtonInner: {
    width: 54,
    height: 54,
    borderRadius: 27,
    backgroundColor: '#ffffff',
  },
  placeholderButton: {
    width: 80,
  },
  overlayContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dataOverlay: {
    position: 'absolute',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 8,
    padding: 8,
    borderWidth: 2,
    minWidth: 120,
    alignItems: 'center',
  },
  overlayTitle: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  overlayValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modeInfo: {
    position: 'absolute',
    bottom: 100,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 12,
  },
  modeTitle: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  modeDescription: {
    color: '#d1d5db',
    fontSize: 12,
  },
  inactiveOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  inactiveContent: {
    alignItems: 'center',
    padding: 24,
  },
  inactiveTitle: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  inactiveText: {
    color: '#d1d5db',
    fontSize: 16,
    textAlign: 'center',
    maxWidth: 300,
  },
});

export default AugmentedRealityScreen;