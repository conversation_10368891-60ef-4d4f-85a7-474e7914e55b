import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, Alert, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as FileSystem from 'expo-file-system';
import * as Location from 'expo-location';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../hooks/useAuth';
import { useFarm } from '../../hooks/useFarm';
import { StackNavigationProp } from '@react-navigation/stack';
import { MainStackParamList } from '../../navigation/types';
import { Field } from '../../types/field';
import { getFields } from '../../services/fieldService';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';

type OfflineFieldMappingScreenNavigationProp = StackNavigationProp<MainStackParamList, 'OfflineFieldMapping'>;

type OfflineMap = {
  id: string;
  name: string;
  size: string;
  date: string;
  fieldId: string;
};

const OfflineFieldMappingScreen = () => {
  const navigation = useNavigation<OfflineFieldMappingScreenNavigationProp>();
  const { user } = useAuth();
  const { currentFarm } = useFarm();
  const [fields, setFields] = useState<Field[]>([]);
  const [offlineMaps, setOfflineMaps] = useState<OfflineMap[]>([]);
  const [loading, setLoading] = useState(true);
  const [downloading, setDownloading] = useState<string | null>(null);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [locationPermission, setLocationPermission] = useState<boolean | null>(null);

  useEffect(() => {
    const loadFields = async () => {
      try {
        if (currentFarm?.id) {
          const fetchedFields = await getFields(currentFarm.id);
          setFields(fetchedFields);
        }
      } catch (error) {
        console.error('Error loading fields:', error);
        Alert.alert('Error', 'Failed to load fields. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    const checkLocationPermission = async () => {
      const { status } = await Location.requestForegroundPermissionsAsync();
      setLocationPermission(status === 'granted');
    };

    const loadOfflineMaps = async () => {
      try {
        const mapsDir = `${FileSystem.documentDirectory}offline_maps/`;
        const dirInfo = await FileSystem.getInfoAsync(mapsDir);

        if (!dirInfo.exists) {
          await FileSystem.makeDirectoryAsync(mapsDir, { intermediates: true });
          setOfflineMaps([]);
          return;
        }

        const files = await FileSystem.readDirectoryAsync(mapsDir);
        const mapPromises = files.map(async (file) => {
          if (file.endsWith('.json')) {
            const fileInfo = await FileSystem.getInfoAsync(`${mapsDir}${file}`);
            const fileContent = await FileSystem.readAsStringAsync(`${mapsDir}${file}`);
            const mapData = JSON.parse(fileContent);
            return {
              id: file.replace('.json', ''),
              name: mapData.name,
              size: `${(fileInfo.size / 1024 / 1024).toFixed(1)} MB`,
              date: new Date(fileInfo.modificationTime * 1000).toLocaleDateString(),
              fieldId: mapData.fieldId
            };
          }
          return null;
        });

        const maps = (await Promise.all(mapPromises)).filter(map => map !== null) as OfflineMap[];
        setOfflineMaps(maps);
      } catch (error) {
        console.error('Error loading offline maps:', error);
        Alert.alert('Error', 'Failed to load offline maps. Please try again later.');
      }
    };

    loadFields();
    checkLocationPermission();
    loadOfflineMaps();
  }, [currentFarm?.id]);

  const handleDownload = async (field: Field) => {
    if (!field.id) {
      Alert.alert('Error', 'Field ID is missing');
      return;
    }

    setDownloading(field.id);
    setDownloadProgress(0);

    try {
      // Create directory if it doesn't exist
      const mapsDir = `${FileSystem.documentDirectory}offline_maps/`;
      const dirInfo = await FileSystem.getInfoAsync(mapsDir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(mapsDir, { intermediates: true });
      }

      // Simulate download with progress
      for (let i = 0; i <= 10; i++) {
        setDownloadProgress(i * 10);
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      // Save field data to local storage
      const mapData = {
        fieldId: field.id,
        name: field.name,
        boundaries: field.boundaries,
        area: typeof field.area === 'string' ? parseFloat(field.area) : field.area,
        createdAt: new Date().toISOString()
      };

      const filePath = `${mapsDir}${field.id}.json`;
      await FileSystem.writeAsStringAsync(filePath, JSON.stringify(mapData));

      // Add to offline maps list
      const fileInfo = await FileSystem.getInfoAsync(filePath);
      const newMap: OfflineMap = {
        id: field.id,
        name: field.name,
        size: `${(fileInfo.size / 1024 / 1024).toFixed(1)} MB`,
        date: new Date().toLocaleDateString(),
        fieldId: field.id
      };

      setOfflineMaps(prev => [...prev, newMap]);
      Alert.alert('Success', `${field.name} has been downloaded for offline use.`);
    } catch (error) {
      console.error('Error downloading field:', error);
      Alert.alert('Error', 'Failed to download field. Please try again later.');
    } finally {
      setDownloading(null);
      setDownloadProgress(0);
    }
  };

  const handleDelete = async (map: OfflineMap) => {
    try {
      const filePath = `${FileSystem.documentDirectory}offline_maps/${map.id}.json`;
      await FileSystem.deleteAsync(filePath);
      setOfflineMaps(prev => prev.filter(m => m.id !== map.id));
      Alert.alert('Success', `${map.name} has been deleted.`);
    } catch (error) {
      console.error('Error deleting map:', error);
      Alert.alert('Error', 'Failed to delete map. Please try again later.');
    }
  };

  const confirmDelete = (map: OfflineMap) => {
    Alert.alert(
      'Delete Offline Map',
      `Are you sure you want to delete the offline map for ${map.name}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: () => handleDelete(map) }
      ]
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4f46e5" />
        <Text style={styles.loadingText}>Loading fields...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />

      <View style={styles.header}>
        <Text style={styles.title}>Offline Field Mapping</Text>
        <Text style={styles.subtitle}>
          Download field maps for offline use. Create and edit field boundaries even without an internet connection.
        </Text>
      </View>

      {!locationPermission && (
        <View style={styles.permissionWarning}>
          <Ionicons name="warning" size={24} color="#f59e0b" />
          <Text style={styles.permissionText}>
            Location permission is required for field mapping. Please enable location services in your device settings.
          </Text>
        </View>
      )}

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Available Fields</Text>
        <Text style={styles.sectionSubtitle}>Select fields to download for offline use</Text>

        {fields.length === 0 ? (
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>No fields available. Create fields in the web application first.</Text>
          </View>
        ) : (
          <FlatList
            data={fields}
            keyExtractor={(item) => item.id || Math.random().toString()}
            renderItem={({ item }) => {
              const isDownloaded = offlineMaps.some(map => map.fieldId === item.id);
              const isDownloading = downloading === item.id;

              return (
                <View style={styles.fieldCard}>
                  <View style={styles.fieldInfo}>
                    <Text style={styles.fieldName}>{item.name}</Text>
                    <Text style={styles.fieldArea}>{typeof item.area === 'string' ? parseFloat(item.area).toFixed(2) : item.area?.toFixed(2)} acres</Text>
                  </View>

                  {isDownloading ? (
                    <View style={styles.downloadProgress}>
                      <ActivityIndicator size="small" color="#4f46e5" />
                      <Text style={styles.downloadText}>{downloadProgress}%</Text>
                    </View>
                  ) : isDownloaded ? (
                    <View style={styles.downloadedBadge}>
                      <Ionicons name="checkmark-circle" size={20} color="#10b981" />
                      <Text style={styles.downloadedText}>Downloaded</Text>
                    </View>
                  ) : (
                    <TouchableOpacity
                      style={styles.downloadButton}
                      onPress={() => handleDownload(item)}
                      disabled={downloading !== null}
                    >
                      <Ionicons name="cloud-download-outline" size={20} color="#ffffff" />
                      <Text style={styles.buttonText}>Download</Text>
                    </TouchableOpacity>
                  )}
                </View>
              );
            }}
            contentContainerStyle={styles.fieldsList}
          />
        )}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Downloaded Maps</Text>
        <Text style={styles.sectionSubtitle}>Maps available for offline use</Text>

        {offlineMaps.length === 0 ? (
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>No maps downloaded yet. Download maps above to use them offline.</Text>
          </View>
        ) : (
          <FlatList
            data={offlineMaps}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <View style={styles.mapCard}>
                <View style={styles.mapInfo}>
                  <Ionicons name="map-outline" size={24} color="#4f46e5" style={styles.mapIcon} />
                  <View>
                    <Text style={styles.mapName}>{item.name}</Text>
                    <Text style={styles.mapDetails}>Size: {item.size} • Downloaded: {item.date}</Text>
                  </View>
                </View>
                <TouchableOpacity
                  style={styles.deleteButton}
                  onPress={() => confirmDelete(item)}
                >
                  <Ionicons name="trash-outline" size={20} color="#ef4444" />
                </TouchableOpacity>
              </View>
            )}
            contentContainerStyle={styles.mapsList}
          />
        )}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Offline Features</Text>
        <Text style={styles.sectionSubtitle}>Available when using maps offline</Text>

        <View style={styles.featuresList}>
          <View style={styles.featureItem}>
            <View style={styles.featureIconContainer}>
              <Ionicons name="create-outline" size={24} color="#4f46e5" />
            </View>
            <View style={styles.featureContent}>
              <Text style={styles.featureName}>Field Boundary Creation</Text>
              <Text style={styles.featureDescription}>Create and edit field boundaries while in the field, even without an internet connection.</Text>
            </View>
          </View>

          <View style={styles.featureItem}>
            <View style={styles.featureIconContainer}>
              <Ionicons name="location-outline" size={24} color="#4f46e5" />
            </View>
            <View style={styles.featureContent}>
              <Text style={styles.featureName}>GPS Tracking</Text>
              <Text style={styles.featureDescription}>Track your location in the field and record GPS coordinates without an internet connection.</Text>
            </View>
          </View>

          <View style={styles.featureItem}>
            <View style={styles.featureIconContainer}>
              <Ionicons name="save-outline" size={24} color="#4f46e5" />
            </View>
            <View style={styles.featureContent}>
              <Text style={styles.featureName}>Data Collection</Text>
              <Text style={styles.featureDescription}>Collect field data such as soil samples, crop observations, and notes while offline.</Text>
            </View>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#4b5563',
  },
  header: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
  },
  subtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
  },
  permissionWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fef3c7',
    padding: 12,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 8,
  },
  permissionText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: '#92400e',
  },
  section: {
    marginTop: 16,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
    marginBottom: 12,
  },
  emptyState: {
    padding: 24,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    marginBottom: 16,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
  },
  fieldsList: {
    paddingBottom: 8,
  },
  fieldCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  fieldInfo: {
    flex: 1,
  },
  fieldName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  fieldArea: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  downloadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4f46e5',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  downloadProgress: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  downloadText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#4f46e5',
  },
  downloadedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ecfdf5',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
  },
  downloadedText: {
    color: '#10b981',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  mapsList: {
    paddingBottom: 8,
  },
  mapCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  mapInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  mapIcon: {
    marginRight: 12,
  },
  mapName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  mapDetails: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  deleteButton: {
    padding: 8,
  },
  featuresList: {
    marginTop: 8,
  },
  featureItem: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  featureIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#eff6ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  featureContent: {
    flex: 1,
  },
  featureName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  featureDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
  },
});

export default OfflineFieldMappingScreen;
