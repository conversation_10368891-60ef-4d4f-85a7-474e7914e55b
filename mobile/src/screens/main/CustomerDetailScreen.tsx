import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MainStackParamList } from '@/navigation/MainNavigator';
import customerService, { CustomerWithInvoices } from '@/services/customerService';

type CustomerDetailScreenProps = NativeStackScreenProps<MainStackParamList, 'CustomerDetail'>;

const CustomerDetailScreen: React.FC<CustomerDetailScreenProps> = ({ route, navigation }) => {
  const { customerId } = route.params;
  const [customer, setCustomer] = useState<CustomerWithInvoices | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    const fetchCustomer = async () => {
      setLoading(true);
      try {
        const customerData = await customerService.getCustomerById(customerId);
        if (customerData) {
          setCustomer(customerData);
        } else {
          // If API call fails, show an error
          Alert.alert('Error', 'Failed to load customer data');
        }
      } catch (error) {
        console.error('Error fetching customer:', error);
        Alert.alert('Error', 'An error occurred while loading customer data');
      } finally {
        setLoading(false);
      }
    };

    fetchCustomer();
  }, [customerId]);

  const handleEditCustomer = () => {
    // In a real app, this would navigate to an edit screen
    Alert.alert('Edit Customer', 'Customer editing functionality would be implemented here.');
  };

  const handleCreateInvoice = () => {
    // In a real app, this would navigate to an invoice creation screen
    Alert.alert('Create Invoice', 'Invoice creation functionality would be implemented here.');
  };

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString()}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return '#9ca3af';
      case 'SENT':
        return '#3b82f6';
      case 'PAID':
        return '#22c55e';
      case 'OVERDUE':
        return '#ef4444';
      case 'CANCELLED':
        return '#6b7280';
      default:
        return '#9ca3af';
    }
  };

  const renderInvoiceItem = ({ item }: { item: CustomerInvoice }) => (
    <TouchableOpacity
      style={styles.invoiceItem}
      onPress={() => navigation.navigate('InvoiceDetail', { invoiceId: item.id })}
    >
      <View style={styles.invoiceHeader}>
        <Text style={styles.invoiceNumber}>{item.invoiceNumber}</Text>
        <View style={[
          styles.statusBadge,
          { backgroundColor: getStatusColor(item.status) }
        ]}>
          <Text style={styles.statusText}>{item.status}</Text>
        </View>
      </View>
      <View style={styles.invoiceDates}>
        <Text style={styles.dateLabel}>Issued: </Text>
        <Text style={styles.dateValue}>{item.issueDate}</Text>
        <Text style={styles.dateLabel}> • Due: </Text>
        <Text style={styles.dateValue}>{item.dueDate}</Text>
      </View>
      {item.notes && (
        <Text style={styles.invoiceNotes} numberOfLines={1} ellipsizeMode="tail">
          {item.notes}
        </Text>
      )}
      <View style={styles.invoiceAmount}>
        <Text style={styles.amountValue}>{formatCurrency(item.total)}</Text>
      </View>
    </TouchableOpacity>
  );

  const renderOverviewTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.infoCard}>
        <Text style={styles.cardTitle}>Customer Information</Text>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Email:</Text>
          <Text style={styles.infoValue}>{customer?.email || 'N/A'}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Phone:</Text>
          <Text style={styles.infoValue}>{customer?.phone || 'N/A'}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Address:</Text>
          <Text style={styles.infoValue}>{customer?.address || 'N/A'}</Text>
        </View>
      </View>

      <View style={styles.infoCard}>
        <Text style={styles.cardTitle}>Business Summary</Text>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Total Invoices:</Text>
          <Text style={styles.infoValue}>{customer?.invoiceCount || 0}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Total Spent:</Text>
          <Text style={[styles.infoValue, styles.totalSpent]}>
            {formatCurrency(customer?.totalSpent || 0)}
          </Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Last Purchase:</Text>
          <Text style={styles.infoValue}>{customer?.lastPurchaseDate || 'N/A'}</Text>
        </View>
      </View>

      {customer?.notes && (
        <View style={styles.infoCard}>
          <Text style={styles.cardTitle}>Notes</Text>
          <Text style={styles.notesText}>{customer.notes}</Text>
        </View>
      )}
    </View>
  );

  const renderInvoicesTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Customer Invoices</Text>
        <TouchableOpacity onPress={handleCreateInvoice}>
          <Text style={styles.createInvoiceText}>Create Invoice</Text>
        </TouchableOpacity>
      </View>

      {customer?.invoices && customer.invoices.length > 0 ? (
        <FlatList
          data={customer.invoices}
          renderItem={renderInvoiceItem}
          keyExtractor={item => item.id}
          scrollEnabled={false}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons name="document-text" size={60} color="#d1d5db" />
          <Text style={styles.emptyText}>No invoices found</Text>
          <Text style={styles.emptySubtext}>
            Create your first invoice for this customer
          </Text>
        </View>
      )}
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22c55e" />
        <Text style={styles.loadingText}>Loading customer details...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.customerName}>{customer?.name}</Text>
          <Text style={styles.customerType}>Customer</Text>
        </View>
        <TouchableOpacity style={styles.editButton} onPress={handleEditCustomer}>
          <Ionicons name="create-outline" size={24} color="#3b82f6" />
        </TouchableOpacity>
      </View>

      <View style={styles.tabBar}>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'overview' && styles.activeTabButton]}
          onPress={() => setActiveTab('overview')}
        >
          <Text style={[styles.tabButtonText, activeTab === 'overview' && styles.activeTabButtonText]}>
            Overview
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'invoices' && styles.activeTabButton]}
          onPress={() => setActiveTab('invoices')}
        >
          <Text style={[styles.tabButtonText, activeTab === 'invoices' && styles.activeTabButtonText]}>
            Invoices
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView}>
        {activeTab === 'overview' && renderOverviewTab()}
        {activeTab === 'invoices' && renderInvoicesTab()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerContent: {
    flex: 1,
  },
  customerName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  customerType: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  editButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: '#3b82f6',
  },
  tabButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  activeTabButtonText: {
    color: '#3b82f6',
  },
  scrollView: {
    flex: 1,
  },
  tabContent: {
    padding: 15,
  },
  infoCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
  },
  infoValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  totalSpent: {
    color: '#3b82f6',
    fontWeight: 'bold',
  },
  notesText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  createInvoiceText: {
    fontSize: 14,
    color: '#3b82f6',
    fontWeight: '500',
  },
  invoiceItem: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  invoiceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  invoiceNumber: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#fff',
  },
  invoiceDates: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  dateLabel: {
    fontSize: 12,
    color: '#666',
  },
  dateValue: {
    fontSize: 12,
    color: '#333',
    fontWeight: '500',
  },
  invoiceNotes: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
    marginBottom: 5,
  },
  invoiceAmount: {
    alignItems: 'flex-end',
  },
  amountValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#3b82f6',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666',
    marginTop: 10,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 5,
    textAlign: 'center',
  },
});

export default CustomerDetailScreen;
