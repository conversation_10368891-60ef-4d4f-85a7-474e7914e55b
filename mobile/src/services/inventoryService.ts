import api from './apiClient';

// Inventory types
export type InventoryItem = {
  id: string;
  name: string;
  category: string;
  quantity: number;
  unit: string;
  unitPrice?: number;
  supplier?: string;
  reorderPoint?: number;
  location?: string;
  notes?: string;
};

export type InventoryTransaction = {
  id: string;
  inventoryId: string;
  date: string;
  type: 'PURCHASE' | 'USE' | 'ADJUSTMENT' | 'TRANSFER';
  quantity: number;
  notes?: string;
};

export type InventoryWithTransactions = InventoryItem & {
  transactions: InventoryTransaction[];
};

// Inventory service methods
export const inventoryService = {
  // Get all inventory items for a farm
  getInventory: async (farmId: string): Promise<InventoryItem[]> => {
    try {
      if (!farmId) {
        throw new Error('No farm ID provided');
      }

      const response = await api.get<{ inventory: InventoryItem[] }>(`/inventory/farm/${farmId}`);
      return response.data.inventory;
    } catch (error) {
      console.error('Error fetching inventory:', error);
      // Return empty array in case of error
      return [];
    }
  },

  // Get a single inventory item by ID with transactions
  getInventoryById: async (inventoryId: string): Promise<InventoryWithTransactions | null> => {
    try {
      const response = await api.get<{ inventory: InventoryWithTransactions }>(`/inventory/${inventoryId}`);
      return response.data.inventory;
    } catch (error) {
      console.error(`Error fetching inventory ${inventoryId}:`, error);
      // Return null in case of error
      return null;
    }
  },

  // Get transactions for an inventory item
  getInventoryTransactions: async (inventoryId: string): Promise<InventoryTransaction[]> => {
    try {
      const response = await api.get<{ transactions: InventoryTransaction[] }>(`/inventory/${inventoryId}/transactions`);
      return response.data.transactions;
    } catch (error) {
      console.error(`Error fetching transactions for inventory ${inventoryId}:`, error);
      // Return empty array in case of error
      return [];
    }
  },

  // Create a new inventory item
  createInventory: async (inventory: Omit<InventoryItem, 'id'>): Promise<InventoryItem | null> => {
    try {
      const response = await api.post<{ inventory: InventoryItem }>('/inventory', inventory);
      return response.data.inventory;
    } catch (error) {
      console.error('Error creating inventory:', error);
      // Return null in case of error
      return null;
    }
  },

  // Update an existing inventory item
  updateInventory: async (inventoryId: string, inventory: Partial<InventoryItem>): Promise<InventoryItem | null> => {
    try {
      const response = await api.put<{ inventory: InventoryItem }>(`/inventory/${inventoryId}`, inventory);
      return response.data.inventory;
    } catch (error) {
      console.error(`Error updating inventory ${inventoryId}:`, error);
      // Return null in case of error
      return null;
    }
  },

  // Delete an inventory item
  deleteInventory: async (inventoryId: string): Promise<boolean> => {
    try {
      await api.delete(`/inventory/${inventoryId}`);
      return true;
    } catch (error) {
      console.error(`Error deleting inventory ${inventoryId}:`, error);
      // Return false in case of error
      return false;
    }
  },

  // Add a transaction to an inventory item
  addInventoryTransaction: async (
    inventoryId: string, 
    transaction: Omit<InventoryTransaction, 'id' | 'inventoryId'>
  ): Promise<InventoryTransaction | null> => {
    try {
      const response = await api.post<{ transaction: InventoryTransaction }>(
        `/inventory/${inventoryId}/transactions`, 
        transaction
      );
      return response.data.transaction;
    } catch (error) {
      console.error(`Error adding transaction to inventory ${inventoryId}:`, error);
      // Return null in case of error
      return null;
    }
  },

  // Delete a transaction
  deleteInventoryTransaction: async (inventoryId: string, transactionId: string): Promise<boolean> => {
    try {
      await api.delete(`/inventory/${inventoryId}/transactions/${transactionId}`);
      return true;
    } catch (error) {
      console.error(`Error deleting transaction ${transactionId} from inventory ${inventoryId}:`, error);
      // Return false in case of error
      return false;
    }
  }
};

export default inventoryService;