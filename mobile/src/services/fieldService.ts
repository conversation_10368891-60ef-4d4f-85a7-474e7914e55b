import api from './apiClient';

// Field types
export type FieldNote = {
  id: string;
  text: string;
  date: string;
  photoUri?: string;
};

export type Field = {
  id: string;
  name: string;
  acres: number;
  crop?: string;
  status?: string;
  lastActivity?: string;
  soilType?: string;
  plantingDate?: string;
  harvestDate?: string;
  expectedYield?: number;
  yieldUnit?: string;
  notes?: string;
  fieldNotes?: FieldNote[];
  location?: {
    latitude: number;
    longitude: number;
  };
};

export type SoilSample = {
  id: string;
  fieldId: string;
  date: string;
  pH: number;
  organicMatter: number;
  nitrogen: number;
  phosphorus: number;
  potassium: number;
  notes?: string;
};

export type Task = {
  id: string;
  title: string;
  status: string;
  dueDate: string;
  assignedTo?: string;
  fieldId?: string;
};

export type FieldWithDetails = Field & {
  soilSamples: SoilSample[];
  tasks: Task[];
};

export type MapField = {
  id: string;
  name: string;
  coordinates: { latitude: number; longitude: number }[];
  color: string;
};

// Field service methods
export const fieldService = {
  // Get all fields for a farm
  getFields: async (farmId: string): Promise<Field[]> => {
    try {
      if (!farmId) {
        throw new Error('No farm ID provided');
      }

      const response = await api.get<{ fields: Field[] }>(`/fields/farm/${farmId}`);
      return response.data.fields;
    } catch (error) {
      console.error('Error fetching fields:', error);
      // Return empty array in case of error
      return [];
    }
  },

  // Get a single field by ID with soil samples and tasks
  getFieldById: async (fieldId: string): Promise<FieldWithDetails | null> => {
    try {
      const response = await api.get<{ field: FieldWithDetails }>(`/fields/${fieldId}`);
      return response.data.field;
    } catch (error) {
      console.error(`Error fetching field ${fieldId}:`, error);
      // Return null in case of error
      return null;
    }
  },

  // Get soil samples for a field
  getSoilSamples: async (fieldId: string): Promise<SoilSample[]> => {
    try {
      const response = await api.get<{ soilSamples: SoilSample[] }>(`/fields/${fieldId}/soil-samples`);
      return response.data.soilSamples;
    } catch (error) {
      console.error(`Error fetching soil samples for field ${fieldId}:`, error);
      // Return empty array in case of error
      return [];
    }
  },

  // Get tasks for a field
  getFieldTasks: async (fieldId: string): Promise<Task[]> => {
    try {
      const response = await api.get<{ tasks: Task[] }>(`/fields/${fieldId}/tasks`);
      return response.data.tasks;
    } catch (error) {
      console.error(`Error fetching tasks for field ${fieldId}:`, error);
      // Return empty array in case of error
      return [];
    }
  },

  // Get all fields for map display
  getMapFields: async (farmId: string): Promise<MapField[]> => {
    try {
      if (!farmId) {
        throw new Error('No farm ID provided');
      }

      const response = await api.get<{ fields: MapField[] }>(`/fields/farm/${farmId}/map`);
      return response.data.fields;
    } catch (error) {
      console.error('Error fetching map fields:', error);
      // Return empty array in case of error
      return [];
    }
  },

  // Create a new field
  createField: async (field: Omit<Field, 'id'>): Promise<Field | null> => {
    try {
      const response = await api.post<{ field: Field }>('/fields', field);
      return response.data.field;
    } catch (error) {
      console.error('Error creating field:', error);
      // Return null in case of error
      return null;
    }
  },

  // Update an existing field
  updateField: async (fieldId: string, field: Partial<Field>): Promise<Field | null> => {
    try {
      const response = await api.put<{ field: Field }>(`/fields/${fieldId}`, field);
      return response.data.field;
    } catch (error) {
      console.error(`Error updating field ${fieldId}:`, error);
      // Return null in case of error
      return null;
    }
  },

  // Delete a field
  deleteField: async (fieldId: string): Promise<boolean> => {
    try {
      await api.delete(`/fields/${fieldId}`);
      return true;
    } catch (error) {
      console.error(`Error deleting field ${fieldId}:`, error);
      // Return false in case of error
      return false;
    }
  },

  // Add a note to a field
  addFieldNote: async (fieldId: string, note: Omit<FieldNote, 'id' | 'date'>): Promise<FieldNote | null> => {
    try {
      const response = await api.post<{ note: FieldNote }>(`/fields/${fieldId}/notes`, note);
      return response.data.note;
    } catch (error) {
      console.error(`Error adding note to field ${fieldId}:`, error);
      // Return null in case of error
      return null;
    }
  },

  // Delete a field note
  deleteFieldNote: async (fieldId: string, noteId: string): Promise<boolean> => {
    try {
      await api.delete(`/fields/${fieldId}/notes/${noteId}`);
      return true;
    } catch (error) {
      console.error(`Error deleting note ${noteId} from field ${fieldId}:`, error);
      // Return false in case of error
      return false;
    }
  }
};

export default fieldService;