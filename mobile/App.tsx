import React, { useEffect, useRef } from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { NavigationContainer } from '@react-navigation/native';
import AppNavigator from '@/navigation/AppNavigator';
import AuthProvider from '@/store/AuthProvider';
import useSynchronization from '@/hooks/useSynchronization';
import { View, Text } from 'react-native';
import * as Notifications from 'expo-notifications';
import { 
  addNotificationReceivedListener, 
  addNotificationResponseReceivedListener 
} from '@/services/notificationService';
import { useNotificationStore } from '@/store/notificationStore';

export default function App() {
  // Initialize synchronization service
  const { isInitialized, isOnline } = useSynchronization();

  // Notification response listener ref
  const notificationResponseListener = useRef<Notifications.Subscription>();
  const notificationListener = useRef<Notifications.Subscription>();

  // Log initialization status
  useEffect(() => {
    if (isInitialized) {
      console.log('Synchronization service initialized');
    }
  }, [isInitialized]);

  // Initialize notification store and register for push notifications
  useEffect(() => {
    // Initialize push notifications using the notification store
    const initializeNotifications = async () => {
      const { initializePushNotifications } = useNotificationStore.getState();
      await initializePushNotifications();
    };

    initializeNotifications();

    // Set up notification listeners
    notificationListener.current = addNotificationReceivedListener(notification => {
      console.log('Notification received:', notification);
      // Handle received notification based on notification settings
      const { isNotificationsEnabled, notificationSettings } = useNotificationStore.getState();

      if (!isNotificationsEnabled) return;

      // Check notification type and settings
      const data = notification.request.content.data;
      if (data.type === 'task_assignment' && !notificationSettings.taskAssignments) return;
      if (data.type === 'due_date' && !notificationSettings.dueDateAlerts) return;
      if (data.type === 'status_change' && !notificationSettings.statusChanges) return;
      if (data.type === 'system' && !notificationSettings.systemAnnouncements) return;

      // Process notification based on settings
    });

    notificationResponseListener.current = addNotificationResponseReceivedListener(response => {
      console.log('Notification response received:', response);
      const data = response.notification.request.content.data;
      // Handle notification response (e.g., navigate to a specific screen)
    });

    // Clean up listeners on unmount
    return () => {
      if (notificationListener.current) {
        Notifications.removeNotificationSubscription(notificationListener.current);
      }
      if (notificationResponseListener.current) {
        Notifications.removeNotificationSubscription(notificationResponseListener.current);
      }
    };
  }, []);

  // Optional: Show offline indicator
  const OfflineIndicator = () => {
    if (isOnline) return null;

    return (
      <View style={{ 
        backgroundColor: '#f8d7da', 
        padding: 5, 
        alignItems: 'center',
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 999
      }}>
        <Text style={{ color: '#721c24' }}>You are offline. Changes will sync when connection is restored.</Text>
      </View>
    );
  };

  return (
    <SafeAreaProvider>
      <AuthProvider>
        <NavigationContainer>
          <AppNavigator />
          <OfflineIndicator />
          <StatusBar style="auto" />
        </NavigationContainer>
      </AuthProvider>
    </SafeAreaProvider>
  );
}
