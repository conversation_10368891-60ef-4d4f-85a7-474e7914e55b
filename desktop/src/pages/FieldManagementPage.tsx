import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import dbService from '../services/dbService';
import syncService from '../services/syncService';

interface Field {
  id: number;
  farmId: number;
  name: string;
  size: number;
  sizeUnit: 'acres' | 'hectares';
  cropType: string;
  status: 'active' | 'fallow' | 'planned';
  coordinates: {
    lat: number;
    lng: number;
  }[];
  createdAt: string;
  updatedAt: string;
}

interface Farm {
  id: number;
  name: string;
  location: string;
}

const FieldManagementPage: React.FC = () => {
  const { farmId } = useParams<{ farmId: string }>();
  const navigate = useNavigate();
  const [farm, setFarm] = useState<Farm | null>(null);
  const [fields, setFields] = useState<Field[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isAddingField, setIsAddingField] = useState<boolean>(false);
  const [isEditingField, setIsEditingField] = useState<number | null>(null);
  const [formData, setFormData] = useState<Partial<Field>>({
    name: '',
    size: 0,
    sizeUnit: 'acres',
    cropType: '',
    status: 'active',
    coordinates: []
  });

  useEffect(() => {
    const loadData = async () => {
      if (!farmId) {
        setError('Farm ID is required');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        
        // Get farm from local database
        const farmData = await dbService.getInstance().getById<Farm>('farms', parseInt(farmId, 10));
        
        if (!farmData) {
          setError('Farm not found');
          setIsLoading(false);
          return;
        }
        
        setFarm(farmData);
        
        // Get all fields from local database
        const allFields = await dbService.getInstance().getAll<Field>('fields');
        
        // Filter fields for this farm
        const farmFields = allFields.filter(field => field.farmId === parseInt(farmId, 10));
        setFields(farmFields);
      } catch (err) {
        console.error('Failed to load data:', err);
        setError('Failed to load fields');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [farmId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleAddField = () => {
    setIsAddingField(true);
    setIsEditingField(null);
    setFormData({
      name: '',
      size: 0,
      sizeUnit: 'acres',
      cropType: '',
      status: 'active',
      coordinates: []
    });
  };

  const handleEditField = (field: Field) => {
    setIsAddingField(false);
    setIsEditingField(field.id);
    setFormData({
      name: field.name,
      size: field.size,
      sizeUnit: field.sizeUnit,
      cropType: field.cropType,
      status: field.status,
      coordinates: field.coordinates
    });
  };

  const handleCancelForm = () => {
    setIsAddingField(false);
    setIsEditingField(null);
    setFormData({
      name: '',
      size: 0,
      sizeUnit: 'acres',
      cropType: '',
      status: 'active',
      coordinates: []
    });
  };

  const handleSubmitForm = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!farmId) {
      setError('Farm ID is required');
      return;
    }
    
    try {
      setIsLoading(true);
      setError(null);
      
      const now = new Date().toISOString();
      
      if (isAddingField) {
        // Create new field
        const newField: Field = {
          id: Date.now(), // Generate a temporary ID
          farmId: parseInt(farmId, 10),
          name: formData.name || '',
          size: formData.size || 0,
          sizeUnit: formData.sizeUnit || 'acres',
          cropType: formData.cropType || '',
          status: formData.status || 'active',
          coordinates: formData.coordinates || [],
          createdAt: now,
          updatedAt: now
        };
        
        // Save to local database
        await dbService.getInstance().put<Field>('fields', newField);
        
        // Add to sync queue
        syncService.getInstance().addToSyncQueue({
          type: 'create',
          entity: 'fields',
          data: newField
        });
        
        // Update state
        setFields([...fields, newField]);
      } else if (isEditingField !== null) {
        // Find the field being edited
        const fieldToUpdate = fields.find(f => f.id === isEditingField);
        
        if (!fieldToUpdate) {
          throw new Error('Field not found');
        }
        
        // Update field
        const updatedField: Field = {
          ...fieldToUpdate,
          name: formData.name || fieldToUpdate.name,
          size: formData.size || fieldToUpdate.size,
          sizeUnit: formData.sizeUnit || fieldToUpdate.sizeUnit,
          cropType: formData.cropType || fieldToUpdate.cropType,
          status: formData.status || fieldToUpdate.status,
          coordinates: formData.coordinates || fieldToUpdate.coordinates,
          updatedAt: now
        };
        
        // Save to local database
        await dbService.getInstance().put<Field>('fields', updatedField);
        
        // Add to sync queue
        syncService.getInstance().addToSyncQueue({
          type: 'update',
          entity: 'fields',
          id: updatedField.id,
          data: updatedField
        });
        
        // Update state
        setFields(fields.map(f => f.id === updatedField.id ? updatedField : f));
      }
      
      // Reset form
      setIsAddingField(false);
      setIsEditingField(null);
      setFormData({
        name: '',
        size: 0,
        sizeUnit: 'acres',
        cropType: '',
        status: 'active',
        coordinates: []
      });
    } catch (err) {
      console.error('Failed to save field:', err);
      setError('Failed to save field');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteField = async (fieldId: number) => {
    if (!confirm('Are you sure you want to delete this field?')) {
      return;
    }
    
    try {
      setIsLoading(true);
      setError(null);
      
      // Delete from local database
      await dbService.getInstance().delete('fields', fieldId);
      
      // Add to sync queue
      syncService.getInstance().addToSyncQueue({
        type: 'delete',
        entity: 'fields',
        id: fieldId
      });
      
      // Update state
      setFields(fields.filter(f => f.id !== fieldId));
    } catch (err) {
      console.error('Failed to delete field:', err);
      setError('Failed to delete field');
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewMap = (field: Field) => {
    // Navigate to field map view
    navigate(`/farms/${farmId}/fields/${field.id}/map`);
  };

  if (isLoading && !fields.length) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-700 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading fields...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Field Management</h1>
          {farm && <p className="text-gray-600 dark:text-gray-400">Farm: {farm.name}</p>}
        </div>
        <div className="flex space-x-2">
          <button
            onClick={handleAddField}
            className="bg-green-600 hover:bg-green-500 text-white px-4 py-2 rounded"
          >
            Add Field
          </button>
          <button
            onClick={() => navigate(`/farms/${farmId}`)}
            className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"
          >
            Back to Farm
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 p-4 rounded-md mb-6">
          <p>{error}</p>
        </div>
      )}

      {(isAddingField || isEditingField !== null) && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">
            {isAddingField ? 'Add New Field' : 'Edit Field'}
          </h2>
          <form onSubmit={handleSubmitForm}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Field Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name || ''}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                  required
                />
              </div>
              
              <div>
                <label htmlFor="cropType" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Crop Type
                </label>
                <input
                  type="text"
                  id="cropType"
                  name="cropType"
                  value={formData.cropType || ''}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
              
              <div>
                <label htmlFor="size" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Size
                </label>
                <input
                  type="number"
                  id="size"
                  name="size"
                  value={formData.size || ''}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                  required
                />
              </div>
              
              <div>
                <label htmlFor="sizeUnit" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Size Unit
                </label>
                <select
                  id="sizeUnit"
                  name="sizeUnit"
                  value={formData.sizeUnit || 'acres'}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                  required
                >
                  <option value="acres">Acres</option>
                  <option value="hectares">Hectares</option>
                </select>
              </div>
              
              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Status
                </label>
                <select
                  id="status"
                  name="status"
                  value={formData.status || 'active'}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                  required
                >
                  <option value="active">Active</option>
                  <option value="fallow">Fallow</option>
                  <option value="planned">Planned</option>
                </select>
              </div>
            </div>
            
            <div className="mt-6 flex justify-end">
              <button
                type="button"
                onClick={handleCancelForm}
                className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded mr-2"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="bg-green-600 hover:bg-green-500 text-white px-4 py-2 rounded"
                disabled={isLoading}
              >
                {isLoading ? 'Saving...' : 'Save Field'}
              </button>
            </div>
          </form>
        </div>
      )}

      {fields.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 text-center">
          <p className="text-gray-600 dark:text-gray-400">No fields found for this farm.</p>
          <button
            onClick={handleAddField}
            className="mt-4 bg-green-600 hover:bg-green-500 text-white px-4 py-2 rounded"
          >
            Add Your First Field
          </button>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Size
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Crop Type
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {fields.map((field) => (
                <tr key={field.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">{field.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500 dark:text-gray-400">{field.size} {field.sizeUnit}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500 dark:text-gray-400">{field.cropType || 'Not specified'}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                      ${field.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 
                        field.status === 'fallow' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' : 
                        'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'}`}
                    >
                      {field.status.charAt(0).toUpperCase() + field.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => handleViewMap(field)}
                      className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"
                    >
                      View Map
                    </button>
                    <button
                      onClick={() => handleEditField(field)}
                      className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 mr-3"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDeleteField(field.id)}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default FieldManagementPage;