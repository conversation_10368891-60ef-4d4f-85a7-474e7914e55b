import { User } from '../types/user';

/**
 * Authentication service for handling user authentication
 * This service provides methods for login, logout, and session management
 */
class AuthService {
  private static instance: AuthService;
  private currentUser: User | null = null;
  private token: string | null = null;
  private refreshToken: string | null = null;

  private constructor() {
    // Load user data from localStorage if available
    this.loadUserFromStorage();
  }

  /**
   * Get the singleton instance of AuthService
   */
  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Load user data from localStorage
   */
  private loadUserFromStorage(): void {
    try {
      const userData = localStorage.getItem('user');
      const tokenData = localStorage.getItem('token');
      const refreshTokenData = localStorage.getItem('refreshToken');

      if (userData) {
        this.currentUser = JSON.parse(userData);
      }

      if (tokenData) {
        this.token = tokenData;
      }

      if (refreshTokenData) {
        this.refreshToken = refreshTokenData;
      }
    } catch (error) {
      console.error('Failed to load user data from storage:', error);
    }
  }

  /**
   * Save user data to localStorage
   */
  private saveUserToStorage(): void {
    try {
      if (this.currentUser) {
        localStorage.setItem('user', JSON.stringify(this.currentUser));
      } else {
        localStorage.removeItem('user');
      }

      if (this.token) {
        localStorage.setItem('token', this.token);
      } else {
        localStorage.removeItem('token');
      }

      if (this.refreshToken) {
        localStorage.setItem('refreshToken', this.refreshToken);
      } else {
        localStorage.removeItem('refreshToken');
      }
    } catch (error) {
      console.error('Failed to save user data to storage:', error);
    }
  }

  /**
   * Login with username and password
   * @param username User's username or email
   * @param password User's password
   * @param rememberMe Whether to remember the user's session
   * @returns Promise resolving to the logged in user
   */
  public async login(username: string, password: string, rememberMe: boolean = false): Promise<User> {
    try {
      // In a real implementation, this would make an API call to the server
      // For now, we'll simulate a successful login with mock data
      const response = await this.mockLoginRequest(username, password);
      
      this.currentUser = response.user;
      this.token = response.token;
      this.refreshToken = response.refreshToken;
      
      // Save to storage if rememberMe is true
      if (rememberMe) {
        this.saveUserToStorage();
      }
      
      return this.currentUser;
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  }

  /**
   * Logout the current user
   */
  public async logout(): Promise<void> {
    try {
      // In a real implementation, this would make an API call to the server
      // For now, we'll just clear the local data
      this.currentUser = null;
      this.token = null;
      this.refreshToken = null;
      
      // Clear storage
      localStorage.removeItem('user');
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
    } catch (error) {
      console.error('Logout failed:', error);
      throw error;
    }
  }

  /**
   * Get the current user
   * @returns The current user or null if not logged in
   */
  public getCurrentUser(): User | null {
    return this.currentUser;
  }

  /**
   * Check if a user is logged in
   * @returns True if a user is logged in, false otherwise
   */
  public isLoggedIn(): boolean {
    return this.currentUser !== null && this.token !== null;
  }

  /**
   * Get the authentication token
   * @returns The authentication token or null if not logged in
   */
  public getToken(): string | null {
    return this.token;
  }

  /**
   * Refresh the authentication token
   * @returns Promise resolving to the new token
   */
  public async refreshAuthToken(): Promise<string> {
    try {
      if (!this.refreshToken) {
        throw new Error('No refresh token available');
      }
      
      // In a real implementation, this would make an API call to the server
      // For now, we'll simulate a successful token refresh with mock data
      const response = await this.mockRefreshTokenRequest(this.refreshToken);
      
      this.token = response.token;
      this.refreshToken = response.refreshToken;
      
      // Save to storage
      this.saveUserToStorage();
      
      return this.token;
    } catch (error) {
      console.error('Token refresh failed:', error);
      throw error;
    }
  }

  /**
   * Mock login request (for development only)
   * @param username User's username or email
   * @param password User's password
   * @returns Mock response with user, token, and refreshToken
   */
  private async mockLoginRequest(username: string, password: string): Promise<{ user: User; token: string; refreshToken: string }> {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Check credentials (very basic check for demo purposes)
    if (username === 'demo' && password === 'password') {
      return {
        user: {
          id: 1,
          username: 'demo',
          email: '<EMAIL>',
          firstName: 'Demo',
          lastName: 'User',
          role: 'user',
        },
        token: 'mock-auth-token-' + Date.now(),
        refreshToken: 'mock-refresh-token-' + Date.now(),
      };
    } else {
      throw new Error('Invalid credentials');
    }
  }

  /**
   * Mock refresh token request (for development only)
   * @param refreshToken The refresh token
   * @returns Mock response with new token and refreshToken
   */
  private async mockRefreshTokenRequest(refreshToken: string): Promise<{ token: string; refreshToken: string }> {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // In a real implementation, this would validate the refresh token
    // For now, we'll just return new tokens
    return {
      token: 'mock-auth-token-' + Date.now(),
      refreshToken: 'mock-refresh-token-' + Date.now(),
    };
  }
}

export default AuthService.getInstance();