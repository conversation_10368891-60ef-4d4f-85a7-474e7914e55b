export interface Task {
  id: number;
  title: string;
  description: string;
  status: TaskStatus;
  priority: TaskPriority;
  dueDate: Date | null;
  assignedTo: number | null; // User ID
  assignedToName?: string; // User name for display
  farmId: number;
  fieldId?: number; // Optional field association
  equipmentId?: number; // Optional equipment association
  createdAt: Date;
  updatedAt: Date;
  completedAt: Date | null;
}

export enum TaskStatus {
  TODO = 'TODO',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export enum TaskPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

export interface TaskFilter {
  status?: TaskStatus;
  priority?: TaskPriority;
  assignedTo?: number;
  farmId?: number;
  fieldId?: number;
  equipmentId?: number;
  startDate?: Date;
  endDate?: Date;
  searchTerm?: string;
}

export interface CreateTaskRequest {
  title: string;
  description: string;
  status: TaskStatus;
  priority: TaskPriority;
  dueDate: Date | null;
  assignedTo: number | null;
  farmId: number;
  fieldId?: number;
  equipmentId?: number;
}

export interface UpdateTaskRequest {
  id: number;
  title?: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  dueDate?: Date | null;
  assignedTo?: number | null;
  fieldId?: number;
  equipmentId?: number;
  completedAt?: Date | null;
}