import { app, BrowserWindow, ipc<PERSON><PERSON>, <PERSON>u, Tray } from 'electron';
import * as path from 'path';
import * as url from 'url';

// <PERSON>le creating/removing shortcuts on Windows when installing/uninstalling
if (require('electron-squirrel-startup')) {
  app.quit();
}

// Keep a global reference of the window object to prevent garbage collection
let mainWindow: BrowserWindow | null = null;
let tray: Tray | null = null;

const createWindow = (): void => {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      contextIsolation: true,
      nodeIntegration: false,
    },
  });

  // Load the app
  if (process.env.NODE_ENV === 'development') {
    // Load from development server
    mainWindow.loadURL('http://localhost:3000');
    // Open DevTools in development mode
    mainWindow.webContents.openDevTools();
  } else {
    // Load from production build
    mainWindow.loadURL(
      url.format({
        pathname: path.join(__dirname, '../../index.html'),
        protocol: 'file:',
        slashes: true,
      })
    );
  }

  // Handle window close event
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
};

// Create tray icon
const createTray = (): void => {
  tray = new Tray(path.join(__dirname, '../../src/assets/favicon.svg'));
  const contextMenu = Menu.buildFromTemplate([
    { label: 'Open NxtAcre', click: () => mainWindow?.show() },
    { type: 'separator' },
    { label: 'Quit', click: () => app.quit() },
  ]);
  tray.setToolTip('NxtAcre Farm Management');
  tray.setContextMenu(contextMenu);

  tray.on('click', () => {
    if (mainWindow) {
      if (mainWindow.isVisible()) {
        mainWindow.hide();
      } else {
        mainWindow.show();
      }
    }
  });
};

// This method will be called when Electron has finished initialization
app.whenReady().then(() => {
  createWindow();
  createTray();

  app.on('activate', () => {
    // On macOS it's common to re-create a window when the dock icon is clicked
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// Quit when all windows are closed, except on macOS
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Set up IPC handlers
ipcMain.handle('app:get-version', () => {
  return app.getVersion();
});

// Example of an IPC handler for a farm management feature
ipcMain.handle('farms:get-all', async () => {
  // This would typically interact with a database or API
  // For now, return mock data
  return [
    { id: 1, name: 'Green Valley Farm', location: 'California' },
    { id: 2, name: 'Sunrise Acres', location: 'Iowa' },
    { id: 3, name: 'Blue Ridge Orchards', location: 'Virginia' },
  ];
});
